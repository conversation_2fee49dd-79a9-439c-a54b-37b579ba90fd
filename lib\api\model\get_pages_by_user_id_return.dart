//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetPagesByUserIdReturn {
  /// Returns a new [GetPagesByUserIdReturn] instance.
  GetPagesByUserIdReturn({
    this.pages = const [],
    required this.pageCount,
  });

  /// List of pages the user is associated with
  List<SideMenuPageItem> pages;

  /// Total number of pages
  ///
  /// Minimum value: 0
  int pageCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetPagesByUserIdReturn &&
    _deepEquality.equals(other.pages, pages) &&
    other.pageCount == pageCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (pages.hashCode) +
    (pageCount.hashCode);

  @override
  String toString() => 'GetPagesByUserIdReturn[pages=$pages, pageCount=$pageCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'pages'] = this.pages;
      json[r'pageCount'] = this.pageCount;
    return json;
  }

  /// Returns a new [GetPagesByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetPagesByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetPagesByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetPagesByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetPagesByUserIdReturn(
        pages: SideMenuPageItem.listFromJson(json[r'pages']),
        pageCount: mapValueOfType<int>(json, r'pageCount')!,
      );
    }
    return null;
  }

  static List<GetPagesByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetPagesByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetPagesByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetPagesByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetPagesByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetPagesByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetPagesByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetPagesByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetPagesByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetPagesByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'pages',
    'pageCount',
  };
}

