# openapi.model.GetNotificationsReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**notifications** | [**List<NotificationItem>**](NotificationItem.md) | List of user notifications | [default to const []]
**notificationCount** | **int** | Total number of notifications | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



