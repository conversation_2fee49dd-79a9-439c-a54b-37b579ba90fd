import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/app_navigation/models/app_navigation_data.dart';
import 'package:ivent_app/features/app_navigation/widgets/ia_navigation_scaffold.dart';
import 'package:ivent_app/features/home/<USER>/home_page.dart';
import 'package:ivent_app/features/profile/pages/profile_page.dart';
import 'package:ivent_app/features/vibes/pages/vibes_page.dart';
import 'package:ivent_app/features/vibes/pages/camera_page.dart';

class AppNavigationScreen extends StatefulWidget {
  const AppNavigationScreen({Key? key}) : super(key: key);

  @override
  State<AppNavigationScreen> createState() => _AppNavigationScreenState();
}

class _AppNavigationScreenState extends State<AppNavigationScreen> {
  late final AppNavigationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaNavigationScaffold(
        navigationDataList: [
          const AppNavigationData(iconPath: AppAssets.house01, screen: HomePage()),
          const AppNavigationData(iconPath: AppAssets.play, screen: VibesPage(), isNavBarVisible: false),
          const AppNavigationData(iconPath: AppAssets.camera, screen: CameraPage(), isNavBarVisible: false),
          AppNavigationData(iconPath: AppAssets.bell, screen: Container()),
          AppNavigationData(iconPath: AppAssets.user01, screen: ProfilePage(userId: _controller.sessionUser.sessionId)),
        ],
      );
    });
  }
}
