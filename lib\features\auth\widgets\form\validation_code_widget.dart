import 'package:flutter/material.dart';
import 'package:flutter_verification_code/flutter_verification_code.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

/// Custom validation code input widget for phone verification
/// 
/// A specialized widget for entering verification codes sent via SMS.
/// Provides a clean, user-friendly interface for code input with
/// proper validation and state management.
class ValidationCodeWidget extends StatefulWidget {
  /// Callback function called when the code is completely entered
  /// [code] contains the complete validation code
  final ValueChanged<String> onCompleted;
  
  /// Callback function called when editing state changes
  /// [isEditing] indicates whether the user is currently editing
  final ValueChanged<bool> onEditingChanged;
  
  /// Optional callback for when the validation state changes
  /// [isValid] indicates whether the entered code is complete
  final ValueChanged<bool>? onValidationChanged;

  /// Creates a validation code input widget
  /// 
  /// [onCompleted] is called when all code digits are entered
  /// [onEditingChanged] is called when editing state changes
  /// [onValidationChanged] is an optional callback for validation state
  const ValidationCodeWidget({
    super.key,
    required this.onCompleted,
    required this.onEditingChanged,
    this.onValidationChanged,
  });

  @override
  State<ValidationCodeWidget> createState() => _ValidationCodeWidgetState();
}

class _ValidationCodeWidgetState extends State<ValidationCodeWidget> {
  
  /// Handles completion of code entry
  /// 
  /// Called when all digits of the validation code have been entered.
  /// Validates the code length and notifies parent components.
  void _handleCodeCompleted(String code) {
    final bool isValid = code.length == AuthValidationConstants.validationCodeLength;
    
    // Notify parent of validation state
    widget.onValidationChanged?.call(isValid);
    
    // Call completion callback
    widget.onCompleted(code);
  }

  /// Handles editing state changes
  /// 
  /// Called when the user starts or stops editing the validation code.
  /// Manages focus and validation state accordingly.
  void _handleEditingChanged(bool isEditing) {
    // Reset validation when starting to edit
    if (isEditing) {
      widget.onValidationChanged?.call(false);
    }
    
    // Unfocus when editing stops
    if (!isEditing) {
      FocusScope.of(context).unfocus();
    }
    
    // Notify parent of editing state change
    widget.onEditingChanged(isEditing);
  }

  @override
  Widget build(BuildContext context) {
    return VerificationCode(
      textStyle: AppTextStyles.size16Medium,
      keyboardType: TextInputType.number,
      digitsOnly: AuthValidationConstants.validationCodeDigitsOnly,
      underlineColor: AppColors.transparent,
      length: AuthValidationConstants.validationCodeLength,
      cursorColor: AppColors.primary,
      fullBorder: false,
      fillColor: AppColors.grey300,
      underlineUnfocusedColor: AppColors.transparent,
      onCompleted: _handleCodeCompleted,
      onEditing: _handleEditingChanged,
    );
  }
}
