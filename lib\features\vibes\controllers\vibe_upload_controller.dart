import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

class VibeUploadController extends BaseVibesController {
  VibeUploadController(AuthService authService) : super(authService);

  final _capturedMediaPath = ''.obs;
  final _isVideo = false.obs;
  final _isFrontCamera = false.obs;

  String get capturedMediaPath => _capturedMediaPath.value;
  bool get isVideo => _isVideo.value;
  bool get isFrontCamera => _isFrontCamera.value;

  set capturedMediaPath(String value) => _capturedMediaPath.value = value;
  set isVideo(bool value) => _isVideo.value = value;
  set isFrontCamera(bool value) => _isFrontCamera.value = value;

  Future<void> uploadVibe() async {
    
    await authService.vibesApi.createVibe(
      await http.MultipartFile.fromPath(
        'file',
        capturedMediaPath,
        contentType: isVideo
            ? MediaType('video', 'mp4')
            : MediaType('image', 'jpeg'),
      ),
      '05e98cfc-b8a5-4f98-ae4e-f4ccf4d64977',
      VibePrivacyEnum.public,
    );
  }
}
