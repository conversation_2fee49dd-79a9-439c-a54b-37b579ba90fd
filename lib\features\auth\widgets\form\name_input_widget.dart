import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

/// Custom name input widget for user registration
///
/// A specialized input field for collecting user's full name during registration.
/// Handles validation, formatting, and user interaction according to the
/// app's design system and registration requirements.
class NameInputWidget extends StatefulWidget {
  /// Controller for the name text field
  final TextEditingController controller;

  /// Callback function called when the input value changes
  /// Returns true if the input is valid (meets length requirements)
  final ValueChanged<bool> onValidationChanged;

  /// Optional callback for when the input value changes
  final ValueChanged<String>? onChanged;

  /// Optional width override for the input field
  final double? width;

  /// Creates a name input widget
  ///
  /// [controller] is required to manage the text field state
  /// [onValidationChanged] is called whenever validation state changes
  /// [onChanged] is an optional callback for input changes
  /// [width] optionally overrides the default width
  const NameInputWidget({
    super.key,
    required this.controller,
    required this.onValidationChanged,
    this.onChanged,
    this.width,
  });

  @override
  State<NameInputWidget> createState() => _NameInputWidgetState();
}

class _NameInputWidgetState extends State<NameInputWidget> {
  /// Handles text input changes and validation
  ///
  /// Validates the input length against requirements, prevents input
  /// beyond the maximum limit, and notifies parent of validation state.
  void _handleTextChange(String text) {
    // Validate input length
    final bool isValid = text.trim().length >= AuthValidationConstants.fullNameMinLength;

    // Prevent input beyond maximum length
    if (text.length > AuthValidationConstants.fullNameMaxLength) {
      widget.controller.value = widget.controller.value.copyWith(
        text: text.substring(0, AuthValidationConstants.fullNameMaxLength),
        selection: const TextSelection.collapsed(
          offset: AuthValidationConstants.fullNameMaxLength,
        ),
      );
      return;
    }

    // Notify parent of validation state change
    widget.onValidationChanged(isValid);

    // Call optional onChanged callback
    widget.onChanged?.call(text);
  }

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      width: widget.width ?? 300.0,
      child: TextFormField(
        keyboardType: TextInputType.name,
        cursorHeight: 0,
        textAlign: TextAlign.center,
        inputFormatters: [
          FilteringTextInputFormatter.allow(
            RegExp(AuthValidationConstants.namePattern),
          ),
        ],
        controller: widget.controller,
        onChanged: _handleTextChange,
        decoration: InputDecoration(
          hintText: AuthStrings.adSoyad,
          hintStyle: AppTextStyles.size32BoldTextSecondary,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
        style: AppTextStyles.size32Bold,
      ),
    );
  }
}
