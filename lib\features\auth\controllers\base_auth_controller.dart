import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

abstract class BaseAuthController extends BaseController {
  final AuthStateManager state;
  
  BaseAuthController(AuthService authService, this.state) : super(authService);

  void handleAuthError(dynamic error, [String? defaultMessage]) {
    final String errorMessage = error?.toString() ?? defaultMessage ?? 'Bir hata oluştu. Lütfen tekrar deneyin.';

    state.errorMessage = errorMessage;
    state.isLoading = false;
  }
  
  void setLoadingState(bool isLoading) {
    state.isLoading = isLoading;
    if (isLoading) {
      state.clearError();
    }
  }

  bool canProceedToNextStep() {
    return !state.isLoading && state.errorMessage.isEmpty;
  }
}
