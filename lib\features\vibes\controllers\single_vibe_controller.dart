import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';

class SinglePageVibeController extends BaseVibesController {
  final String vibeId;

  SinglePageVibeController(AuthService authService, this.vibeId) : super(authService);

  final _singleVibe = Rx<Vibe?>(null);

  Vibe? get singleVibe => _singleVibe.value;

  set singleVibe(Vibe? value) => _singleVibe.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await _loadInitialVibe();
    print('SingleVibeController has been initialized with user: ${sessionUser.sessionId}');
  }

  Future<void> stopVideo() async {
    if (singleVibe == null || !singleVibe!.isVideo) return;
    singleVibe!.video!.pause();
  }

  Future<void> _loadInitialVibe() async {
    final initialVibe = await authService.vibesApi.getByVibeId(vibeId);
    if (initialVibe == null) return;
    singleVibe = Vibe(
      content: VibeItem(
        vibeId: initialVibe.vibeId,
        vibeFolderId: initialVibe.vibeFolderId,
        mediaUrl: initialVibe.mediaUrl,
        mediaFormat: initialVibe.mediaFormat,
        thumbnailUrl: initialVibe.thumbnailUrl,
        caption: initialVibe.caption,
        creatorId: initialVibe.creatorId,
        creatorType: initialVibe.creatorType,
        creatorUsername: initialVibe.creatorUsername,
        creatorAvatarUrl: initialVibe.creatorAvatarUrl,
        iventId: initialVibe.iventId,
        iventName: initialVibe.iventName,
        memberCount: initialVibe.memberCount,
        likeCount: initialVibe.likeCount,
        commentCount: initialVibe.commentCount,
        nextVibeId: initialVibe.nextVibeId,
        previousVibeId: initialVibe.previousVibeId,
        vibeIndex: initialVibe.vibeIndex,
        vibeCount: initialVibe.vibeCount,
        createdAt: initialVibe.createdAt,
      ),
      vibeFolderId: initialVibe.vibeFolderId,
      videoManager: initialVibe.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );
    if (singleVibe!.isVideo) await _loadVideo();
  }

  Future<void> getNextVibe() async {
    if (singleVibe == null || singleVibe!.content.nextVibeId == null) return;
    if (singleVibe!.isVideo) singleVibe!.video!.pause();
    final nextVibe = await authService.vibesApi.getByVibeId(
      singleVibe!.content.nextVibeId!,
    );
    if (nextVibe == null) return;
    singleVibe = Vibe(
      content: VibeItem(
        vibeId: nextVibe.vibeId,
        vibeFolderId: nextVibe.vibeFolderId,
        mediaUrl: nextVibe.mediaUrl,
        mediaFormat: nextVibe.mediaFormat,
        thumbnailUrl: nextVibe.thumbnailUrl,
        caption: nextVibe.caption,
        creatorId: nextVibe.creatorId,
        creatorType: nextVibe.creatorType,
        creatorUsername: nextVibe.creatorUsername,
        creatorAvatarUrl: nextVibe.creatorAvatarUrl,
        iventId: nextVibe.iventId,
        iventName: nextVibe.iventName,
        memberCount: nextVibe.memberCount,
        likeCount: nextVibe.likeCount,
        commentCount: nextVibe.commentCount,
        nextVibeId: nextVibe.nextVibeId,
        previousVibeId: nextVibe.previousVibeId,
        vibeIndex: nextVibe.vibeIndex,
        vibeCount: nextVibe.vibeCount,
        createdAt: nextVibe.createdAt,
      ),
      vibeFolderId: nextVibe.vibeFolderId,
      videoManager: nextVibe.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );
    if (singleVibe!.isVideo) await _loadVideo();
  }

  Future<void> getPreviousVibe() async {
    if (singleVibe == null || singleVibe!.content.previousVibeId == null) return;
    if (singleVibe!.isVideo) singleVibe!.video!.pause();
    final previousVibe = await authService.vibesApi.getByVibeId(
      singleVibe!.content.previousVibeId!,
    );
    if (previousVibe == null) return;
    singleVibe = Vibe(
      content: VibeItem(
        vibeId: previousVibe.vibeId,
        vibeFolderId: previousVibe.vibeFolderId,
        mediaUrl: previousVibe.mediaUrl,
        mediaFormat: previousVibe.mediaFormat,
        thumbnailUrl: previousVibe.thumbnailUrl,
        caption: previousVibe.caption,
        creatorId: previousVibe.creatorId,
        creatorType: previousVibe.creatorType,
        creatorUsername: previousVibe.creatorUsername,
        creatorAvatarUrl: previousVibe.creatorAvatarUrl,
        iventId: previousVibe.iventId,
        iventName: previousVibe.iventName,
        memberCount: previousVibe.memberCount,
        likeCount: previousVibe.likeCount,
        commentCount: previousVibe.commentCount,
        nextVibeId: previousVibe.nextVibeId,
        previousVibeId: previousVibe.previousVibeId,
        vibeIndex: previousVibe.vibeIndex,
        vibeCount: previousVibe.vibeCount,
        createdAt: previousVibe.createdAt,
      ),
      vibeFolderId: previousVibe.vibeFolderId,
      videoManager: previousVibe.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );
    if (singleVibe!.isVideo) await _loadVideo();
  }

  Future<void> _loadVideo() async {
    await singleVibe!.video?.initializeVideo(cacheManager);
    singleVibe!.video?.play();
  }
}
