//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetMemoryByMemoryIdReturn {
  /// Returns a new [GetMemoryByMemoryIdReturn] instance.
  GetMemoryByMemoryIdReturn({
    required this.memoryId,
    required this.mediaUrl,
    this.thumbnailUrl,
    required this.caption,
    required this.creatorId,
    required this.creatorUsername,
    this.creatorAvatarUrl,
    required this.iventId,
    required this.iventName,
    this.dates = const [],
    this.memberCount,
    this.memberFirstnames = const [],
  });

  /// UUID of the memory
  String memoryId;

  /// URL to the memory media
  String mediaUrl;

  /// URL to the memory thumbnail
  String? thumbnailUrl;

  /// Caption of the memory
  String caption;

  /// UUID of the memory creator
  String creatorId;

  /// Username of the memory creator
  String creatorUsername;

  /// URL to the memory creator image
  String? creatorAvatarUrl;

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// List of dates for the ivent in ISO 8601 date-time format
  List<String>? dates;

  /// Number of members in the ivent
  ///
  /// Minimum value: 0
  int? memberCount;

  /// List of member's first names in the ivent
  List<String>? memberFirstnames;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetMemoryByMemoryIdReturn &&
    other.memoryId == memoryId &&
    other.mediaUrl == mediaUrl &&
    other.thumbnailUrl == thumbnailUrl &&
    other.caption == caption &&
    other.creatorId == creatorId &&
    other.creatorUsername == creatorUsername &&
    other.creatorAvatarUrl == creatorAvatarUrl &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    _deepEquality.equals(other.dates, dates) &&
    other.memberCount == memberCount &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (memoryId.hashCode) +
    (mediaUrl.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (caption.hashCode) +
    (creatorId.hashCode) +
    (creatorUsername.hashCode) +
    (creatorAvatarUrl == null ? 0 : creatorAvatarUrl!.hashCode) +
    (iventId.hashCode) +
    (iventName.hashCode) +
    (dates == null ? 0 : dates!.hashCode) +
    (memberCount == null ? 0 : memberCount!.hashCode) +
    (memberFirstnames == null ? 0 : memberFirstnames!.hashCode);

  @override
  String toString() => 'GetMemoryByMemoryIdReturn[memoryId=$memoryId, mediaUrl=$mediaUrl, thumbnailUrl=$thumbnailUrl, caption=$caption, creatorId=$creatorId, creatorUsername=$creatorUsername, creatorAvatarUrl=$creatorAvatarUrl, iventId=$iventId, iventName=$iventName, dates=$dates, memberCount=$memberCount, memberFirstnames=$memberFirstnames]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'memoryId'] = this.memoryId;
      json[r'mediaUrl'] = this.mediaUrl;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'caption'] = this.caption;
      json[r'creatorId'] = this.creatorId;
      json[r'creatorUsername'] = this.creatorUsername;
    if (this.creatorAvatarUrl != null) {
      json[r'creatorAvatarUrl'] = this.creatorAvatarUrl;
    } else {
      json[r'creatorAvatarUrl'] = null;
    }
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
    if (this.dates != null) {
      json[r'dates'] = this.dates;
    } else {
      json[r'dates'] = null;
    }
    if (this.memberCount != null) {
      json[r'memberCount'] = this.memberCount;
    } else {
      json[r'memberCount'] = null;
    }
    if (this.memberFirstnames != null) {
      json[r'memberFirstnames'] = this.memberFirstnames;
    } else {
      json[r'memberFirstnames'] = null;
    }
    return json;
  }

  /// Returns a new [GetMemoryByMemoryIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetMemoryByMemoryIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetMemoryByMemoryIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetMemoryByMemoryIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetMemoryByMemoryIdReturn(
        memoryId: mapValueOfType<String>(json, r'memoryId')!,
        mediaUrl: mapValueOfType<String>(json, r'mediaUrl')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        caption: mapValueOfType<String>(json, r'caption')!,
        creatorId: mapValueOfType<String>(json, r'creatorId')!,
        creatorUsername: mapValueOfType<String>(json, r'creatorUsername')!,
        creatorAvatarUrl: mapValueOfType<String>(json, r'creatorAvatarUrl'),
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        dates: json[r'dates'] is Iterable
            ? (json[r'dates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberCount: mapValueOfType<int>(json, r'memberCount'),
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<GetMemoryByMemoryIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetMemoryByMemoryIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetMemoryByMemoryIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetMemoryByMemoryIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetMemoryByMemoryIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetMemoryByMemoryIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetMemoryByMemoryIdReturn-objects as value to a dart map
  static Map<String, List<GetMemoryByMemoryIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetMemoryByMemoryIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetMemoryByMemoryIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'memoryId',
    'mediaUrl',
    'caption',
    'creatorId',
    'creatorUsername',
    'iventId',
    'iventName',
  };
}

