//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class UpdateDateByIventIdDto {
  /// Returns a new [UpdateDateByIventIdDto] instance.
  UpdateDateByIventIdDto({
    this.newDates = const [],
  });

  /// Array of new date strings in date-time format, in ISO 8601 date-time format
  List<String> newDates;

  @override
  bool operator ==(Object other) => identical(this, other) || other is UpdateDateByIventIdDto &&
    _deepEquality.equals(other.newDates, newDates);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (newDates.hashCode);

  @override
  String toString() => 'UpdateDateByIventIdDto[newDates=$newDates]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'newDates'] = this.newDates;
    return json;
  }

  /// Returns a new [UpdateDateByIventIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static UpdateDateByIventIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "UpdateDateByIventIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "UpdateDateByIventIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return UpdateDateByIventIdDto(
        newDates: json[r'newDates'] is Iterable
            ? (json[r'newDates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<UpdateDateByIventIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UpdateDateByIventIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UpdateDateByIventIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, UpdateDateByIventIdDto> mapFromJson(dynamic json) {
    final map = <String, UpdateDateByIventIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = UpdateDateByIventIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of UpdateDateByIventIdDto-objects as value to a dart map
  static Map<String, List<UpdateDateByIventIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<UpdateDateByIventIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = UpdateDateByIventIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'newDates',
  };
}

