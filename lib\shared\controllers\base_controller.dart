import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/app/routes/other.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';

abstract class BaseController extends GetxController {
  final AuthService authService;

  BaseController(this.authService);

  SessionUser get sessionUser => authService.sessionUser!;
  bool get isLoggedIn => authService.isLoggedIn;

  @mustCallSuper
  void initController() {}

  @mustCallSuper
  void closeController() {}

  @override
  void onInit() {
    super.onInit();
    initController();
  }

  @override
  void onClose() {
    closeController();
    super.onClose();
  }

  void goToSomethingWentWrongPage() => Get.toNamed(OtherRoutes.SOMETHING_WENT_WRONG);
}

abstract class BaseControllerWithSharedState<T extends GetxController> extends BaseController {
  final T state;

  BaseControllerWithSharedState(AuthService authService, this.state) : super(authService);
}
