import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';

class OnboardingContentWidget extends StatelessWidget {
  final int currentPage;
  final List<String> imagePaths;
  final ValueChanged<int>? onPageIndicatorTapped;

  const OnboardingContentWidget({
    super.key,
    required this.currentPage,
    required this.imagePaths,
    this.onPageIndicatorTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: _buildGradientImage()),
          const SizedBox(height: AuthDimensions.relatedElementSpacing),
          _buildOnboardingTextContent(),
          const SizedBox(height: AuthDimensions.formElementSpacing),
          _buildPageIndicator(),
        ],
      ),
    );
  }

  Widget _buildGradientImage() {
    return Stack(
      children: [
        IaRoundedContainer(
          roundness: AppDimensions.radiusXL,
          decorationImage: DecorationImage(
            image: AssetImage(imagePaths[currentPage]),
            fit: BoxFit.cover,
          ),
        ),
        const IaRoundedContainer(
          roundness: AppDimensions.radiusXL,
          gradient: AppColors.gradientWhite,
        ),
      ],
    );
  }

  Widget _buildOnboardingTextContent() {
    switch (currentPage) {
      case 0:
        return _buildOnboardingText1();
      case 1:
        return _buildOnboardingText2();
      case 2:
        return _buildOnboardingText3();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildOnboardingText1() {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AuthStrings.onboarding1[0], style: AppTextStyles.size24BoldPrimary),
            const SizedBox(width: AppDimensions.padding4),
            Text(AuthStrings.onboarding1[1], style: AppTextStyles.size24Bold),
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AuthStrings.onboarding1[2], style: AppTextStyles.size24BoldPrimary),
            const SizedBox(width: AppDimensions.padding4),
            Text(AuthStrings.onboarding1[3], style: AppTextStyles.size24Bold),
          ],
        ),
      ],
    );
  }

  Widget _buildOnboardingText2() {
    return Column(
      children: [
        Text(AuthStrings.onboarding2[0] + ' ' + AuthStrings.onboarding2[1], style: AppTextStyles.size24BoldPrimary),
        Text(AuthStrings.onboarding2[2] + ' ' + AuthStrings.onboarding2[3], style: AppTextStyles.size24Bold),
      ],
    );
  }

  Widget _buildOnboardingText3() {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AuthStrings.onboarding3[0], style: AppTextStyles.size24BoldPrimary),
            const SizedBox(width: AppDimensions.padding4),
            Text(AuthStrings.onboarding3[1], style: AppTextStyles.size24Bold),
          ],
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AuthStrings.onboarding3[2], style: AppTextStyles.size24BoldPrimary),
            const SizedBox(width: AppDimensions.padding4),
            Text(AuthStrings.onboarding3[3], style: AppTextStyles.size24Bold),
          ],
        ),
      ],
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        imagePaths.length,
        (index) => _buildPageIndicatorDot(index),
      ),
    );
  }

  Widget _buildPageIndicatorDot(int index) {
    final bool isActive = index == currentPage;

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AuthDimensions.pageIndicatorSpacing / 2,
      ),
      child: GestureDetector(
        onTap: () => onPageIndicatorTapped?.call(index),
        child: CircleAvatar(
          backgroundColor: isActive ? AppColors.darkGrey : AppColors.mediumGrey,
          radius: AuthDimensions.pageIndicatorRadius,
        ),
      ),
    );
  }
}
