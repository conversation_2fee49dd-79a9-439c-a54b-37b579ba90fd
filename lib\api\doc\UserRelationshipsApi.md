# openapi.api.UserRelationshipsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**blockUserByUserId**](UserRelationshipsApi.md#userrelationshipscontrollerblockuserbyuserid) | **POST** /userRelationships/{userId}/block | Hesap IDsi ile hesap engellenir
[**getUserBlocklist**](UserRelationshipsApi.md#userrelationshipscontrollergetuserblocklist) | **GET** /userRelationships/blocklist | Engellenenleri listeler
[**inviteFriendByUserId**](UserRelationshipsApi.md#userrelationshipscontrollerinvitefriendbyuserid) | **POST** /userRelationships/{userId}/invite | Hesap IDsi ile arkadaş daveti gönderilir
[**removeFriendByUserId**](UserRelationshipsApi.md#userrelationshipscontrollerremovefriendbyuserid) | **POST** /userRelationships/{userId}/remove | Hesap IDsi ile arkadaşlıktan çıkılır
[**searchFriendsByUserId**](UserRelationshipsApi.md#userrelationshipscontrollersearchfriendsbyuserid) | **GET** /userRelationships/{userId}/friends | Hesap IDsi ile arkadaşlar listelenir
[**unblockUserByUserId**](UserRelationshipsApi.md#userrelationshipscontrollerunblockuserbyuserid) | **POST** /userRelationships/{userId}/unblock | Hesap IDsi ile hesap engeli kaldırılır


# **blockUserByUserId**
> blockUserByUserId(userId)

Hesap IDsi ile hesap engellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UserRelationshipsApi();
final userId = userId_example; // String | 

try {
    api_instance.blockUserByUserId(userId);
} catch (e) {
    print('Exception when calling UserRelationshipsApi->blockUserByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getUserBlocklist**
> GetUserBlocklistReturn getUserBlocklist()

Engellenenleri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UserRelationshipsApi();

try {
    final result = api_instance.getUserBlocklist();
    print(result);
} catch (e) {
    print('Exception when calling UserRelationshipsApi->getUserBlocklist: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**GetUserBlocklistReturn**](GetUserBlocklistReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **inviteFriendByUserId**
> inviteFriendByUserId(userId)

Hesap IDsi ile arkadaş daveti gönderilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UserRelationshipsApi();
final userId = userId_example; // String | 

try {
    api_instance.inviteFriendByUserId(userId);
} catch (e) {
    print('Exception when calling UserRelationshipsApi->inviteFriendByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeFriendByUserId**
> removeFriendByUserId(userId)

Hesap IDsi ile arkadaşlıktan çıkılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UserRelationshipsApi();
final userId = userId_example; // String | 

try {
    api_instance.removeFriendByUserId(userId);
} catch (e) {
    print('Exception when calling UserRelationshipsApi->removeFriendByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchFriendsByUserId**
> SearchFriendsByUserIdReturn searchFriendsByUserId(userId, type, q, limit, page)

Hesap IDsi ile arkadaşlar listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UserRelationshipsApi();
final userId = userId_example; // String | 
final type = ; // FriendListingTypeEnum | Type of friends to list - either groups or users
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchFriendsByUserId(userId, type, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UserRelationshipsApi->searchFriendsByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userId** | **String**|  | 
 **type** | [**FriendListingTypeEnum**](.md)| Type of friends to list - either groups or users | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchFriendsByUserIdReturn**](SearchFriendsByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unblockUserByUserId**
> unblockUserByUserId(userId)

Hesap IDsi ile hesap engeli kaldırılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UserRelationshipsApi();
final userId = userId_example; // String | 

try {
    api_instance.unblockUserByUserId(userId);
} catch (e) {
    print('Exception when calling UserRelationshipsApi->unblockUserByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **userId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


