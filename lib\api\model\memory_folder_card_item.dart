//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MemoryFolderCardItem {
  /// Returns a new [MemoryFolderCardItem] instance.
  MemoryFolderCardItem({
    required this.memoryFolderId,
    this.thumbnailUrl,
    required this.iventId,
    required this.iventName,
    this.dates = const [],
    required this.memberCount,
    this.memberFirstnames = const [],
    this.createdAt,
  });

  /// UUID of the memory folder
  String memoryFolderId;

  /// URL to the memory folder thumbnail image
  String? thumbnailUrl;

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// List of dates for the ivent in ISO 8601 date-time format
  List<String> dates;

  /// Number of members in the ivent
  ///
  /// Minimum value: 0
  int memberCount;

  /// List of member's first names in the ivent
  List<String> memberFirstnames;

  /// Date of creation of the memory folder in ISO 8601 date-time format
  String? createdAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MemoryFolderCardItem &&
    other.memoryFolderId == memoryFolderId &&
    other.thumbnailUrl == thumbnailUrl &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    _deepEquality.equals(other.dates, dates) &&
    other.memberCount == memberCount &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames) &&
    other.createdAt == createdAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (memoryFolderId.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (iventId.hashCode) +
    (iventName.hashCode) +
    (dates.hashCode) +
    (memberCount.hashCode) +
    (memberFirstnames.hashCode) +
    (createdAt == null ? 0 : createdAt!.hashCode);

  @override
  String toString() => 'MemoryFolderCardItem[memoryFolderId=$memoryFolderId, thumbnailUrl=$thumbnailUrl, iventId=$iventId, iventName=$iventName, dates=$dates, memberCount=$memberCount, memberFirstnames=$memberFirstnames, createdAt=$createdAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'memoryFolderId'] = this.memoryFolderId;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
      json[r'dates'] = this.dates;
      json[r'memberCount'] = this.memberCount;
      json[r'memberFirstnames'] = this.memberFirstnames;
    if (this.createdAt != null) {
      json[r'createdAt'] = this.createdAt;
    } else {
      json[r'createdAt'] = null;
    }
    return json;
  }

  /// Returns a new [MemoryFolderCardItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MemoryFolderCardItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MemoryFolderCardItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MemoryFolderCardItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MemoryFolderCardItem(
        memoryFolderId: mapValueOfType<String>(json, r'memoryFolderId')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        dates: json[r'dates'] is Iterable
            ? (json[r'dates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        createdAt: mapValueOfType<String>(json, r'createdAt'),
      );
    }
    return null;
  }

  static List<MemoryFolderCardItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MemoryFolderCardItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MemoryFolderCardItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MemoryFolderCardItem> mapFromJson(dynamic json) {
    final map = <String, MemoryFolderCardItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MemoryFolderCardItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MemoryFolderCardItem-objects as value to a dart map
  static Map<String, List<MemoryFolderCardItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MemoryFolderCardItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = MemoryFolderCardItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'memoryFolderId',
    'iventId',
    'iventName',
    'dates',
    'memberCount',
    'memberFirstnames',
  };
}

