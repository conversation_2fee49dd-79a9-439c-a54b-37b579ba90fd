//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class UpdateLinksByPageIdDto {
  /// Returns a new [UpdateLinksByPageIdDto] instance.
  UpdateLinksByPageIdDto({
    required this.newLink,
  });

  /// New website link for the page
  String newLink;

  @override
  bool operator ==(Object other) => identical(this, other) || other is UpdateLinksByPageIdDto &&
    other.newLink == newLink;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (newLink.hashCode);

  @override
  String toString() => 'UpdateLinksByPageIdDto[newLink=$newLink]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'newLink'] = this.newLink;
    return json;
  }

  /// Returns a new [UpdateLinksByPageIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static UpdateLinksByPageIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "UpdateLinksByPageIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "UpdateLinksByPageIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return UpdateLinksByPageIdDto(
        newLink: mapValueOfType<String>(json, r'newLink')!,
      );
    }
    return null;
  }

  static List<UpdateLinksByPageIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UpdateLinksByPageIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UpdateLinksByPageIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, UpdateLinksByPageIdDto> mapFromJson(dynamic json) {
    final map = <String, UpdateLinksByPageIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = UpdateLinksByPageIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of UpdateLinksByPageIdDto-objects as value to a dart map
  static Map<String, List<UpdateLinksByPageIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<UpdateLinksByPageIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = UpdateLinksByPageIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'newLink',
  };
}

