//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class CommentsApi {
  CommentsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Yorum oluşturur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreateCommentDto] createCommentDto (required):
  Future<Response> createCommentWithHttpInfo(CreateCommentDto createCommentDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/comments/create';

    // ignore: prefer_final_locals
    Object? postBody = createCommentDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Yorum oluşturur
  ///
  /// Parameters:
  ///
  /// * [CreateCommentDto] createCommentDto (required):
  Future<CreateCommentReturn?> createComment(CreateCommentDto createCommentDto,) async {
    final response = await createCommentWithHttpInfo(createCommentDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateCommentReturn',) as CreateCommentReturn;
    
    }
    return null;
  }

  /// Yorumun IDsi ile yorumu siler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deleteCommentByCommentIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/comments/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Yorumun IDsi ile yorumu siler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deleteCommentByCommentId(String id,) async {
    final response = await deleteCommentByCommentIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

