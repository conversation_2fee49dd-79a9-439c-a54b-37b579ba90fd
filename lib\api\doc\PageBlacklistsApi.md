# openapi.api.PageBlacklistsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**blockUserByPageId**](PageBlacklistsApi.md#pageblacklistscontrollerblockuserbypageid) | **POST** /pageBlacklists/{pageId}/block | Sayfa IDsi ile bir hesabı engeller
[**searchPageBlocklistByPageId**](PageBlacklistsApi.md#pageblacklistscontrollersearchpageblocklistbypageid) | **GET** /pageBlacklists/{pageId}/blocklist | Sayfa IDsi ile engellenenleri listeler
[**unblockUserByPageId**](PageBlacklistsApi.md#pageblacklistscontrollerunblockuserbypageid) | **POST** /pageBlacklists/{pageId}/unblock | Sayfa IDsi ile bir hesabın engelini kaldırır


# **blockUserByPageId**
> blockUserByPageId(pageId, blockUserByPageIdDto)

Sayfa IDsi ile bir hesabı engeller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageBlacklistsApi();
final pageId = pageId_example; // String | 
final blockUserByPageIdDto = BlockUserByPageIdDto(); // BlockUserByPageIdDto | 

try {
    api_instance.blockUserByPageId(pageId, blockUserByPageIdDto);
} catch (e) {
    print('Exception when calling PageBlacklistsApi->blockUserByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **blockUserByPageIdDto** | [**BlockUserByPageIdDto**](BlockUserByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchPageBlocklistByPageId**
> SearchPageBlocklistByPageIdReturn searchPageBlocklistByPageId(pageId, q, limit, page)

Sayfa IDsi ile engellenenleri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageBlacklistsApi();
final pageId = pageId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchPageBlocklistByPageId(pageId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PageBlacklistsApi->searchPageBlocklistByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchPageBlocklistByPageIdReturn**](SearchPageBlocklistByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unblockUserByPageId**
> unblockUserByPageId(pageId, unblockUserByPageIdDto)

Sayfa IDsi ile bir hesabın engelini kaldırır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageBlacklistsApi();
final pageId = pageId_example; // String | 
final unblockUserByPageIdDto = UnblockUserByPageIdDto(); // UnblockUserByPageIdDto | 

try {
    api_instance.unblockUserByPageId(pageId, unblockUserByPageIdDto);
} catch (e) {
    print('Exception when calling PageBlacklistsApi->unblockUserByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **unblockUserByPageIdDto** | [**UnblockUserByPageIdDto**](UnblockUserByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


