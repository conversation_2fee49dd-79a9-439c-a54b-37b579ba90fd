//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

library openapi.api;

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:http/http.dart';
import 'package:intl/intl.dart';
import 'package:meta/meta.dart';

part 'api_client.dart';
part 'api_helper.dart';
part 'api_exception.dart';
part 'auth/authentication.dart';
part 'auth/api_key_auth.dart';
part 'auth/oauth.dart';
part 'auth/http_basic_auth.dart';
part 'auth/http_bearer_auth.dart';

part 'api/auth_api.dart';
part 'api/comments_api.dart';
part 'api/group_memberships_api.dart';
part 'api/groups_api.dart';
part 'api/health_api.dart';
part 'api/hobbies_api.dart';
part 'api/home_api.dart';
part 'api/ivent_collabs_api.dart';
part 'api/ivents_api.dart';
part 'api/locations_api.dart';
part 'api/mapbox_api.dart';
part 'api/memories_api.dart';
part 'api/notifications_api.dart';
part 'api/page_blacklists_api.dart';
part 'api/page_memberships_api.dart';
part 'api/pages_api.dart';
part 'api/squad_memberships_api.dart';
part 'api/universities_api.dart';
part 'api/user_relationships_api.dart';
part 'api/users_api.dart';
part 'api/vibes_api.dart';

part 'model/account_type_enum.dart';
part 'model/add_hobbies_by_hobby_id_dto.dart';
part 'model/add_moderator_by_group_id_dto.dart';
part 'model/add_page_members_by_page_id_dto.dart';
part 'model/auth_enum.dart';
part 'model/basic_account_list_item.dart';
part 'model/block_user_by_page_id_dto.dart';
part 'model/collab_dto.dart';
part 'model/collabrator_list_item.dart';
part 'model/comment_item.dart';
part 'model/create_comment_dto.dart';
part 'model/create_comment_return.dart';
part 'model/create_group_dto.dart';
part 'model/create_group_return.dart';
part 'model/create_ivent_dto.dart';
part 'model/create_ivent_return.dart';
part 'model/create_memory_dto.dart';
part 'model/create_memory_return.dart';
part 'model/create_page_dto.dart';
part 'model/create_page_return.dart';
part 'model/create_vibe_return.dart';
part 'model/feed_date_enum.dart';
part 'model/feed_return.dart';
part 'model/friend_listing_type_enum.dart';
part 'model/get_banner_by_ivent_id_dto.dart';
part 'model/get_banner_by_ivent_id_return.dart';
part 'model/get_comments_by_vibe_id_return.dart';
part 'model/get_contacts_by_user_id_dto.dart';
part 'model/get_contacts_by_user_id_return.dart';
part 'model/get_favorites_by_user_id_return.dart';
part 'model/get_follower_friends_by_user_id_return.dart';
part 'model/get_followers_by_user_id_return.dart';
part 'model/get_followings_by_user_id_return.dart';
part 'model/get_group_by_group_id_return.dart';
part 'model/get_ivent_page_by_ivent_id_return.dart';
part 'model/get_ivents_by_user_id_return.dart';
part 'model/get_ivents_created_by_page_id_return.dart';
part 'model/get_latest_ivents_return.dart';
part 'model/get_latest_locations_return.dart';
part 'model/get_level_by_user_id_return.dart';
part 'model/get_likes_by_vibe_id_return.dart';
part 'model/get_locations_return.dart';
part 'model/get_memory_by_memory_id_return.dart';
part 'model/get_memory_folders_by_user_id_return.dart';
part 'model/get_notifications_return.dart';
part 'model/get_page_by_page_id_return.dart';
part 'model/get_page_details_by_page_id_return.dart';
part 'model/get_pages_by_user_id_return.dart';
part 'model/get_suggested_images_return.dart';
part 'model/get_user_banner_by_user_id_return.dart';
part 'model/get_user_blocklist_return.dart';
part 'model/get_user_by_user_id_return.dart';
part 'model/get_vibe_by_vibe_id_return.dart';
part 'model/get_vibe_folders_by_page_id_return.dart';
part 'model/get_vibe_folders_by_user_id_return.dart';
part 'model/get_vibes_return.dart';
part 'model/group_list_item.dart';
part 'model/group_membership_status_enum.dart';
part 'model/hobbies_search_origin_enum.dart';
part 'model/hobby_item.dart';
part 'model/invite_friends_by_ivent_id_dto.dart';
part 'model/invite_members_by_group_id_dto.dart';
part 'model/ivent_card_item.dart';
part 'model/ivent_creator_type_enum.dart';
part 'model/ivent_list_item.dart';
part 'model/ivent_list_item_with_is_favorited.dart';
part 'model/ivent_listing_type_enum.dart';
part 'model/ivent_privacy_enum.dart';
part 'model/ivent_view_type_enum.dart';
part 'model/join_ivent_and_create_squad_by_ivent_id_dto.dart';
part 'model/location_item.dart';
part 'model/map_return.dart';
part 'model/marker_item.dart';
part 'model/media_format_enum.dart';
part 'model/memory_folder_card_item.dart';
part 'model/memory_origin_enum.dart';
part 'model/notification_enum.dart';
part 'model/notification_item.dart';
part 'model/notification_reply_type_enum.dart';
part 'model/page_membership_status_enum.dart';
part 'model/register_dto.dart';
part 'model/register_return.dart';
part 'model/remove_collab_by_ivent_id_dto.dart';
part 'model/remove_follower_by_page_id_dto.dart';
part 'model/remove_follower_by_user_id_dto.dart';
part 'model/remove_member_by_group_id_dto.dart';
part 'model/remove_moderator_by_group_id_dto.dart';
part 'model/remove_page_member_by_page_id_dto.dart';
part 'model/remove_page_moderator_by_page_id_dto.dart';
part 'model/search_account_return.dart';
part 'model/search_administration_by_page_id_return.dart';
part 'model/search_box_category_item.dart';
part 'model/search_box_category_list_return.dart';
part 'model/search_box_category_return.dart';
part 'model/search_box_context.dart';
part 'model/search_box_context_address.dart';
part 'model/search_box_context_district.dart';
part 'model/search_box_context_locality.dart';
part 'model/search_box_context_neighborhood.dart';
part 'model/search_box_context_place.dart';
part 'model/search_box_context_postcode.dart';
part 'model/search_box_context_region.dart';
part 'model/search_box_context_street.dart';
part 'model/search_box_coordinates.dart';
part 'model/search_box_feature.dart';
part 'model/search_box_forward_return.dart';
part 'model/search_box_geometry.dart';
part 'model/search_box_properties.dart';
part 'model/search_box_retrieve_return.dart';
part 'model/search_box_reverse_return.dart';
part 'model/search_box_routable_points.dart';
part 'model/search_box_suggest_feature.dart';
part 'model/search_box_suggest_return.dart';
part 'model/search_collabs_for_ivent_creation_return.dart';
part 'model/search_collabs_return.dart';
part 'model/search_followers_by_page_id_return.dart';
part 'model/search_friends_by_user_id_return.dart';
part 'model/search_group_members_by_group_id_return.dart';
part 'model/search_hobbies_return.dart';
part 'model/search_invitable_users_by_ivent_id_return.dart';
part 'model/search_ivent_return.dart';
part 'model/search_moderators_for_page_creation_return.dart';
part 'model/search_page_blocklist_by_page_id_return.dart';
part 'model/search_page_members_by_page_id_return.dart';
part 'model/search_participants_by_ivent_id_return.dart';
part 'model/search_universities_return.dart';
part 'model/search_users_for_group_creation_return.dart';
part 'model/search_users_to_add_by_page_id_return.dart';
part 'model/send_verification_code_dto.dart';
part 'model/side_menu_page_item.dart';
part 'model/transfer_administration_by_group_id_dto.dart';
part 'model/transfer_page_administration_by_page_id_dto.dart';
part 'model/unblock_user_by_page_id_dto.dart';
part 'model/university_item.dart';
part 'model/update_by_user_id_dto.dart';
part 'model/update_by_vibe_id_dto.dart';
part 'model/update_date_by_ivent_id_dto.dart';
part 'model/update_description_by_page_id_dto.dart';
part 'model/update_details_by_ivent_id_dto.dart';
part 'model/update_email_by_user_id_dto.dart';
part 'model/update_grad_by_user_id_dto.dart';
part 'model/update_links_by_page_id_dto.dart';
part 'model/update_location_by_ivent_id_dto.dart';
part 'model/update_location_by_page_id_dto.dart';
part 'model/update_phone_number_by_user_id_dto.dart';
part 'model/user_edu_verification_enum.dart';
part 'model/user_gender_enum.dart';
part 'model/user_list_item.dart';
part 'model/user_list_item_with_group_role.dart';
part 'model/user_list_item_with_page_role.dart';
part 'model/user_list_item_with_phone_number.dart';
part 'model/user_list_item_with_relationship_status.dart';
part 'model/user_relationship_status_enum.dart';
part 'model/user_role_enum.dart';
part 'model/validate_dto.dart';
part 'model/validate_return.dart';
part 'model/vibe_folder_card_item.dart';
part 'model/vibe_item.dart';
part 'model/vibe_privacy_enum.dart';


/// An [ApiClient] instance that uses the default values obtained from
/// the OpenAPI specification file.
var defaultApiClient = ApiClient();

const _delimiters = {'csv': ',', 'ssv': ' ', 'tsv': '\t', 'pipes': '|'};
const _dateEpochMarker = 'epoch';
const _deepEquality = DeepCollectionEquality();
final _dateFormatter = DateFormat('yyyy-MM-dd');
final _regList = RegExp(r'^List<(.*)>$');
final _regSet = RegExp(r'^Set<(.*)>$');
final _regMap = RegExp(r'^Map<String,(.*)>$');

bool _isEpochMarker(String? pattern) => pattern == _dateEpochMarker || pattern == '/$_dateEpochMarker/';
