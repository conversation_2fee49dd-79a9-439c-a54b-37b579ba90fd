# openapi.model.GetLocationsReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**locations** | [**List<LocationItem>**](LocationItem.md) | List of locations | [default to const []]
**locationCount** | **int** | Total number of locations | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



