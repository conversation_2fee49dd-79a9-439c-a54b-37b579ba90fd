# openapi.model.UniversityItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**universityName** | **String** | Name of the university | 
**universityImageUrl** | **String** | URL to the university image | [optional] 
**universityLocationState** | **String** | State where the university is located | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



