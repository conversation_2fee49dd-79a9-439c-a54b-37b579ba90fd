//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class HobbiesSearchOriginEnum {
  /// Instantiate a new enum with the provided [value].
  const HobbiesSearchOriginEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const default_ = HobbiesSearchOriginEnum._(r'default');
  static const profile = HobbiesSearchOriginEnum._(r'profile');

  /// List of all possible values in this [enum][HobbiesSearchOriginEnum].
  static const values = <HobbiesSearchOriginEnum>[
    default_,
    profile,
  ];

  static HobbiesSearchOriginEnum? fromJson(dynamic value) => HobbiesSearchOriginEnumTypeTransformer().decode(value);

  static List<HobbiesSearchOriginEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <HobbiesSearchOriginEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = HobbiesSearchOriginEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [HobbiesSearchOriginEnum] to String,
/// and [decode] dynamic data back to [HobbiesSearchOriginEnum].
class HobbiesSearchOriginEnumTypeTransformer {
  factory HobbiesSearchOriginEnumTypeTransformer() => _instance ??= const HobbiesSearchOriginEnumTypeTransformer._();

  const HobbiesSearchOriginEnumTypeTransformer._();

  String encode(HobbiesSearchOriginEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a HobbiesSearchOriginEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  HobbiesSearchOriginEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'default': return HobbiesSearchOriginEnum.default_;
        case r'profile': return HobbiesSearchOriginEnum.profile;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [HobbiesSearchOriginEnumTypeTransformer] instance.
  static HobbiesSearchOriginEnumTypeTransformer? _instance;
}

