//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxContextPostcode {
  /// Returns a new [SearchBoxContextPostcode] instance.
  SearchBoxContextPostcode({
    this.id,
    this.name,
  });

  /// Postcode ID
  String? id;

  /// Postcode name
  String? name;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxContextPostcode &&
    other.id == id &&
    other.name == name;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id == null ? 0 : id!.hashCode) +
    (name == null ? 0 : name!.hashCode);

  @override
  String toString() => 'SearchBoxContextPostcode[id=$id, name=$name]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.id != null) {
      json[r'id'] = this.id;
    } else {
      json[r'id'] = null;
    }
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    return json;
  }

  /// Returns a new [SearchBoxContextPostcode] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxContextPostcode? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxContextPostcode[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxContextPostcode[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxContextPostcode(
        id: mapValueOfType<String>(json, r'id'),
        name: mapValueOfType<String>(json, r'name'),
      );
    }
    return null;
  }

  static List<SearchBoxContextPostcode> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxContextPostcode>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxContextPostcode.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxContextPostcode> mapFromJson(dynamic json) {
    final map = <String, SearchBoxContextPostcode>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxContextPostcode.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxContextPostcode-objects as value to a dart map
  static Map<String, List<SearchBoxContextPostcode>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxContextPostcode>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxContextPostcode.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

