//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchParticipantsByIventIdReturn {
  /// Returns a new [SearchParticipantsByIventIdReturn] instance.
  SearchParticipantsByIventIdReturn({
    this.users = const [],
    required this.userCount,
    required this.viewType,
  });

  /// List of users with their relationship status
  List<UserListItemWithRelationshipStatus> users;

  /// Total number of users
  ///
  /// Minimum value: 0
  int userCount;

  IventViewTypeEnum viewType;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchParticipantsByIventIdReturn &&
    _deepEquality.equals(other.users, users) &&
    other.userCount == userCount &&
    other.viewType == viewType;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (users.hashCode) +
    (userCount.hashCode) +
    (viewType.hashCode);

  @override
  String toString() => 'SearchParticipantsByIventIdReturn[users=$users, userCount=$userCount, viewType=$viewType]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'users'] = this.users;
      json[r'userCount'] = this.userCount;
      json[r'viewType'] = this.viewType;
    return json;
  }

  /// Returns a new [SearchParticipantsByIventIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchParticipantsByIventIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchParticipantsByIventIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchParticipantsByIventIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchParticipantsByIventIdReturn(
        users: UserListItemWithRelationshipStatus.listFromJson(json[r'users']),
        userCount: mapValueOfType<int>(json, r'userCount')!,
        viewType: IventViewTypeEnum.fromJson(json[r'viewType'])!,
      );
    }
    return null;
  }

  static List<SearchParticipantsByIventIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchParticipantsByIventIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchParticipantsByIventIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchParticipantsByIventIdReturn> mapFromJson(dynamic json) {
    final map = <String, SearchParticipantsByIventIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchParticipantsByIventIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchParticipantsByIventIdReturn-objects as value to a dart map
  static Map<String, List<SearchParticipantsByIventIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchParticipantsByIventIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchParticipantsByIventIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'users',
    'userCount',
    'viewType',
  };
}

