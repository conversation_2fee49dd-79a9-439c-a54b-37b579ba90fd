# openapi.model.CreateCommentDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**comment** | **String** | The comment text content | 
**vibeId** | **String** | UUID of the vibe being commented on | 
**iventName** | **String** | An ivent name can only contain letters, numbers, underscores, and hyphens. | 
**thumbnailUrl** | **String** | Optional thumbnail URL for the comment | [optional] 
**creatorId** | **String** | UUID of the user creating the comment | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



