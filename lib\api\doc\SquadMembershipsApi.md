# openapi.api.SquadMembershipsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**inviteFriendsByIventId**](SquadMembershipsApi.md#squadmembershipscontrollerinvitefriendsbyiventid) | **POST** /squadMemberships/{iventId}/invite | Ivent IDsi ile seçilen hesaplar davet edilir
[**joinIventAndCreateSquadByIventId**](SquadMembershipsApi.md#squadmembershipscontrollerjoiniventandcreatesquadbyiventid) | **POST** /squadMemberships/{iventId}/join | Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)
[**leaveSquadByIventId**](SquadMembershipsApi.md#squadmembershipscontrollerleavesquadbyiventid) | **POST** /squadMemberships/{iventId}/leave | Ivent IDsi ile etkinlikten ayrılınır
[**searchInvitableUsersByIventId**](SquadMembershipsApi.md#squadmembershipscontrollersearchinvitableusersbyiventid) | **GET** /squadMemberships/{iventId}/search | Ivente katılırken davet edilebilecek hesapları listeler
[**searchParticipantsByIventId**](SquadMembershipsApi.md#squadmembershipscontrollersearchparticipantsbyiventid) | **GET** /squadMemberships/{iventId} | Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler


# **inviteFriendsByIventId**
> inviteFriendsByIventId(iventId, inviteFriendsByIventIdDto)

Ivent IDsi ile seçilen hesaplar davet edilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = SquadMembershipsApi();
final iventId = iventId_example; // String | 
final inviteFriendsByIventIdDto = InviteFriendsByIventIdDto(); // InviteFriendsByIventIdDto | 

try {
    api_instance.inviteFriendsByIventId(iventId, inviteFriendsByIventIdDto);
} catch (e) {
    print('Exception when calling SquadMembershipsApi->inviteFriendsByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 
 **inviteFriendsByIventIdDto** | [**InviteFriendsByIventIdDto**](InviteFriendsByIventIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **joinIventAndCreateSquadByIventId**
> joinIventAndCreateSquadByIventId(iventId, joinIventAndCreateSquadByIventIdDto)

Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = SquadMembershipsApi();
final iventId = iventId_example; // String | 
final joinIventAndCreateSquadByIventIdDto = JoinIventAndCreateSquadByIventIdDto(); // JoinIventAndCreateSquadByIventIdDto | 

try {
    api_instance.joinIventAndCreateSquadByIventId(iventId, joinIventAndCreateSquadByIventIdDto);
} catch (e) {
    print('Exception when calling SquadMembershipsApi->joinIventAndCreateSquadByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 
 **joinIventAndCreateSquadByIventIdDto** | [**JoinIventAndCreateSquadByIventIdDto**](JoinIventAndCreateSquadByIventIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **leaveSquadByIventId**
> leaveSquadByIventId(iventId)

Ivent IDsi ile etkinlikten ayrılınır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = SquadMembershipsApi();
final iventId = iventId_example; // String | 

try {
    api_instance.leaveSquadByIventId(iventId);
} catch (e) {
    print('Exception when calling SquadMembershipsApi->leaveSquadByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchInvitableUsersByIventId**
> SearchInvitableUsersByIventIdReturn searchInvitableUsersByIventId(iventId, type, q, limit, page)

Ivente katılırken davet edilebilecek hesapları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = SquadMembershipsApi();
final iventId = iventId_example; // String | 
final type = ; // FriendListingTypeEnum | Type of friends to list - either groups or users
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchInvitableUsersByIventId(iventId, type, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling SquadMembershipsApi->searchInvitableUsersByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 
 **type** | [**FriendListingTypeEnum**](.md)| Type of friends to list - either groups or users | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchInvitableUsersByIventIdReturn**](SearchInvitableUsersByIventIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchParticipantsByIventId**
> SearchParticipantsByIventIdReturn searchParticipantsByIventId(iventId, q, limit, page)

Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = SquadMembershipsApi();
final iventId = iventId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchParticipantsByIventId(iventId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling SquadMembershipsApi->searchParticipantsByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchParticipantsByIventIdReturn**](SearchParticipantsByIventIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


