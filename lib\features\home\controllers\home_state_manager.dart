import 'package:get/get.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';

class HomeStateManager extends GetxController {
  final currentDate = DateTime.now().obs;

  // Shared Reactive State
  final _dateFilterIndex = 1.obs;
  final _selectedDates = <DateTime>[].obs;
  final _selectedHobbyIds = <String>[].obs;
  final _locationCoeff = 5.obs;
  final _selectedPlace = Rxn<IaLocationItem>();

  // Getters
  int get dateFilterIndex => _dateFilterIndex.value;
  List<DateTime> get selectedDates => _selectedDates;
  List<String> get selectedHobbyIds => _selectedHobbyIds;
  int get locationCoeff => _locationCoeff.value;
  IaLocationItem? get selectedPlace => _selectedPlace.value;
  int get appliedFilterCount =>
      _selectedHobbyIds.length + (_locationCoeff.value != 5.0 ? 1 : 0) + (selectedPlace != null ? 1 : 0);

  // Setters
  set dateFilterIndex(int value) => _dateFilterIndex.value = value;
  set selectedDates(List<DateTime> value) => _selectedDates.assignAll(value);
  set selectedHobbyIds(List<String> value) => _selectedHobbyIds.assignAll(value);
  set locationCoeff(int value) => _locationCoeff.value = value;
  set selectedPlace(IaLocationItem? value) => _selectedPlace.value = value;
}
