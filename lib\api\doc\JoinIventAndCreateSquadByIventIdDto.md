# openapi.model.JoinIventAndCreateSquadByIventIdDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groupIds** | **List<String>** | Array of group UUIDs to invite to the ivent | [default to const []]
**userIds** | **List<String>** | Array of user UUIDs to invite to the ivent | [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



