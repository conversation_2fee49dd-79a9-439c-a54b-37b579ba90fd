//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetVibesReturn {
  /// Returns a new [GetVibesReturn] instance.
  GetVibesReturn({
    this.vibes = const [],
    required this.vibeCount,
  });

  /// List of vibes
  List<VibeItem> vibes;

  /// Total number of vibes
  ///
  /// Minimum value: 0
  int vibeCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetVibesReturn &&
    _deepEquality.equals(other.vibes, vibes) &&
    other.vibeCount == vibeCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (vibes.hashCode) +
    (vibeCount.hashCode);

  @override
  String toString() => 'GetVibesReturn[vibes=$vibes, vibeCount=$vibeCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'vibes'] = this.vibes;
      json[r'vibeCount'] = this.vibeCount;
    return json;
  }

  /// Returns a new [GetVibesReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetVibesReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetVibesReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetVibesReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetVibesReturn(
        vibes: VibeItem.listFromJson(json[r'vibes']),
        vibeCount: mapValueOfType<int>(json, r'vibeCount')!,
      );
    }
    return null;
  }

  static List<GetVibesReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetVibesReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetVibesReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetVibesReturn> mapFromJson(dynamic json) {
    final map = <String, GetVibesReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetVibesReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetVibesReturn-objects as value to a dart map
  static Map<String, List<GetVibesReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetVibesReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetVibesReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'vibes',
    'vibeCount',
  };
}

