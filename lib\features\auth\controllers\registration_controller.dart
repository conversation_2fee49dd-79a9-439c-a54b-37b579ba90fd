import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/features/auth/controllers/base_auth_controller.dart';

class RegistrationController extends BaseAuthController {
  RegistrationController(AuthService authService, AuthStateManager state) : super(authService, state);

  final _fullname = ''.obs;
  final _checkedHobbyIds = <String>[].obs;
  final _registerReturn = Rxn<RegisterReturn>();

  String get fullname => _fullname.value;
  List<String> get checkedHobbyIds => _checkedHobbyIds;
  RegisterReturn? get registerReturn => _registerReturn.value;

  bool get isFullnameValid =>
      fullname.trim().length >= AuthValidationConstants.fullNameMinLength &&
      fullname.trim().length <= AuthValidationConstants.fullNameMaxLength;

  bool get areHobbiesValid => checkedHobbyIds.length >= AuthValidationConstants.minRequiredHobbies;
  bool get isRegistrationDataValid => isFullnameValid && areHobbiesValid && state.isPhoneNumberValid;
  bool get isRegistrationSuccessful => registerReturn != null;

  set fullname(String value) => _fullname.value = value.trim();
  set checkedHobbyIds(List<String> value) => _checkedHobbyIds.assignAll(value);
  set registerReturn(RegisterReturn? value) => _registerReturn.value = value;

  void addHobby(String hobbyId) {
    if (!_checkedHobbyIds.contains(hobbyId)) {
      _checkedHobbyIds.add(hobbyId);
    }
  }

  void removeHobby(String hobbyId) {
    _checkedHobbyIds.remove(hobbyId);
  }

  void toggleHobby(String hobbyId) {
    if (_checkedHobbyIds.contains(hobbyId)) {
      removeHobby(hobbyId);
    } else {
      addHobby(hobbyId);
    }
  }

  Future<void> registerUser() async {
    try {
      setLoadingState(true);

      if (!isRegistrationDataValid) {
        throw Exception(_getValidationErrorMessage());
      }

      final registerDto = RegisterDto(
        fullname: fullname,
        phoneNumber: state.formattedPhoneNumber,
        hobbyIds: checkedHobbyIds,
      );

      registerReturn = await authService.usersApi.register(registerDto);

      if (registerReturn != null) {
        await _createUserSession();
      } else {
        throw Exception('Kayıt işlemi başarısız oldu');
      }

      setLoadingState(false);
    } catch (error) {
      handleAuthError(error, 'Kayıt sırasında bir hata oluştu');
      rethrow;
    }
  }

  void clearRegistrationData() {
    _fullname.value = '';
    _checkedHobbyIds.clear();
    _registerReturn.value = null;
    state.clearError();
  }

  Future<void> _createUserSession() async {
    final result = registerReturn!;

    final sessionUser = SessionUser(
      token: result.token,
      sessionId: result.userId,
      sessionRole: result.role,
      sessionUsername: result.username,
      sessionFullname: result.fullname,
      sessionAvatarUrl: result.avatarUrl,
    );

    await authService.login(sessionUser);
  }

  String _getValidationErrorMessage() {
    if (!state.isPhoneNumberValid) {
      return 'Geçersiz telefon numarası';
    }
    if (!isFullnameValid) {
      return 'Geçersiz ad soyad';
    }
    if (!areHobbiesValid) {
      return 'En az ${AuthValidationConstants.minRequiredHobbies} ilgi alanı seçmelisiniz';
    }
    return 'Kayıt bilgileri eksik veya hatalı';
  }
}
