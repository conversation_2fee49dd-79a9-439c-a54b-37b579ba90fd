//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Privacy setting for the ivent
class IventPrivacyEnum {
  /// Instantiate a new enum with the provided [value].
  const IventPrivacyEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const private = IventPrivacyEnum._(r'private');
  static const friends = IventPrivacyEnum._(r'friends');
  static const edu = IventPrivacyEnum._(r'edu');
  static const selectedEdu = IventPrivacyEnum._(r'selected_edu');
  static const public = IventPrivacyEnum._(r'public');

  /// List of all possible values in this [enum][IventPrivacyEnum].
  static const values = <IventPrivacyEnum>[
    private,
    friends,
    edu,
    selectedEdu,
    public,
  ];

  static IventPrivacyEnum? fromJson(dynamic value) => IventPrivacyEnumTypeTransformer().decode(value);

  static List<IventPrivacyEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <IventPrivacyEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = IventPrivacyEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [IventPrivacyEnum] to String,
/// and [decode] dynamic data back to [IventPrivacyEnum].
class IventPrivacyEnumTypeTransformer {
  factory IventPrivacyEnumTypeTransformer() => _instance ??= const IventPrivacyEnumTypeTransformer._();

  const IventPrivacyEnumTypeTransformer._();

  String encode(IventPrivacyEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a IventPrivacyEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  IventPrivacyEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'private': return IventPrivacyEnum.private;
        case r'friends': return IventPrivacyEnum.friends;
        case r'edu': return IventPrivacyEnum.edu;
        case r'selected_edu': return IventPrivacyEnum.selectedEdu;
        case r'public': return IventPrivacyEnum.public;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [IventPrivacyEnumTypeTransformer] instance.
  static IventPrivacyEnumTypeTransformer? _instance;
}

