//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateMemoryDto {
  /// Returns a new [CreateMemoryDto] instance.
  CreateMemoryDto({
    required this.mediaFormat,
    this.caption,
    required this.squadId,
  });

  MediaFormatEnum mediaFormat;

  /// Caption or description for the memory
  String? caption;

  String squadId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateMemoryDto &&
    other.mediaFormat == mediaFormat &&
    other.caption == caption &&
    other.squadId == squadId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (mediaFormat.hashCode) +
    (caption == null ? 0 : caption!.hashCode) +
    (squadId.hashCode);

  @override
  String toString() => 'CreateMemoryDto[mediaFormat=$mediaFormat, caption=$caption, squadId=$squadId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'mediaFormat'] = this.mediaFormat;
    if (this.caption != null) {
      json[r'caption'] = this.caption;
    } else {
      json[r'caption'] = null;
    }
      json[r'squadId'] = this.squadId;
    return json;
  }

  /// Returns a new [CreateMemoryDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateMemoryDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateMemoryDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateMemoryDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateMemoryDto(
        mediaFormat: MediaFormatEnum.fromJson(json[r'mediaFormat'])!,
        caption: mapValueOfType<String>(json, r'caption'),
        squadId: mapValueOfType<String>(json, r'squadId')!,
      );
    }
    return null;
  }

  static List<CreateMemoryDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateMemoryDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateMemoryDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateMemoryDto> mapFromJson(dynamic json) {
    final map = <String, CreateMemoryDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateMemoryDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateMemoryDto-objects as value to a dart map
  static Map<String, List<CreateMemoryDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateMemoryDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateMemoryDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'mediaFormat',
    'squadId',
  };
}

