//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetLikesByVibeIdReturn {
  /// Returns a new [GetLikesByVibeIdReturn] instance.
  GetLikesByVibeIdReturn({
    this.likes = const [],
    required this.likeCount,
  });

  /// List of users who liked the vibe
  List<UserListItemWithRelationshipStatus> likes;

  /// Total number of likes on the vibe
  ///
  /// Minimum value: 0
  int likeCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetLikesByVibeIdReturn &&
    _deepEquality.equals(other.likes, likes) &&
    other.likeCount == likeCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (likes.hashCode) +
    (likeCount.hashCode);

  @override
  String toString() => 'GetLikesByVibeIdReturn[likes=$likes, likeCount=$likeCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'likes'] = this.likes;
      json[r'likeCount'] = this.likeCount;
    return json;
  }

  /// Returns a new [GetLikesByVibeIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetLikesByVibeIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetLikesByVibeIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetLikesByVibeIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetLikesByVibeIdReturn(
        likes: UserListItemWithRelationshipStatus.listFromJson(json[r'likes']),
        likeCount: mapValueOfType<int>(json, r'likeCount')!,
      );
    }
    return null;
  }

  static List<GetLikesByVibeIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetLikesByVibeIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetLikesByVibeIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetLikesByVibeIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetLikesByVibeIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetLikesByVibeIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetLikesByVibeIdReturn-objects as value to a dart map
  static Map<String, List<GetLikesByVibeIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetLikesByVibeIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetLikesByVibeIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'likes',
    'likeCount',
  };
}

