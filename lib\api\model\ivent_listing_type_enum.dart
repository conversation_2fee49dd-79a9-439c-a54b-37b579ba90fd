//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class IventListingTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const IventListingTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const joined = IventListingTypeEnum._(r'joined');
  static const created = IventListingTypeEnum._(r'created');

  /// List of all possible values in this [enum][IventListingTypeEnum].
  static const values = <IventListingTypeEnum>[
    joined,
    created,
  ];

  static IventListingTypeEnum? fromJson(dynamic value) => IventListingTypeEnumTypeTransformer().decode(value);

  static List<IventListingTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <IventListingTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = IventListingTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [IventListingTypeEnum] to String,
/// and [decode] dynamic data back to [IventListingTypeEnum].
class IventListingTypeEnumTypeTransformer {
  factory IventListingTypeEnumTypeTransformer() => _instance ??= const IventListingTypeEnumTypeTransformer._();

  const IventListingTypeEnumTypeTransformer._();

  String encode(IventListingTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a IventListingTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  IventListingTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'joined': return IventListingTypeEnum.joined;
        case r'created': return IventListingTypeEnum.created;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [IventListingTypeEnumTypeTransformer] instance.
  static IventListingTypeEnumTypeTransformer? _instance;
}

