import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_controller.dart';
import 'package:ivent_app/features/auth/widgets/common/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/form/validation_code_widget.dart';

class ValidatePhone extends StatefulWidget {
  const ValidatePhone({super.key});

  @override
  State<ValidatePhone> createState() => _ValidatePhoneState();
}

class _ValidatePhoneState extends State<ValidatePhone> {
  late final AuthController _controller;

  bool _canContinueToNextPage = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          _buildInfoText(),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          _buildValidationCodeInput(),
          const Spacer(),
          _buildContinueButton(),
        ],
      ),
    );
  }

  Widget _buildInfoText() {
    return const AuthInfoTextWidget(
      text: AuthStrings.koduGirebilirsin,
    );
  }

  Widget _buildValidationCodeInput() {
    return ValidationCodeWidget(
      onCompleted: _handleCodeCompleted,
      onEditingChanged: _handleEditingChanged,
      onValidationChanged: _handleValidationChanged,
    );
  }

  Widget _buildContinueButton() {
    return IaFloatingActionButton(
      isEnabled: _canContinueToNextPage,
      text: AuthStrings.devamEt,
      onPressed: _handleContinuePressed,
    );
  }

  void _handleCodeCompleted(String code) {
    _controller.validationController.validationCode = code;
  }

  void _handleEditingChanged(bool isEditing) {
    setState(() {
      _isEditing = isEditing;
      if (isEditing) {
        _canContinueToNextPage = false;
      }
    });
  }

  void _handleValidationChanged(bool isValid) {
    setState(() {
      _canContinueToNextPage = isValid && !_isEditing;
    });
  }

  Future<void> _handleContinuePressed() async {
    if (!_canContinueToNextPage || !_controller.canProceedInAuthFlow()) {
      return;
    }

    try {
      await _controller.validationController.validateUser();

      final validateReturn = _controller.validationController.validateReturn;
      if (validateReturn == null) {
        _controller.goToSomethingWentWrongPage();
        return;
      }

      if (validateReturn.type == AuthEnum.login) {
        await _controller.validationController.loginUser();
        _controller.goToBottomNavBar();
      } else {
        _controller.goToNamePage();
      }
    } catch (error) {
      print('Validation error: $error');
    }
  }

  void _initializeController() {
    _controller = Get.find<AuthController>();
  }
}
