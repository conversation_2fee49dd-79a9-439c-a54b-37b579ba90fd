import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/app/routes/profile.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';

class IventDetailParticipants extends StatelessWidget {
  final TextEditingController _searchBarController = TextEditingController();
  final String iventId;

  IventDetailParticipants(this.iventId, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final IventDetailsController controller = Get.find(tag: iventId);

    return IaScaffold.search(
      title: controller.iventInfoController.iventPage!.iventName,
      textEditingController: _searchBarController,
      body: Obx(() {
        final pageContext = controller.participantsController.participants;
        if (pageContext == null) {
          return const IaLoadingIndicator();
        }
        return ListView.separated(
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: pageContext.userCount,
          itemBuilder: (context, index) {
            final element = pageContext.users[index];
            return IaListTile.withImageUrl(
              onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: element.userId),
              avatarUrl: element.avatarUrl,
              title: '@${element.username}',
              subtitle: element.university,
              trailing: Text('Etkinliğe Katılıyor', style: AppTextStyles.size12RegularTextSecondary),
            );
          },
          separatorBuilder: IaListTile.separatorBuilder20,
        );
      }),
    );
  }
}
