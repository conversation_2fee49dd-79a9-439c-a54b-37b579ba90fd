import 'package:geolocator/geolocator.dart' as geo;
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/mapbox/controllers/marker_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/features/mapbox/models/map_bounds.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

/// Controller for managing Mapbox map functionality and user location
///
/// Handles map initialization, user location tracking, camera movements,
/// and coordinate bounds management. Integrates with MarkerController
/// for marker management functionality.
class MapboxController {
  // Dependencies
  final AuthService authService;
  final Function(List<MarkerFeature> selectedFeatures)? onSelectedFeaturesChanged;

  // Controllers
  late final MarkerController markerController;
  late final MapboxMap mapboxMap;

  // State
  bool isMapboxMapInitialized = false;

  // Reactive state
  final _currentMapBounds = Rxn<MapBounds>();
  final _userLocationCoordinates = Rxn<Position>();
  final _userLocation = Rxn<IaLocationItem>();

  // Constructor
  MapboxController({
    required this.authService,
    this.onSelectedFeaturesChanged,
  }) {
    markerController = MarkerController(onSelectedFeaturesChanged: onSelectedFeaturesChanged);
    getUserLocationCoordinates();
  }

  // Getters
  MapBounds? get currentMapBounds => _currentMapBounds.value;
  Position? get userLocationCoordinates => _userLocationCoordinates.value;
  IaLocationItem? get userLocation => _userLocation.value;

  // Setters
  set userLocationCoordinates(Position? value) => _userLocationCoordinates.value = value;
  set userLocation(IaLocationItem? value) => _userLocation.value = value;

  // Public methods

  /// Initializes the Mapbox map instance
  void setMapboxMap(MapboxMap mapboxMap) {
    if (isMapboxMapInitialized) return;
    this.mapboxMap = mapboxMap;
    markerController.setMapboxMap(mapboxMap);
    isMapboxMapInitialized = true;
  }

  /// Gets user's current location coordinates and updates location info
  Future<void> getUserLocationCoordinates() async {
    final status = await Permission.locationWhenInUse.request();
    if (!status.isGranted) return;

    final position = await geo.Geolocator.getCurrentPosition();
    userLocationCoordinates = Position(position.longitude, position.latitude);
    await _getUserLocationInfo();
  }

  /// Moves camera to user's current location
  Future<void> moveCameraToUserLocation() async {
    if (!isMapboxMapInitialized) return;
    await getUserLocationCoordinates();
    if (userLocationCoordinates == null) return;

    await mapboxMap.setCamera(
      CameraOptions(
        center: Point(coordinates: userLocationCoordinates!),
        zoom: 14,
      ),
    );
  }

  /// Updates the visible map bounds based on current camera state
  Future<void> updateVisibleMapBounds() async {
    if (!isMapboxMapInitialized) return;

    final newCameraState = await mapboxMap.getCameraState();
    final cameraOptions = CameraOptions(
      center: newCameraState.center,
      zoom: newCameraState.zoom,
      bearing: newCameraState.bearing,
      pitch: newCameraState.pitch,
    );

    final bounds = await mapboxMap.coordinateBoundsForCamera(cameraOptions);
    _currentMapBounds.value = MapBounds.fromCoordinateBounds(bounds);
  }

  // Private methods

  /// Retrieves location information using reverse geocoding
  Future<void> _getUserLocationInfo() async {
    if (userLocationCoordinates == null) return;

    final result = await authService.mapboxApi.searchBoxReverse(
      userLocationCoordinates!.lng.toDouble(),
      userLocationCoordinates!.lat.toDouble(),
    );

    if (result != null && result.features.isNotEmpty) {
      userLocation = IaLocationItem.fromProperties(result.features.first.properties);
    }
  }
}
