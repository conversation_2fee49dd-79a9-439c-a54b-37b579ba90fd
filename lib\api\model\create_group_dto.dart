//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateGroupDto {
  /// Returns a new [CreateGroupDto] instance.
  CreateGroupDto({
    required this.groupName,
    this.thumbnailBuffer,
    this.userIds = const [],
  });

  /// Group name can only contain letters, numbers, underscores, hyphens, and periods
  String groupName;

  /// Base64 encoded thumbnail image buffer for the group
  String? thumbnailBuffer;

  /// Array of user UUIDs to add as group members
  List<String> userIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateGroupDto &&
    other.groupName == groupName &&
    other.thumbnailBuffer == thumbnailBuffer &&
    _deepEquality.equals(other.userIds, userIds);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (groupName.hashCode) +
    (thumbnailBuffer == null ? 0 : thumbnailBuffer!.hashCode) +
    (userIds.hashCode);

  @override
  String toString() => 'CreateGroupDto[groupName=$groupName, thumbnailBuffer=$thumbnailBuffer, userIds=$userIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'groupName'] = this.groupName;
    if (this.thumbnailBuffer != null) {
      json[r'thumbnailBuffer'] = this.thumbnailBuffer;
    } else {
      json[r'thumbnailBuffer'] = null;
    }
      json[r'userIds'] = this.userIds;
    return json;
  }

  /// Returns a new [CreateGroupDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateGroupDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateGroupDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateGroupDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateGroupDto(
        groupName: mapValueOfType<String>(json, r'groupName')!,
        thumbnailBuffer: mapValueOfType<String>(json, r'thumbnailBuffer'),
        userIds: json[r'userIds'] is Iterable
            ? (json[r'userIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<CreateGroupDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateGroupDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateGroupDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateGroupDto> mapFromJson(dynamic json) {
    final map = <String, CreateGroupDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateGroupDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateGroupDto-objects as value to a dart map
  static Map<String, List<CreateGroupDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateGroupDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateGroupDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'groupName',
    'userIds',
  };
}

