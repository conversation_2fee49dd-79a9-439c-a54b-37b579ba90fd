//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetCommentsByVibeIdReturn {
  /// Returns a new [GetCommentsByVibeIdReturn] instance.
  GetCommentsByVibeIdReturn({
    this.comments = const [],
    required this.commentCount,
  });

  /// List of comments on the vibe
  List<CommentItem> comments;

  /// Total number of comments on the vibe
  ///
  /// Minimum value: 0
  int commentCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetCommentsByVibeIdReturn &&
    _deepEquality.equals(other.comments, comments) &&
    other.commentCount == commentCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (comments.hashCode) +
    (commentCount.hashCode);

  @override
  String toString() => 'GetCommentsByVibeIdReturn[comments=$comments, commentCount=$commentCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'comments'] = this.comments;
      json[r'commentCount'] = this.commentCount;
    return json;
  }

  /// Returns a new [GetCommentsByVibeIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetCommentsByVibeIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetCommentsByVibeIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetCommentsByVibeIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetCommentsByVibeIdReturn(
        comments: CommentItem.listFromJson(json[r'comments']),
        commentCount: mapValueOfType<int>(json, r'commentCount')!,
      );
    }
    return null;
  }

  static List<GetCommentsByVibeIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetCommentsByVibeIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetCommentsByVibeIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetCommentsByVibeIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetCommentsByVibeIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetCommentsByVibeIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetCommentsByVibeIdReturn-objects as value to a dart map
  static Map<String, List<GetCommentsByVibeIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetCommentsByVibeIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetCommentsByVibeIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'comments',
    'commentCount',
  };
}

