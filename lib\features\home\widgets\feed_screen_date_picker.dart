import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/features/home/<USER>/calendar.dart';

class FeedScreenDatePicker extends StatefulWidget {
  final Function(DateTime startDate, DateTime endDate) onDateRangeSelected;
  final Function(int index) onDateButtonPressed;

  const FeedScreenDatePicker({
    super.key,
    required this.onDateRangeSelected,
    required this.onDateButtonPressed,
  });

  @override
  State<FeedScreenDatePicker> createState() => _FeedScreenDatePickerState();
}

class _FeedScreenDatePickerState extends State<FeedScreenDatePicker> {
  final List<String> dateOptions = [
    'Zaman Aralığı Seç',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>u Hafta',
    'Haftaya',
    '<PERSON>u <PERSON><PERSON>',
    '<PERSON>u <PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
  ];

  bool _isCalendarVisible = false;
  int? _selectedIndex = null;
  List<DateTime?> _selectedDates = [];

  void _toggleCalendarVisibility() => setState(() => _isCalendarVisible = !_isCalendarVisible);

  void _onDateButtonPressed(int index) {
    setState(() {
      _selectedIndex = index;

      if (index == 0) {
        _onCalenderButtonPressed();
      } else {
        if (_isCalendarVisible) {
          _toggleCalendarVisibility();
        }
        if (index != 1) {
          _selectedDates = [];
        }
      }
    });

    widget.onDateButtonPressed(index);
  }

  void _onCalenderButtonPressed() {
    if (_isCalendarVisible && _selectedDates.length == 2) {
      widget.onDateRangeSelected(_selectedDates[0]!, _selectedDates[1]!);
    } else if (_isCalendarVisible) {
      _selectedIndex = null;
    }
    _toggleCalendarVisibility();
  }

  void _applySelectedDates() {
    _toggleCalendarVisibility();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Column(
        children: [
          SizedBox(
            height: AppDimensions.buttonHeightDateFilter,
            child: ListView.separated(
              clipBehavior: Clip.none,
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
              itemCount: dateOptions.length,
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext context, index) {
                return HomeButtons.dateFilter(
                  onTap: () => _onDateButtonPressed(index),
                  isSelected: _selectedIndex == index,
                  text: dateOptions[index],
                  iconPath: index == 0 ? AppAssets.calendar : null,
                );
              },
              separatorBuilder: (BuildContext context, int index) => const SizedBox(width: AppDimensions.padding8),
            ),
          ),
          if (_isCalendarVisible) const SizedBox(height: AppDimensions.padding20),
          if (_isCalendarVisible)
            Calendar(
              selectedDates: _selectedDates,
              onDateRangeSelected: (dates) => setState(() => _selectedDates = dates),
              onDateRangeAccepted: _applySelectedDates,
            ),
        ],
      ),
    );
  }
}
