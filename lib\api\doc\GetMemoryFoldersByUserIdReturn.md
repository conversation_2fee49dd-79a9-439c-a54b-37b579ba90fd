# openapi.model.GetMemoryFoldersByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**memoryFolders** | [**List<MemoryFolderCardItem>**](MemoryFolderCardItem.md) | List of user's memory folders | [default to const []]
**memoryFolderCount** | **int** | Total number of memory folders | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



