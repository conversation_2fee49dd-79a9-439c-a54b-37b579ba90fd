//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// User education verification status
class UserEduVerificationEnum {
  /// Instantiate a new enum with the provided [value].
  const UserEduVerificationEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const unverified = UserEduVerificationEnum._(r'unverified');
  static const student = UserEduVerificationEnum._(r'student');
  static const grad = UserEduVerificationEnum._(r'grad');

  /// List of all possible values in this [enum][UserEduVerificationEnum].
  static const values = <UserEduVerificationEnum>[
    unverified,
    student,
    grad,
  ];

  static UserEduVerificationEnum? fromJson(dynamic value) => UserEduVerificationEnumTypeTransformer().decode(value);

  static List<UserEduVerificationEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UserEduVerificationEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UserEduVerificationEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [UserEduVerificationEnum] to String,
/// and [decode] dynamic data back to [UserEduVerificationEnum].
class UserEduVerificationEnumTypeTransformer {
  factory UserEduVerificationEnumTypeTransformer() => _instance ??= const UserEduVerificationEnumTypeTransformer._();

  const UserEduVerificationEnumTypeTransformer._();

  String encode(UserEduVerificationEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a UserEduVerificationEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  UserEduVerificationEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'unverified': return UserEduVerificationEnum.unverified;
        case r'student': return UserEduVerificationEnum.student;
        case r'grad': return UserEduVerificationEnum.grad;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [UserEduVerificationEnumTypeTransformer] instance.
  static UserEduVerificationEnumTypeTransformer? _instance;
}

