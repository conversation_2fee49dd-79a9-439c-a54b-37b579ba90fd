class Hobby {
  final String hobbyName;
  final String hobbyId;
  final String? parentHobbyName;
  final String? parentHobbyId;
  final int level;

  Hobby({
    required this.hobbyName,
    required this.hobbyId,
    this.parentHobbyName,
    this.parentHobbyId,
    required this.level,
  });

  static String getHobbyNameFromHobbyId(String _hobbyId) {
    return hobbyList.firstWhere((element) => element.hobbyId == _hobbyId).hobbyName;
  }

  static List<Hobby> getHobbiesFromHobbyIds(List<String> _hobbyId) {
    return _hobbyId.map((e) => hobbyList.firstWhere((element) => element.hobbyId == e)).toList();
  }

  static String? getParentHobbyNameFromHobbyId(String _hobbyId) {
    return hobbyList.firstWhere((element) => element.hobbyId == _hobbyId).parentHobbyName;
  }

  static String? getParentHobbyIdFromHobbyId(String _hobbyId) {
    return hobbyList.firstWhere((element) => element.hobbyId == _hobbyId).parentHobbyId;
  }

  static String getHobbyIdFromHobbyName(String _hobbyName) {
    return hobbyList.firstWhere((element) => element.hobbyName == _hobbyName).hobbyId;
  }

  static String? getParentHobbyNameFromHobbyName(String _hobbyName) {
    return hobbyList.firstWhere((element) => element.hobbyName == _hobbyName).parentHobbyName;
  }

  static String? getParentHobbyIdFromHobbyName(String _hobbyName) {
    return hobbyList.firstWhere((element) => element.hobbyId == _hobbyName).parentHobbyId;
  }

  static List<Hobby> getHobbiesOfTheParentHobby(String _parentHobbyName) {
    return hobbyList.where((element) => element.parentHobbyName == _parentHobbyName).toList();
  }

  static List<Hobby> getHobbiesOfTheLevel(int _level) {
    return hobbyList.where((element) => element.level == _level).toList();
  }

  static List<Hobby> filterHobbies({
    String? parentHobbyName,
    String? parentHobbyId,
    int? level,
    int? limit,
  }) {
    var filtered = hobbyList.where((Hobby element) {
      if (parentHobbyName != null && parentHobbyName != element.parentHobbyName) return false;
      if (parentHobbyId != null && parentHobbyId != element.parentHobbyId) return false;
      if (level != null && level != element.level) return false;
      return true;
    }).toList();
    if (limit != null) filtered = filtered.sublist(0, (filtered.length > limit ? limit : filtered.length));
    return filtered;
  }

  static Map<String, List<Hobby>> hobbyListByParentHobbyName = {
    'Sanat & Kültür': Hobby.filterHobbies(parentHobbyName: 'Sanat & Kültür'),
    'Müzik': Hobby.filterHobbies(parentHobbyName: 'Müzik'),
    'Spor': Hobby.filterHobbies(parentHobbyName: 'Spor'),
    'Yeme İçme': Hobby.filterHobbies(parentHobbyName: 'Yeme İçme'),
    'Toplum': Hobby.filterHobbies(parentHobbyName: 'Toplum'),
    'Kariyer & Akademik': Hobby.filterHobbies(parentHobbyName: 'Kariyer & Akademik'),
  };

  static List<Hobby> hobbyList = [
    Hobby(hobbyName: 'Kariyer & Akademik', hobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7', level: 1),
    Hobby(hobbyName: 'Müzik', hobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b', level: 1),
    Hobby(hobbyName: 'Sanat & Kültür', hobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db', level: 1),
    Hobby(hobbyName: 'Spor', hobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3', level: 1),
    Hobby(hobbyName: 'Toplum', hobbyId: '53d2a5b5-c145-4d2a-bbf8-aca3d890afbd', level: 1),
    Hobby(hobbyName: 'Yeme İçme', hobbyId: '76e48bce-aa41-4a16-8653-817f3f7cef03', level: 1),
    Hobby(
      hobbyName: 'Güzel Sanatlar & Konservatuar',
      hobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Networking ve Kişisel Gelişim',
      hobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Sağlık Bilimleri',
      hobbyId: 'd16ad40d-1da2-4449-89fb-8cd86bf00bbd',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Sosyal Bilimler',
      hobbyId: '7168b65f-275e-4074-9b75-6781c964320e',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Teknoloji ve Mühendislik',
      hobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Temel Bilimler',
      hobbyId: '3e3178b4-ce21-460f-b64e-9536d33f7696',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Yönetim Bilimleri',
      hobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      parentHobbyName: 'Kariyer & Akademik',
      parentHobbyId: 'e58ef689-a6b6-4b77-805e-31236dccb7c7',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Blues',
      hobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Hiphop ve Drill',
      hobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Jam Session',
      hobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Jazz',
      hobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Klasik Müzik',
      hobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Parti',
      hobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Pop',
      hobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Rock & Metal',
      hobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Techno ve EDM',
      hobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Türk Müziği',
      hobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      parentHobbyName: 'Müzik',
      parentHobbyId: 'e7545352-ef3a-4a42-93dc-987ec1fa641b',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Gezi ve Keşif',
      hobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      parentHobbyName: 'Sanat & Kültür',
      parentHobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Görsel Sanatlar',
      hobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      parentHobbyName: 'Sanat & Kültür',
      parentHobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Moda ve Güzellik',
      hobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      parentHobbyName: 'Sanat & Kültür',
      parentHobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Sahne Sanatları',
      hobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      parentHobbyName: 'Sanat & Kültür',
      parentHobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Sinema ve Film',
      hobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      parentHobbyName: 'Sanat & Kültür',
      parentHobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Yazı ve Edebiyat',
      hobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      parentHobbyName: 'Sanat & Kültür',
      parentHobbyId: '4c3d5b8e-194b-4c3c-bd37-a0b26117f4db',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Bireysel Sporlar',
      hobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Fitness ve Sağlık',
      hobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Kış Sporları',
      hobbyId: '9325347e-3cff-4784-9c2c-1c19a39ce5d8',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Motorsporları',
      hobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Outdoor Sporlar',
      hobbyId: 'c7c575bd-1a73-4709-a37e-c2aa63edc6f1',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Savunma Sanatları',
      hobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Su Sporları',
      hobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Takım Sporları',
      hobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Tekerlekli Sporlar',
      hobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      parentHobbyName: 'Spor',
      parentHobbyId: '22c34c46-3914-4212-9d3c-c245ce7b61c3',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Sosyal Sorumluluk',
      hobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      parentHobbyName: 'Toplum',
      parentHobbyId: '53d2a5b5-c145-4d2a-bbf8-aca3d890afbd',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Toplumsal Dayanışma',
      hobbyId: '1a5f33e0-abe3-425a-907c-3216e0491a5f',
      parentHobbyName: 'Toplum',
      parentHobbyId: '53d2a5b5-c145-4d2a-bbf8-aca3d890afbd',
      level: 2,
    ),
    Hobby(
      hobbyName: 'İçme',
      hobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      parentHobbyName: 'Yeme İçme',
      parentHobbyId: '76e48bce-aa41-4a16-8653-817f3f7cef03',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Yeme',
      hobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      parentHobbyName: 'Yeme İçme',
      parentHobbyId: '76e48bce-aa41-4a16-8653-817f3f7cef03',
      level: 2,
    ),
    Hobby(
      hobbyName: 'Atletizm',
      hobbyId: 'f2978903-9d2a-4ba1-8adf-10615087c40d',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bilardo',
      hobbyId: '6de0b544-1b1e-417b-967b-240cea73d493',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Binicilik',
      hobbyId: '01a6c391-1aaa-4fa5-8e9a-a7e4d722b0a5',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Golf',
      hobbyId: '1ea9065a-6f80-41ef-9579-3797c8d17066',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Koşu',
      hobbyId: '451a014a-7951-48fb-a7f5-9b44b852eb02',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Masa Tenisi',
      hobbyId: '43073563-1a66-455c-bcf6-256ade39950f',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Okçuluk',
      hobbyId: '85b2c505-7426-492c-84f2-f486b1de6df3',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tenis',
      hobbyId: 'ff87b914-f931-487e-a918-efcc8c463a9d',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yüzme',
      hobbyId: '6856a957-f73e-4db2-905d-addcaca9d1de',
      parentHobbyName: 'Bireysel Sporlar',
      parentHobbyId: '77dfecc2-ec47-4da6-878a-7185f310116f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Akustik Gitar',
      hobbyId: '25e948ea-3c35-41c1-8d90-3e159e8eca4d',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bas Gitar',
      hobbyId: 'b4a05017-524d-4e2c-9ea0-319957b00447',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bateri',
      hobbyId: 'ffa9a568-71e9-41c4-8626-b1f78567b0bc',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Blues',
      hobbyId: 'de535ca8-ea5e-47dd-bd10-98cd62dce777',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Davul',
      hobbyId: '1eb7eb29-d3e7-4faa-b8ea-c48f2ae38dfc',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Elektro Gitar',
      hobbyId: '25793a26-b754-4113-a933-172a292eebb7',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klavye/Piyano',
      hobbyId: 'd0102db0-ec92-4f15-bff2-2d7353797412',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Üflemeli Çalgı',
      hobbyId: '11095f19-f425-4b71-b985-67fb598648a3',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'f3f604e4-0d4a-4563-8e7d-469146ed3874',
      parentHobbyName: 'Blues',
      parentHobbyId: '383a0e09-0549-4fe5-a6b1-f1618366bc4f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bodybuilding',
      hobbyId: '8b1afb1f-9608-42d4-ba38-364af9eccc06',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Cross Fit',
      hobbyId: '0dffd064-5764-44fc-bd55-f59b6f9445de',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dayanıklılık',
      hobbyId: 'a1824e33-d240-45c9-a4ef-83c25e0d946e',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fitness',
      hobbyId: '407503da-d3da-42a9-bcf9-b182a4904d04',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Meditasyon',
      hobbyId: '72124737-4a2f-48df-8ad7-df90f93d63a6',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Mindfullness',
      hobbyId: 'd0e6a9a5-e82c-41e4-983a-37a3aa058794',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Pilates',
      hobbyId: 'ee6cadce-fc47-42b3-ab8d-09c7ec69a510',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Powerlifting',
      hobbyId: '8a6df417-079a-4667-bbb8-bb6ea1dd51f8',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sağlıklı Yaşam',
      hobbyId: 'a9cb99fc-76bb-498e-8214-79d5f57a8d6d',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Spa',
      hobbyId: '4708b0a5-ba28-4af9-a6c1-b16594dbc9eb',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yoga',
      hobbyId: '721a5412-7f6f-418d-a260-6074075b1194',
      parentHobbyName: 'Fitness ve Sağlık',
      parentHobbyId: 'cdf2bc67-9f56-4a1c-95a4-d66334f7744e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Arkeoloji',
      hobbyId: 'e9f8a7f9-9321-4ff5-a02b-5890c6768efa',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Coğrafi Güzellikler',
      hobbyId: 'b796b3fb-bc57-43d9-9941-bbce48f428be',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Doğa ve Macera',
      hobbyId: '1c704f28-7689-4bd3-a746-f9e420a82ab8',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dünya Mirası Alanları',
      hobbyId: 'be93c2e7-3897-428e-8fd7-95101d0ced9c',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kültür',
      hobbyId: 'cd81989b-9f5c-4df5-8edc-52223a0663f9',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Müze',
      hobbyId: '2009255c-f742-4ebc-a2aa-174088daa7f6',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sehir ve Kültür',
      hobbyId: '663190ac-1ad3-4b18-9bef-6b3c63df341b',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tarih',
      hobbyId: '00f06a71-39ae-4c41-be69-686c9db4b481',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Turizm',
      hobbyId: '7866c630-83de-465a-a289-275a9f9e343f',
      parentHobbyName: 'Gezi ve Keşif',
      parentHobbyId: '9063bd74-15ac-45bf-b74f-f3cc4b67ef55',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fotoğrafçılık',
      hobbyId: '9bf38f7b-dd74-4170-8539-52aec2a8ecff',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Görsel Sanatlar',
      hobbyId: '5b045765-09f5-4427-b85c-19ad013b04a7',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Grafik Tasarım',
      hobbyId: 'da6b5cf9-b036-45f0-90f7-d6838f69220a',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Heykel',
      hobbyId: '4b9f79c4-d824-4a23-a806-4dc05a884336',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'İlüstrasyon',
      hobbyId: 'a89f0159-baee-4857-9068-f0ae325221ec',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Resim',
      hobbyId: 'e77453c7-b78a-4bd2-80fa-a7d1c63ae1ef',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sanat Sepet',
      hobbyId: 'e1dbebed-57ca-4697-824e-2600f64b620e',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Seramik & Çömlek',
      hobbyId: 'e5f841db-1a5d-4be5-a646-1af6dfe8fb5d',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Video Sanatı',
      hobbyId: 'fcaa2790-7574-4afb-ae5f-5dfb2698aa06',
      parentHobbyName: 'Görsel Sanatlar',
      parentHobbyId: '75f22b90-e33f-477f-8b15-5d6249cecfa4',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Endüstriyel Tasarım',
      hobbyId: 'a6bc08d8-43d0-4ba1-9509-e8d68b8dc050',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fotoğrafçılık',
      hobbyId: 'd0685b5c-3270-4608-a2e6-116eab704847',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Grafik Tasarım',
      hobbyId: '9cb0c3a2-b734-4eb9-80d9-9f358bdb98e0',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'İç Mimarlık',
      hobbyId: 'c74b634d-9f73-4624-8d53-a9bbd0148196',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Mimarlık',
      hobbyId: 'df9c835a-e94f-4045-b3cb-31ee8a4b75c9',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Modern Sanat',
      hobbyId: '6bd1bdbf-da94-4b21-ba85-a11614bbfdab',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Müzik',
      hobbyId: 'ef7d0607-47c5-43ba-b3e7-eaf15502a321',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Müzikoloji',
      hobbyId: '04810096-383a-49a1-8d0c-631275f4105d',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Peyzaj',
      hobbyId: 'b049f060-c7d6-4d22-9366-255198afdaef',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Prodüksiyon',
      hobbyId: '0eb33a7d-d9dd-4a93-9962-043901120ddc',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sahne Sanatları',
      hobbyId: 'fccc1ff1-49c3-4843-ae10-a9d2fcacb783',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sanat Sepet',
      hobbyId: '1340383e-b0fd-40f3-9ba8-5adb7e43242e',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Şehir Planlama',
      hobbyId: '1909df05-ea3e-48c4-af20-c69fc93f6ae8',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Ses Tasarım',
      hobbyId: '845cdb6f-96a4-4f39-be8b-0883036affbe',
      parentHobbyName: 'Güzel Sanatlar & Konservatuar',
      parentHobbyId: 'd50c26bf-514b-4203-b2da-bbc680ade12a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Break Dance',
      hobbyId: 'bfedff73-28dc-491c-9b72-e837b2b6384c',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'DJ',
      hobbyId: 'dcae39ad-e625-4845-94a0-e330e652f15c',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Drill',
      hobbyId: '553fca52-6499-4c6c-a158-abd249613054',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hiphop',
      hobbyId: 'bfdb7f79-933c-404f-aee2-fcbbe3426058',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Müzik Prodüksiyonu',
      hobbyId: '11101181-7960-4513-b3f2-ad65f5876af2',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Parti',
      hobbyId: 'e4a75849-e265-4459-92af-49f91f975f89',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'R&B',
      hobbyId: 'c10db84d-43bd-4527-8719-cf9e2837e617',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sokak Kültürü',
      hobbyId: '74a73ae0-9d08-48e9-a778-e8d4f4e97213',
      parentHobbyName: 'Hiphop ve Drill',
      parentHobbyId: 'b06912db-683d-44bc-be87-7fa9eb98429e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bar',
      hobbyId: 'a8ef4af4-b84f-4e90-81f4-57b1624b1dd7',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Barista',
      hobbyId: 'f85810c4-e4b9-4e87-85d3-0fbc2c7db4d0',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Barmen',
      hobbyId: '286d3e98-ded5-4473-9111-5eff028cb5c6',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bira',
      hobbyId: 'a0c261cf-a35f-4b51-8d13-c13425fc1dd2',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Enerji İçeceği',
      hobbyId: '40221d04-85b6-49f2-84a7-cf73d3409789',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Espresso',
      hobbyId: '228f39b3-a305-486a-b81f-a7d6d8d0041d',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'İçecek',
      hobbyId: '6059c443-c4e1-40f5-b228-f66cc413c563',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kafe',
      hobbyId: '850f7646-ac82-40b9-a5c2-9ee1cd1dc8db',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kahve',
      hobbyId: 'dfbde655-86af-4f48-ae8c-ce6a9c0b2226',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kokteyl',
      hobbyId: '24cd21f5-cbcb-40e4-ae9b-e91fe3610112',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Likör',
      hobbyId: 'f59d1fc1-48e7-4417-97a4-cb305dbf8ea3',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Meyhane',
      hobbyId: 'cd2bce17-dc4f-4213-8e25-e59d2bd7c479',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Pub',
      hobbyId: '0e9d10d6-0aff-439b-955d-280bb704c360',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Rakı',
      hobbyId: 'ffc19432-3285-4d13-8a71-0c9cfe0be967',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Şarap',
      hobbyId: '05646e03-07c5-4935-80e0-97cae0f8b321',
      parentHobbyName: 'İçme',
      parentHobbyId: 'b555ef99-cccb-4a53-b27a-837e3130edf3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bas Gitar',
      hobbyId: '2351767d-f821-4f49-8c76-ef01eb08aaf1',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bateri',
      hobbyId: 'd1b02653-f525-40ba-a5c4-a310277b6f9f',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Davul',
      hobbyId: '44728388-6eac-454b-82dd-e7fafbae3a0b',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Gitar',
      hobbyId: '6f917152-a783-45df-860e-c62a6cd14277',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Jamming',
      hobbyId: 'd46e6c8a-280e-41d9-aee1-7501a6068171',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klavye/Piyano',
      hobbyId: '4e02fe93-6e7a-4e76-ad2b-23f229f9240c',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Üflemeli Çalgı',
      hobbyId: '40e4251c-5f0f-40b2-9474-0e05dbe02f71',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'dd6aa546-e7cd-421e-a64a-2c177fcf58bf',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yaylı Çalgı',
      hobbyId: '551455c0-bab3-4fbf-8a98-b98d7cc109e3',
      parentHobbyName: 'Jam Session',
      parentHobbyId: '1da3e659-d107-4e43-850d-a29298b5db9e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bas Gitar',
      hobbyId: '13676c20-cd95-488b-82d3-8d0f647c92f9',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bateri',
      hobbyId: '2267afa9-9350-4f50-ad97-9f0c1eca802e',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Davul',
      hobbyId: 'ce8ea6c4-017b-4f41-b946-79ab2774e4c9',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Jazz',
      hobbyId: 'e1839c6f-f412-44aa-a2d8-69d3ac34cdd2',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klasik Gitar',
      hobbyId: 'd754fff1-5689-4cdc-823a-2a977f3edca6',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klavye/Piyano',
      hobbyId: 'ba4a77ba-9600-4503-9930-341e3c16aa91',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Üflemeli Çalgı',
      hobbyId: '577528cf-cc8d-4e72-a83c-b3317d6d454f',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'f2bf1a6a-cb90-4333-92db-76480ccc0399',
      parentHobbyName: 'Jazz',
      parentHobbyId: '4842d60b-983c-4b88-ac64-17be8d960fd7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Buz Hokeyi',
      hobbyId: 'b74faa24-7fdd-468d-9a62-655f6aee8c80',
      parentHobbyName: 'Kış Sporları',
      parentHobbyId: '9325347e-3cff-4784-9c2c-1c19a39ce5d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Buz Pateni',
      hobbyId: 'c2ee9376-00c0-4d2f-8bf0-5b183e72a316',
      parentHobbyName: 'Kış Sporları',
      parentHobbyId: '9325347e-3cff-4784-9c2c-1c19a39ce5d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kayak',
      hobbyId: 'a1e0ce98-f9d2-4efb-8777-3cd4da1fdc53',
      parentHobbyName: 'Kış Sporları',
      parentHobbyId: '9325347e-3cff-4784-9c2c-1c19a39ce5d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Snowboard',
      hobbyId: '65a42612-8b04-4bcb-acb5-55ae6ca6dc4c',
      parentHobbyName: 'Kış Sporları',
      parentHobbyId: '9325347e-3cff-4784-9c2c-1c19a39ce5d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bas Gitar',
      hobbyId: 'bf920513-95f8-4fcc-abdd-499c07883b34',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klasik Gitar',
      hobbyId: 'b9276558-1ad7-440f-a596-0439529f909b',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klasik Müzik',
      hobbyId: '956df2b9-4516-4116-81ba-9e41029d9dad',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klavye/Piyano',
      hobbyId: 'ee28f835-719e-4996-bd18-85b29a2b7ae8',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Üflemeli Çalgı',
      hobbyId: '72e8061b-a8ac-4c6c-930d-7ddb408e4fdf',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vurmalı Çalgı',
      hobbyId: 'b0d6c8be-5e08-4bd4-9c2c-6e65d51616ca',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yaylı Çalgı',
      hobbyId: '785308da-b7a2-4f00-a2f2-a142702a75ee',
      parentHobbyName: 'Klasik Müzik',
      parentHobbyId: '99a82632-70cc-4aed-932a-d834323665a8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Giyim',
      hobbyId: '880ee5ae-8c01-4301-8ed1-c48d89300330',
      parentHobbyName: 'Moda ve Güzellik',
      parentHobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kozmetik',
      hobbyId: '8b78931f-aeb4-4c56-b727-f686b2f1f6eb',
      parentHobbyName: 'Moda ve Güzellik',
      parentHobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Moda',
      hobbyId: 'df835448-d02e-4dc5-97ca-72a3bc72a581',
      parentHobbyName: 'Moda ve Güzellik',
      parentHobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Moda Tasarım',
      hobbyId: 'ed730dda-80e4-4579-a8ff-0b2b058bfeaf',
      parentHobbyName: 'Moda ve Güzellik',
      parentHobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Moda ve Güzellik',
      hobbyId: '26c99677-d6bc-4621-8941-595f2aa3e8a8',
      parentHobbyName: 'Moda ve Güzellik',
      parentHobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Stil',
      hobbyId: '41848d73-f923-49c7-b7e5-f9132183ece0',
      parentHobbyName: 'Moda ve Güzellik',
      parentHobbyId: '94db6c7a-2713-4758-840c-43e2f2a37b04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Cruiser Motosiklet',
      hobbyId: 'a0c6801a-c919-413b-aa12-d92d1f19505c',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Enduro',
      hobbyId: '75aa548e-744d-4ec5-9f68-66a55226225f',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Formula 1',
      hobbyId: '4a59521a-7775-43d0-ad26-3687f0016a01',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Motocross',
      hobbyId: '54dba20d-e50e-4e49-8eef-86d68b55205f',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Motosiklet',
      hobbyId: '8c9b7089-6ffc-4ad6-a757-20fb5a1f0e96',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Off-Road',
      hobbyId: 'edc6ed90-7a93-4538-b3bb-32c4e46785cb',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Ralli',
      hobbyId: '08c95992-6ccf-4d31-b3e9-f9e2efd01494',
      parentHobbyName: 'Motorsporları',
      parentHobbyId: '282a5692-6cb9-4a87-a9d1-da7f7458cb68',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bilinçlilik',
      hobbyId: '3af6506d-d8fe-4d93-a5d2-7849da8196c7',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Farkındalık',
      hobbyId: '5bb295a3-3ed1-496e-91a1-34b3f8fa3f01',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Felsefe',
      hobbyId: 'f4fee597-d1c0-4e0f-9cd5-f634cd509de0',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fuar',
      hobbyId: '90f57a4a-2dd9-4216-b19c-7d9eecb87125',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kişisel Gelişim',
      hobbyId: '1346b575-8cf1-468e-b3ea-d1513cc0f3be',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Konferans',
      hobbyId: '64e3c82f-cae3-4d25-a18c-9f8a99a82c01',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Mindfullnes',
      hobbyId: '228a50ac-790b-4649-8a45-9adde790cd1a',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Münazara',
      hobbyId: '37184063-f5e8-44cb-a2a9-a502cf163dfb',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Networking',
      hobbyId: 'f2a2643a-5183-4d87-bd07-4893131f5c66',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Oturum',
      hobbyId: '21263203-3a5e-45f2-91dd-c902c2b0c536',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Psikoloji',
      hobbyId: 'c3ea641e-16d6-4664-9b32-a811094cbc4e',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Zirve',
      hobbyId: 'a5fff9cd-98ae-4778-a84b-4e4a79adf7ef',
      parentHobbyName: 'Networking ve Kişisel Gelişim',
      parentHobbyId: 'dbeaa3c5-ba73-494b-861f-3fe76ea5edb9',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Camping',
      hobbyId: '594b17ab-3347-42bb-a0d9-386bb6908724',
      parentHobbyName: 'Outdoor Sporlar',
      parentHobbyId: 'c7c575bd-1a73-4709-a37e-c2aa63edc6f1',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dağcılık',
      hobbyId: '8c16b32e-5fcc-4e44-8045-b03d13b5ec80',
      parentHobbyName: 'Outdoor Sporlar',
      parentHobbyId: 'c7c575bd-1a73-4709-a37e-c2aa63edc6f1',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hiking',
      hobbyId: '803ed658-db6d-49a0-aad3-3f2d7b979a95',
      parentHobbyName: 'Outdoor Sporlar',
      parentHobbyId: 'c7c575bd-1a73-4709-a37e-c2aa63edc6f1',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Paraşüt',
      hobbyId: 'a71d4288-c8e4-490e-a6c6-cc031e6497e5',
      parentHobbyName: 'Outdoor Sporlar',
      parentHobbyId: 'c7c575bd-1a73-4709-a37e-c2aa63edc6f1',
      level: 3,
    ),
    Hobby(
      hobbyName: "80'ler 90'lar",
      hobbyId: '982f96d4-a91d-4bc2-8fe1-3d15a2af9df3',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bar',
      hobbyId: 'c18049e5-77a3-4d3e-857e-d5d8c379949b',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'DJ',
      hobbyId: 'f8745890-fc09-4fbd-9527-f0205bda66a3',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Gece Hayatı',
      hobbyId: 'a8ca43d6-67eb-46dd-8bef-05d511dafd01',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Parti',
      hobbyId: '7b8f5b29-3a9b-4caa-9498-11f778da3e04',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Pub',
      hobbyId: '58fbd67c-7c7b-44ca-acbf-117ee9efa08f',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Rave',
      hobbyId: '722564f3-2b2f-478c-a822-ee257b4d9d05',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: '54c62c7d-ea4e-4749-9a85-ccced7d906e9',
      parentHobbyName: 'Parti',
      parentHobbyId: 'a790e2b3-061d-4a38-ace1-293670cfda7a',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Akustik Gitar',
      hobbyId: 'f0f2fc63-7ed7-447f-b6cc-1cacfa516555',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bas Gitar',
      hobbyId: 'b9718753-e478-442d-8970-ca265a9f4ac7',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bateri',
      hobbyId: '0c290aa9-4b5f-4424-8728-aa7f4e10397f',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Davul',
      hobbyId: '6675396d-8a71-400e-a9cc-021cefb02da2',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klasik Gitar',
      hobbyId: '9e6b030a-bb4d-4804-bdbf-7588a25c90bb',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klavye/Piyano',
      hobbyId: '81f49612-1489-4128-ab20-f75c5906cee4',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Parti',
      hobbyId: 'ee6da79b-1020-4484-be54-98132bcfd494',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Pop',
      hobbyId: '6ad9a016-35c0-41b5-8e07-eefad538e298',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Ukulele',
      hobbyId: 'e81cfa1d-0e27-4607-b494-cd7687fe0981',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'e739f485-f1f0-4ae5-8cc1-fb2710d1a53c',
      parentHobbyName: 'Pop',
      parentHobbyId: 'f4a535bd-bee4-43f3-8101-464170a35ae7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Akustik Gitar',
      hobbyId: '646e0cc4-dad7-4994-89d9-578595f32b03',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bas Gitar',
      hobbyId: '46f69519-baa7-4317-812a-307131a9b2e8',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bateri',
      hobbyId: '51c63fe6-bf61-40e6-8c3b-8eec2988d1fd',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Black Metal',
      hobbyId: '86defc43-3ef1-4bf4-b9fb-f03b4dc99212',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Davul',
      hobbyId: 'dd72e02a-608c-4b2f-8ce6-6a084a33b3af',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Death Metal',
      hobbyId: 'e9adb56f-d186-46a0-9a2f-0a901ac1778c',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Elektro Gitar',
      hobbyId: '70995dd9-146b-4efb-a3f8-223914fc2185',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Heavy Metal',
      hobbyId: 'e0b8c377-ab93-4ca7-aa0d-91c79d90c677',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klavye/Piyano',
      hobbyId: 'e08509bb-e726-4531-9875-19bbb51c582f',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Nu Metal',
      hobbyId: '83da1ccc-c0e1-4f2b-bd70-39a5f0217c88',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Rock',
      hobbyId: 'dbefef37-359f-42c8-96f2-205b021473e1',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Trash Metal',
      hobbyId: '535c7a76-e880-4e94-a7bb-4c6781a20208',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Üflemeli Çalgı',
      hobbyId: '08b8f094-ebee-42d2-911b-c5a44b90a7f4',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'd21cf068-0679-40d4-993c-958680163746',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yaylı Çalgı',
      hobbyId: '2ddfc256-2fd2-4678-9e01-057855bfb7a8',
      parentHobbyName: 'Rock & Metal',
      parentHobbyId: '31eca3b6-090a-4a36-9653-1ffc753e0804',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dişçilik',
      hobbyId: 'f858efdc-678c-4323-b5f4-84e40d36feda',
      parentHobbyName: 'Sağlık Bilimleri',
      parentHobbyId: 'd16ad40d-1da2-4449-89fb-8cd86bf00bbd',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hemşirelik',
      hobbyId: '927182de-6e99-4cad-9d8f-32d5ce119bc9',
      parentHobbyName: 'Sağlık Bilimleri',
      parentHobbyId: 'd16ad40d-1da2-4449-89fb-8cd86bf00bbd',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tıp',
      hobbyId: '7481c3e8-8177-4105-9ea9-862f2f0c9ea9',
      parentHobbyName: 'Sağlık Bilimleri',
      parentHobbyId: 'd16ad40d-1da2-4449-89fb-8cd86bf00bbd',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Akrobasi',
      hobbyId: '7ec3f4d9-e596-431c-bd1b-529ca5cb1236',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bale',
      hobbyId: 'bb41a16d-75b8-4d59-9e45-2a33952a5a52',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Caz Dansları',
      hobbyId: '5fc89606-b79c-4d6c-ab9b-859ebf2bb5ac',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dans',
      hobbyId: '7c3dee64-390b-454b-806f-3910527fc05c',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Halk Dansları',
      hobbyId: '574320b1-1dca-4116-a3ad-996c6449013f',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hiphop Dance',
      hobbyId: 'e1afa1ee-97a2-489f-8d98-88a4bb97e752',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Komedi',
      hobbyId: '3d53cca4-d903-4898-84bf-0d3f170bb689',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Latin Dansları',
      hobbyId: '0ae7a27e-eed3-49c2-b545-7a90cced3ded',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Modern Dans',
      hobbyId: '08dd4176-4726-4a1e-9983-6c1553f33da5',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Müzikal',
      hobbyId: '8e6f2d6c-8201-498c-99d8-5676435b2cf9',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Opera',
      hobbyId: '94528a4d-739c-447a-8ec9-acadc2d57187',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Oyunculuk',
      hobbyId: 'b4fa4a4c-ccaa-4db5-adde-b2a346981117',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sanat Sepet',
      hobbyId: '2b9c7fe7-6546-4b5b-831b-f267d36a4c73',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Stand-Up',
      hobbyId: '7afd99e2-c2c2-46ff-ab0a-e1b0cf9eeafa',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tiyatro',
      hobbyId: 'e694368c-93bc-4a93-ae4a-9cb82e6fc8ff',
      parentHobbyName: 'Sahne Sanatları',
      parentHobbyId: '758f594b-27bd-4815-a81d-2b760dfbf465',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Aikido',
      hobbyId: 'b3d3e918-7af6-4277-8564-f66511d0e4ef',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Boks',
      hobbyId: '508cc2a7-6c00-4a11-8893-009fdb729cd1',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Güreş',
      hobbyId: '82e52af7-ac4e-465d-9c1c-3aec9faca009',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Karate',
      hobbyId: 'f11d8c66-b4da-4a46-8291-2684c062922c',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kickbox',
      hobbyId: '47f0165a-a7b7-43ca-a36d-81c425e7dd3d',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kung-Fu',
      hobbyId: 'c0ee2494-5df9-4cbd-a5fd-12443192b11a',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Muay Thai',
      hobbyId: '481e8d9e-7dca-4604-a885-5176d63fd901',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Taekwando',
      hobbyId: '9215e005-d804-425f-8e77-878cc4fa8900',
      parentHobbyName: 'Savunma Sanatları',
      parentHobbyId: '48819d71-5f93-42ec-979b-f936b05a54d8',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Belgesel',
      hobbyId: '529fa2c0-2092-4927-bea5-e14078e9ca52',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Çekim',
      hobbyId: '88e668e9-afef-4a2b-8e12-958af70ebb80',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dizi',
      hobbyId: '646a3a89-bfa4-4957-8f71-06918aa8f13e',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Film',
      hobbyId: '7d11b475-ada8-49d1-86a8-c021a48162af',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Oyunculuk',
      hobbyId: '7492e29d-a79a-41c6-9931-192d58fc4d01',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sanat Sepet',
      hobbyId: '9b1e2e9e-fe53-451e-88af-cab11b31a0a6',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sinema',
      hobbyId: 'dcc3ef5a-220a-4e3d-96ff-5e20feeeda0c',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sinema Sanatı',
      hobbyId: '3eca6d8d-15a3-4ab4-8c21-5829799ba575',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Video Sanatı',
      hobbyId: '7bb2380e-b2e7-468f-9ba9-bd232ce93fa7',
      parentHobbyName: 'Sinema ve Film',
      parentHobbyId: 'a086ebf6-6b9f-4ba5-9bad-176e7354a63b',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Felsefe',
      hobbyId: '8b702a8b-f657-458f-80a1-5e5b8d5781b3',
      parentHobbyName: 'Sosyal Bilimler',
      parentHobbyId: '7168b65f-275e-4074-9b75-6781c964320e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Psikoloji',
      hobbyId: '8369bad4-9a42-49b3-a8ab-ae4c54a8f054',
      parentHobbyName: 'Sosyal Bilimler',
      parentHobbyId: '7168b65f-275e-4074-9b75-6781c964320e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Siyaset & Politika',
      hobbyId: '95c1b61d-23ef-46b1-8ad2-611bfc75153d',
      parentHobbyName: 'Sosyal Bilimler',
      parentHobbyId: '7168b65f-275e-4074-9b75-6781c964320e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sosyoloji',
      hobbyId: 'b8be3c3f-7acc-4b80-8870-28250f5421d3',
      parentHobbyName: 'Sosyal Bilimler',
      parentHobbyId: '7168b65f-275e-4074-9b75-6781c964320e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tarih',
      hobbyId: '816d927f-528d-4877-91a6-1b3572fa5c40',
      parentHobbyName: 'Sosyal Bilimler',
      parentHobbyId: '7168b65f-275e-4074-9b75-6781c964320e',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bağış Kampanyaları',
      hobbyId: 'c9310713-af00-448a-934a-01c4af6715b6',
      parentHobbyName: 'Sosyal Sorumluluk',
      parentHobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Gönüllülük',
      hobbyId: '0dd39040-ddcd-4e49-a537-c4d4626a74ca',
      parentHobbyName: 'Sosyal Sorumluluk',
      parentHobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hayvanseverlik',
      hobbyId: '00f94e76-cbf1-495e-b6d9-209d45b4b680',
      parentHobbyName: 'Sosyal Sorumluluk',
      parentHobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kamu Hizmeti',
      hobbyId: 'ad912baf-711b-4ecb-ae04-18da61a267db',
      parentHobbyName: 'Sosyal Sorumluluk',
      parentHobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kermesler',
      hobbyId: '255e0f9a-c665-4750-8977-594120cfb665',
      parentHobbyName: 'Sosyal Sorumluluk',
      parentHobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sivil Toplum Kuruluşları',
      hobbyId: '5d23dbfb-b2ed-4c0d-86cd-1db2e3775f31',
      parentHobbyName: 'Sosyal Sorumluluk',
      parentHobbyId: 'e3dab36e-2758-4a01-8416-7be51bb3f304',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dalış',
      hobbyId: '0cb167f7-f73a-4d6e-8caf-0be2895afc63',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kano',
      hobbyId: '8d0b7850-674f-469d-aa09-7e2e508f4d48',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kite Sörf',
      hobbyId: 'fdda3ad4-d4c1-41d2-916b-495a0dbf6347',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kürek',
      hobbyId: '4ef02b82-c1ad-4ae6-861b-926048b13f96',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Rüzgar Sörfü',
      hobbyId: '52b13f95-9b3e-4da5-8ec5-fd4fefd40d43',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sörf',
      hobbyId: 'cee88c79-a415-4037-9e04-1e504957b4d4',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'SUP',
      hobbyId: 'd46c1737-4972-406d-ab60-817e947a541c',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tekne',
      hobbyId: '5c707017-1c77-4caf-a458-3888dd2d1d02',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Wakeboard',
      hobbyId: 'a5c316d8-e21e-4917-ac69-09a0beff4845',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yat',
      hobbyId: '02728326-e99c-4192-879c-d6c6c2ce4f2f',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yelken',
      hobbyId: 'a56b81d1-0c5b-4b6a-9896-ca515920487b',
      parentHobbyName: 'Su Sporları',
      parentHobbyId: 'c7426e50-73a6-46c1-8162-12c704f24cd3',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Amerikan Futbolu',
      hobbyId: 'a19c2ec6-7480-47af-9f4b-a51d0deba54d',
      parentHobbyName: 'Takım Sporları',
      parentHobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Basketbol',
      hobbyId: '37c5c186-059c-48d3-8f1c-fea52ecb9707',
      parentHobbyName: 'Takım Sporları',
      parentHobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Futbol',
      hobbyId: '545622b6-5ccb-4682-8cdd-18223d7bae25',
      parentHobbyName: 'Takım Sporları',
      parentHobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hentbol',
      hobbyId: 'c276043e-adfb-4ea0-a5aa-e155a2dbf7ae',
      parentHobbyName: 'Takım Sporları',
      parentHobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hokey',
      hobbyId: 'c3c68f85-a359-4bae-974c-f89f24cceacb',
      parentHobbyName: 'Takım Sporları',
      parentHobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Voleybol',
      hobbyId: 'd2c5fa64-0089-4c53-ab8c-b197c1959f25',
      parentHobbyName: 'Takım Sporları',
      parentHobbyId: '34a359ff-b4d0-40e2-a4f0-3f186c691feb',
      level: 3,
    ),
    Hobby(
      hobbyName: 'DJ',
      hobbyId: '05efb49f-f3ca-4d72-a1d9-78f7472c71cd',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dubstep',
      hobbyId: 'c3ef38b3-3930-46e7-b3fd-5305490a95ee',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'House Music',
      hobbyId: '6080b2b9-eb6f-45fc-9b66-54e283bde329',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Müzik Prodüksyonu',
      hobbyId: '7e47f4e8-8309-4ab3-adf3-ac2d9361c455',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Parti',
      hobbyId: '521698b4-59f6-47b2-b97b-ff6619b21553',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Rave',
      hobbyId: '528f29ab-ce4c-413c-ac6b-2e6c1697d170',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Shuffle Dance',
      hobbyId: 'ae87e729-f2cf-495a-9123-9b5db286d980',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Techno',
      hobbyId: '5def9867-ea4a-428b-b581-16ba0a18dbca',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Trance',
      hobbyId: 'b0710f02-e67b-446c-be9b-e41bb66e4888',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'd695da92-0a11-4f35-a939-733b8fb755d0',
      parentHobbyName: 'Techno ve EDM',
      parentHobbyId: 'ef32e7c0-70e8-4901-bfe3-d895b42e62b7',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bisiklet',
      hobbyId: '962d42a4-c0ba-42cd-8324-991dc75b65e1',
      parentHobbyName: 'Tekerlekli Sporlar',
      parentHobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'BMX',
      hobbyId: '835ad90a-95e0-44cd-a222-f9b283aa99ac',
      parentHobbyName: 'Tekerlekli Sporlar',
      parentHobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kaykay',
      hobbyId: '82ae941f-cc00-4636-8cb7-af33e6d2a546',
      parentHobbyName: 'Tekerlekli Sporlar',
      parentHobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Longboard',
      hobbyId: 'a2260a13-c7e2-460c-8279-2789f7f97ee7',
      parentHobbyName: 'Tekerlekli Sporlar',
      parentHobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Paten',
      hobbyId: 'bac41dc3-0551-4aeb-bfbb-02edc21e45c1',
      parentHobbyName: 'Tekerlekli Sporlar',
      parentHobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Scooter',
      hobbyId: '964f54b7-78d1-43c1-a0e1-92c9eae043e8',
      parentHobbyName: 'Tekerlekli Sporlar',
      parentHobbyId: '2d9e360a-a21d-4465-8610-34aa3ada3e04',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Back-End',
      hobbyId: 'affed6c5-9371-4067-9e8b-ef6675777cf9',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Bilgisayar',
      hobbyId: '16820365-d6bb-4101-b5fa-96adcb06b0eb',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Biyoteknoloji',
      hobbyId: 'da53f858-e3a2-4fb8-bbfa-bcd8e05a863b',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'C/C++',
      hobbyId: '382ff2c3-30bd-4f62-a8e0-709141fb1042',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Çevre Mühendisliği',
      hobbyId: '1bb85149-f589-46ca-b90a-28931579b580',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Cloud Computing',
      hobbyId: 'f254b923-a768-4cfc-9746-c1ec875209e5',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Denizcilik Teknolojileri',
      hobbyId: '22e975b6-c8c6-4f3d-8525-3180a288980c',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dev Ops',
      hobbyId: 'd577db80-ddd6-4852-9d68-11eda1fc368a',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Elektronik',
      hobbyId: '32cb9585-85ac-4391-a429-e6fc6cbfde61',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Flutter',
      hobbyId: '31ad4486-b967-48b7-9ff7-47472abfe295',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Front-End',
      hobbyId: '04657d90-8ff7-44b1-9d6e-5866edf67a4e',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Genetik Mühendisliği',
      hobbyId: '61634979-4def-49b2-8a1b-6b9c9c935118',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Haria Mühendisliği',
      hobbyId: '2f077ac3-93c1-4d65-bb13-c3a21f8281b3',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Havacılık Teknolojileri',
      hobbyId: 'bf419b6b-bee0-4aa6-a992-b1c000a8ccec',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'İnşaat Mühendisliği',
      hobbyId: 'cf530abc-dfeb-4f80-af19-4f2be373b980',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Java',
      hobbyId: '11bca86b-754f-48f3-982b-36f25bdc33ea',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Javascript',
      hobbyId: '0636464c-ec3a-417d-9658-73929bd03db0',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kotlin',
      hobbyId: '28252253-eeb9-4635-a855-6292f7709ad4',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Machine Learning',
      hobbyId: '5e96ce3c-1d37-4f10-8ac9-7571a0b93c12',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Makine Mühendisliği',
      hobbyId: '77aa4fb0-d1a2-4334-b648-f14e7d7fd45d',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Matlab',
      hobbyId: 'ab6eea24-7504-43bc-990d-54210ba794de',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Mobil Geliştirici',
      hobbyId: 'fd179b88-e9db-4176-bb80-965ddaf884ee',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: '.NET',
      hobbyId: 'eb20de85-16b1-43bc-91b1-c5e3a7163f85',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Python',
      hobbyId: 'c3cd19d0-5562-44f5-a661-39ba31411cd6',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Robotik',
      hobbyId: '9a89537b-a131-45f3-8eaf-4c4262186134',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Siber Güvenlik',
      hobbyId: 'ad687489-01a9-4bc9-a20b-b368f9ff0a1f',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Swift',
      hobbyId: '12e5999d-70e8-42f5-a49e-647822c912bc',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tekstil Teknolojileri',
      hobbyId: '5fb736c4-3e05-4888-a512-93f5460518fc',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Veri Bilimi',
      hobbyId: '44b0eb50-025a-4581-9ce6-51b8c2ee8c7f',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yapay Zeka',
      hobbyId: '1e37f728-4254-49ee-917b-7dce724046e5',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yazılım',
      hobbyId: '2bd0e925-55d9-4a90-9e86-53af6ed10bd0',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yer Bilimleri',
      hobbyId: '2fe4f31b-35ee-4fc3-bbfb-b2422eb66bd6',
      parentHobbyName: 'Teknoloji ve Mühendislik',
      parentHobbyId: '981ba54c-598c-4ab3-82a7-bc7555738cc5',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Biyoloji',
      hobbyId: '362cfbd0-44d1-49e7-bb16-ea74a8829e73',
      parentHobbyName: 'Temel Bilimler',
      parentHobbyId: '3e3178b4-ce21-460f-b64e-9536d33f7696',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fizik',
      hobbyId: 'bfb33c87-b4cb-4bce-847b-c04a683151bd',
      parentHobbyName: 'Temel Bilimler',
      parentHobbyId: '3e3178b4-ce21-460f-b64e-9536d33f7696',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kimya',
      hobbyId: '0e8007d9-d8df-496b-8c3f-2d97d2730f8c',
      parentHobbyName: 'Temel Bilimler',
      parentHobbyId: '3e3178b4-ce21-460f-b64e-9536d33f7696',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Matematik',
      hobbyId: '1246b704-049d-427b-a209-e2c9c950523f',
      parentHobbyName: 'Temel Bilimler',
      parentHobbyId: '3e3178b4-ce21-460f-b64e-9536d33f7696',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Anma Töreni',
      hobbyId: '4513ccaa-c85f-4cb9-bbdd-8913cea84350',
      parentHobbyName: 'Toplumsal Dayanışma',
      parentHobbyId: '1a5f33e0-abe3-425a-907c-3216e0491a5f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Milli Bayramlar',
      hobbyId: 'a3b3860e-21f9-4d42-8c0a-6fada1216f24',
      parentHobbyName: 'Toplumsal Dayanışma',
      parentHobbyId: '1a5f33e0-abe3-425a-907c-3216e0491a5f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Toplumsal Farkındalık',
      hobbyId: '87e0e358-5cc3-46ef-a5f5-0c4aac4a6d87',
      parentHobbyName: 'Toplumsal Dayanışma',
      parentHobbyId: '1a5f33e0-abe3-425a-907c-3216e0491a5f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yürüyüşler',
      hobbyId: 'ab4ab022-0308-47fd-925a-40e7c04ebf30',
      parentHobbyName: 'Toplumsal Dayanışma',
      parentHobbyId: '1a5f33e0-abe3-425a-907c-3216e0491a5f',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Arabesk',
      hobbyId: 'd88e546d-8ccf-475f-a39e-50ef34137e53',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Klasik Gitar',
      hobbyId: '76142264-5bd8-47b1-891f-70ce4493f3d2',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Türk Halk Müziği',
      hobbyId: 'c81a286c-f8f1-43a3-a3e9-5874e4788d8c',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Türk Sanat Müziği',
      hobbyId: 'a2496f72-5702-40bf-bfa8-cf7b8f6e0394',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Türkü',
      hobbyId: 'bcec8050-ddb2-40e0-aff3-39789c6b4b13',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Üflemeli Çalgı',
      hobbyId: 'b847f5f6-41d4-4b0f-b59b-92fe8582acde',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Vokal',
      hobbyId: 'ea6ae85a-e1db-4a58-ad95-de5499944fc7',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yatlı Çalgı',
      hobbyId: '313e63fb-423e-4639-a1bc-45b903999ae6',
      parentHobbyName: 'Türk Müziği',
      parentHobbyId: '16a797f5-3f7b-4260-97ab-d1f181b8a233',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dijital Yayıncılık',
      hobbyId: '0fce101d-1329-440b-be4d-015d643c531f',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Dil Bilim',
      hobbyId: 'e886544e-c05d-445b-887b-21a611800830',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Edebiyat',
      hobbyId: '4b53a9e3-64e7-416e-95b8-75d9754b95df',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kitap Fuarları',
      hobbyId: 'fff1490f-4979-4eb0-89d1-b99037d0d102',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kitap Kurdu',
      hobbyId: '5cce0ef7-83c7-4018-8186-9c6d746c7374',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kitap Okuma',
      hobbyId: '9d17ff37-9318-42f5-ac5c-7e330661d68d',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Okurluk',
      hobbyId: '3e756759-4dfc-423c-8d95-d52a7a255ca0',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sanat Sepet',
      hobbyId: '0f40229a-3c0f-47c8-9925-61d0a2a9e9e2',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yazar İmza Günleri',
      hobbyId: '46f1dc24-9010-46e1-9f1a-86e1a329d13b',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yazarlık',
      hobbyId: 'de085333-dff3-405d-9862-af1d3c2f89ba',
      parentHobbyName: 'Yazı ve Edebiyat',
      parentHobbyId: 'fefe1fb6-3d7f-49b9-94b2-4745980268a2',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Aşçılık',
      hobbyId: '105ec6f9-ad32-47d8-b688-0234937a90ca',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Atıştırma',
      hobbyId: '62995f3b-16f9-4988-8421-b7455870e7a8',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Deniz Mahsülleri',
      hobbyId: '04e56198-598d-4533-bcbb-00eb80206dd3',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fast Food',
      hobbyId: 'b52441fa-7d95-4607-b34e-744a986f5d73',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Fransız Mutfağı',
      hobbyId: 'dc6a8c84-7319-41ae-a738-f70cfcb83ab8',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Gastronomi',
      hobbyId: '889a2b69-6a9c-44d8-9799-e2e9b363f0b8',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Hamur İşi',
      hobbyId: '034d74d4-4d3e-45d9-aa6c-b40329a2d76f',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Kteojenik Beslenme',
      hobbyId: '4a40f4fd-3807-4cd1-b7fe-3168d2677916',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Meksika Mutfağı',
      hobbyId: '56333362-68a7-4fa2-a7d7-871c02478730',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Sağlıklı Beslenme',
      hobbyId: 'c4e75b7c-3e87-44fd-89e9-785e1bff7b45',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Steakhouse',
      hobbyId: '0e687b80-e3e0-426b-887c-a7e2c02a8f74',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Tatlı',
      hobbyId: '968d1d26-a256-44ab-b8cc-8f9f7e97e2e9',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Türk Mutfağı',
      hobbyId: '98c5722a-6795-4b29-b61d-ac0ffaa4d79e',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Uzak Doğu Mutfağı',
      hobbyId: 'fcd5955c-0a68-4567-91d1-7092efe515ef',
      parentHobbyName: 'Yeme',
      parentHobbyId: '204bfaea-5a96-45e8-bf65-b26e5aed8e26',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Ekonomi',
      hobbyId: 'e2190b4e-4dba-4da4-83ce-4ef531c7f03e',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Excel',
      hobbyId: '83a267d5-a024-44de-a4c1-e07230453608',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Finans',
      hobbyId: '05e1e8af-b2b9-49d2-9121-5e97a64c5816',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Girişimcilik',
      hobbyId: 'eb867fe1-c4bc-42e3-bb58-f6cd3caa080a',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'İşletme',
      hobbyId: '94ff36cc-ef94-4c33-93b7-33272a9a18d8',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Liderlik',
      hobbyId: '3bbfacd0-6e67-49ce-8507-0aee8e151f1a',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'MS Office',
      hobbyId: '5bdcb8bf-b760-4290-b230-6f84e7263e7e',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Pazarlama',
      hobbyId: 'cdb2e2c2-b2cf-4dda-95a7-9101670bb624',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Proje Yöneticiliği',
      hobbyId: '54eb5688-ea61-4143-a511-a08799884d77',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Satış',
      hobbyId: '35a83e29-8404-43ad-b3d7-fb2865cee905',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
    Hobby(
      hobbyName: 'Yatırımcılık',
      hobbyId: 'edc8005a-eee0-4c9f-acc3-a2df5e624c05',
      parentHobbyName: 'Yönetim Bilimleri',
      parentHobbyId: 'c95f30c8-6c48-435f-8075-384878caa9be',
      level: 3,
    ),
  ];
}
