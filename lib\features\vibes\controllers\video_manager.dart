import 'dart:io';

import 'package:video_player/video_player.dart';

class VideoManager {
  // Keep maximum of 5 x 3 controllers active
  final Map<String, List<VideoPlayerController>> _activeControllers = {};

  final int horizontalCount = 3;
  final int verticalCount = 5;

  Future<VideoPlayerController> getControllerForVideoByUrl(String url, String vibeFolderId) async {
    // Try to find existing controller
    if (_activeControllers.containsKey(vibeFolderId)) {
      for (final controller in _activeControllers[vibeFolderId]!) {
        if (controller.dataSource == url) {
          return controller;
        }
      }
      
      if (_activeControllers[vibeFolderId]!.length >= verticalCount) {
        final oldest = _activeControllers[vibeFolderId]!.removeAt(0);
        oldest.dispose();
      }

      final newController = VideoPlayerController.networkUrl(Uri.parse(url));
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    } else {
      if (_activeControllers.length >= horizontalCount) {
        final oldest = _activeControllers.remove(_activeControllers.keys.first); // Remove oldest vibe folder
        for (final controller in oldest!) {
          controller.dispose();
        }
      }

      _activeControllers[vibeFolderId] = [];
      final newController = VideoPlayerController.networkUrl(Uri.parse(url));
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    }
  }

  Future<VideoPlayerController> getControllerForVideoByFile(File file, String vibeFolderId) async {
    // Try to find existing controller
    if (_activeControllers.containsKey(vibeFolderId)) {
      for (final controller in _activeControllers[vibeFolderId]!) {
        if (controller.dataSource == file.path) {
          return controller;
        }
      }

      if (_activeControllers[vibeFolderId]!.length >= verticalCount) {
        final oldest = _activeControllers[vibeFolderId]!.removeAt(0);
        oldest.dispose();
      }

      final newController = VideoPlayerController.file(file);
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    } else {
      if (_activeControllers.length >= horizontalCount) {
        final oldest = _activeControllers.remove(_activeControllers.keys.first); // Remove oldest vibe folder
        for (final controller in oldest!) {
          controller.dispose();
        }
      }

      _activeControllers[vibeFolderId] = [];
      final newController = VideoPlayerController.file(file);
      await newController.initialize();
      _activeControllers[vibeFolderId]!.add(newController);
      return newController;
    }
  }

  void disposeAll() {
    for (final controllers in _activeControllers.values) {
      for (final controller in controllers) {
        controller.dispose();
      }
    }
    _activeControllers.clear();
  }
}
