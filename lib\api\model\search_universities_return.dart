//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchUniversitiesReturn {
  /// Returns a new [SearchUniversitiesReturn] instance.
  SearchUniversitiesReturn({
    this.universities = const [],
    required this.universityCount,
  });

  /// List of universities
  List<UniversityItem> universities;

  /// Total number of universities
  ///
  /// Minimum value: 0
  int universityCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchUniversitiesReturn &&
    _deepEquality.equals(other.universities, universities) &&
    other.universityCount == universityCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (universities.hashCode) +
    (universityCount.hashCode);

  @override
  String toString() => 'SearchUniversitiesReturn[universities=$universities, universityCount=$universityCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'universities'] = this.universities;
      json[r'universityCount'] = this.universityCount;
    return json;
  }

  /// Returns a new [SearchUniversitiesReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchUniversitiesReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchUniversitiesReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchUniversitiesReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchUniversitiesReturn(
        universities: UniversityItem.listFromJson(json[r'universities']),
        universityCount: mapValueOfType<int>(json, r'universityCount')!,
      );
    }
    return null;
  }

  static List<SearchUniversitiesReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchUniversitiesReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchUniversitiesReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchUniversitiesReturn> mapFromJson(dynamic json) {
    final map = <String, SearchUniversitiesReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchUniversitiesReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchUniversitiesReturn-objects as value to a dart map
  static Map<String, List<SearchUniversitiesReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchUniversitiesReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchUniversitiesReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'universities',
    'universityCount',
  };
}

