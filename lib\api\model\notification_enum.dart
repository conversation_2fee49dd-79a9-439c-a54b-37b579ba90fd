//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Type of notification
class NotificationEnum {
  /// Instantiate a new enum with the provided [value].
  const NotificationEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const type1 = NotificationEnum._(r'type_1');
  static const type2 = NotificationEnum._(r'type_2');
  static const type3 = NotificationEnum._(r'type_3');
  static const type4 = NotificationEnum._(r'type_4');
  static const type5 = NotificationEnum._(r'type_5');
  static const type6 = NotificationEnum._(r'type_6');
  static const type7 = NotificationEnum._(r'type_7');
  static const type8 = NotificationEnum._(r'type_8');
  static const type9 = NotificationEnum._(r'type_9');
  static const type10 = NotificationEnum._(r'type_10');
  static const type11 = NotificationEnum._(r'type_11');
  static const type12 = NotificationEnum._(r'type_12');
  static const type13 = NotificationEnum._(r'type_13');
  static const type14 = NotificationEnum._(r'type_14');
  static const type15 = NotificationEnum._(r'type_15');
  static const type16 = NotificationEnum._(r'type_16');
  static const type17 = NotificationEnum._(r'type_17');
  static const type18 = NotificationEnum._(r'type_18');
  static const type19 = NotificationEnum._(r'type_19');
  static const type20 = NotificationEnum._(r'type_20');
  static const type21 = NotificationEnum._(r'type_21');
  static const type22 = NotificationEnum._(r'type_22');
  static const type23 = NotificationEnum._(r'type_23');
  static const type24 = NotificationEnum._(r'type_24');
  static const type25 = NotificationEnum._(r'type_25');
  static const type26 = NotificationEnum._(r'type_26');
  static const type27 = NotificationEnum._(r'type_27');
  static const type28 = NotificationEnum._(r'type_28');
  static const type29 = NotificationEnum._(r'type_29');
  static const type30 = NotificationEnum._(r'type_30');
  static const type31 = NotificationEnum._(r'type_31');
  static const type32 = NotificationEnum._(r'type_32');
  static const type33 = NotificationEnum._(r'type_33');
  static const type34 = NotificationEnum._(r'type_34');

  /// List of all possible values in this [enum][NotificationEnum].
  static const values = <NotificationEnum>[
    type1,
    type2,
    type3,
    type4,
    type5,
    type6,
    type7,
    type8,
    type9,
    type10,
    type11,
    type12,
    type13,
    type14,
    type15,
    type16,
    type17,
    type18,
    type19,
    type20,
    type21,
    type22,
    type23,
    type24,
    type25,
    type26,
    type27,
    type28,
    type29,
    type30,
    type31,
    type32,
    type33,
    type34,
  ];

  static NotificationEnum? fromJson(dynamic value) => NotificationEnumTypeTransformer().decode(value);

  static List<NotificationEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <NotificationEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = NotificationEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [NotificationEnum] to String,
/// and [decode] dynamic data back to [NotificationEnum].
class NotificationEnumTypeTransformer {
  factory NotificationEnumTypeTransformer() => _instance ??= const NotificationEnumTypeTransformer._();

  const NotificationEnumTypeTransformer._();

  String encode(NotificationEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a NotificationEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  NotificationEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'type_1': return NotificationEnum.type1;
        case r'type_2': return NotificationEnum.type2;
        case r'type_3': return NotificationEnum.type3;
        case r'type_4': return NotificationEnum.type4;
        case r'type_5': return NotificationEnum.type5;
        case r'type_6': return NotificationEnum.type6;
        case r'type_7': return NotificationEnum.type7;
        case r'type_8': return NotificationEnum.type8;
        case r'type_9': return NotificationEnum.type9;
        case r'type_10': return NotificationEnum.type10;
        case r'type_11': return NotificationEnum.type11;
        case r'type_12': return NotificationEnum.type12;
        case r'type_13': return NotificationEnum.type13;
        case r'type_14': return NotificationEnum.type14;
        case r'type_15': return NotificationEnum.type15;
        case r'type_16': return NotificationEnum.type16;
        case r'type_17': return NotificationEnum.type17;
        case r'type_18': return NotificationEnum.type18;
        case r'type_19': return NotificationEnum.type19;
        case r'type_20': return NotificationEnum.type20;
        case r'type_21': return NotificationEnum.type21;
        case r'type_22': return NotificationEnum.type22;
        case r'type_23': return NotificationEnum.type23;
        case r'type_24': return NotificationEnum.type24;
        case r'type_25': return NotificationEnum.type25;
        case r'type_26': return NotificationEnum.type26;
        case r'type_27': return NotificationEnum.type27;
        case r'type_28': return NotificationEnum.type28;
        case r'type_29': return NotificationEnum.type29;
        case r'type_30': return NotificationEnum.type30;
        case r'type_31': return NotificationEnum.type31;
        case r'type_32': return NotificationEnum.type32;
        case r'type_33': return NotificationEnum.type33;
        case r'type_34': return NotificationEnum.type34;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [NotificationEnumTypeTransformer] instance.
  static NotificationEnumTypeTransformer? _instance;
}

