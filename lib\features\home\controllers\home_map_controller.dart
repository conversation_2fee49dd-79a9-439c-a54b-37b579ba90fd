import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/remove_duplicate_coordinates.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/controllers/marker_controller.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

/// Controller for managing map functionality in the home feature
///
/// Handles map interactions, marker management, and iVent banner display
/// based on selected map features. Integrates with MapboxController
/// for core map functionality.
class HomeMapController extends BaseHomeController {
  // Controllers
  late final MapboxController mapboxController;

  // Reactive state
  final _iventBanners = <IventCardItem>[].obs;
  final _mapReturn = Rxn<MapReturn>();

  // Constructor
  HomeMapController(AuthService authService, HomeStateManager state) : super(authService, state) {
    mapboxController = MapboxController(
      authService: authService,
      onSelectedFeaturesChanged: onSelectedFeaturesChanged,
    );
  }

  // Getters
  List<IventCardItem> get iventBanners => _iventBanners;
  MapReturn? get mapReturn => _mapReturn.value;
  MarkerController get markerController => mapboxController.markerController;
  String get todayAsString => DateFormat('d MMMM EEEE').format(DateTime.now());

  // Setters
  set iventBanners(List<IventCardItem> value) => _iventBanners.assignAll(value);
  set mapReturn(MapReturn? value) => _mapReturn.value = value;

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();
    await mapboxController.getUserLocationCoordinates();
  }

  // Public methods

  /// Handles changes in selected map features and updates iVent banners
  Future<void> onSelectedFeaturesChanged(List<MarkerFeature> selectedFeatures) async {
    if (selectedFeatures.isEmpty) {
      iventBanners = [];
      return;
    }

    final iventIds = selectedFeatures.map((feature) => feature.id).toList();
    final getBannerByIventIdResult = await authService.iventsApi.getBannerByIventId(
      GetBannerByIventIdDto(iventIds: iventIds),
    );

    if (getBannerByIventIdResult != null) {
      iventBanners = getBannerByIventIdResult.ivents;
    }
  }

  /// Updates visible map bounds and reloads markers
  Future<void> updateVisibleMapBounds({CameraState? cameraState}) async {
    await mapboxController.updateVisibleMapBounds();
    await loadMarkers();
  }

  /// Loads markers for the current map bounds
  Future<void> loadMarkers() async {
    final bounds = mapboxController.currentMapBounds;
    if (bounds == null) return;

    // Load iVents for current map bounds with extended date range
    mapReturn = await authService.homeApi.map(
      DateTime.parse('2018-01-01T00:00:00Z').toIso8601String(),
      DateTime.now().add(const Duration(days: 365 * 5)).toIso8601String(),
      bounds.latStart,
      bounds.latEnd,
      bounds.lngStart,
      bounds.lngEnd,
    );

    if (mapReturn == null) return;

    // Convert iVents to marker features
    final result = mapReturn!.ivents;
    List<MarkerFeature> markersToBeAdded = result.map((ivent) {
      return MarkerFeature(
        id: ivent.iventId,
        properties: MarkerFeatureProperties(isSelected: false),
        geometry: MarkerFeatureGeometry(
          coordinates: [ivent.longitude, ivent.latitude],
        ),
      );
    }).toList();

    // Remove duplicates and add to map
    markersToBeAdded = removeDuplicateCoordinates(
      markersToBeAdded,
      exclude: markerController.allFeatures,
    );
    markerController.addMarkers(markersToBeAdded);
  }
}
