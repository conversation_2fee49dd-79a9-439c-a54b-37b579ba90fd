# openapi.model.MemoryFolderCardItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**memoryFolderId** | **String** | UUID of the memory folder | 
**thumbnailUrl** | **String** | URL to the memory folder thumbnail image | [optional] 
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**dates** | **List<String>** | List of dates for the ivent in ISO 8601 date-time format | [default to const []]
**memberCount** | **int** | Number of members in the ivent | 
**memberFirstnames** | **List<String>** | List of member's first names in the ivent | [default to const []]
**createdAt** | **String** | Date of creation of the memory folder in ISO 8601 date-time format | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



