# openapi.model.GetPageByPageIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**pageId** | **String** | UUID of the page | 
**pageName** | **String** | Name of the page | 
**thumbnailUrl** | **String** | URL to the page thumbnail image | [optional] 
**createdIventCount** | **int** | Number of ivents created by this page | 
**followerCount** | **int** | Number of followers of this page | 
**tagIds** | **List<String>** | Hobby tag IDs associated with the page | [default to const []]
**haveMembership** | **bool** | Whether this page has membership functionality | 
**isFirstPerson** | **bool** | Whether the current user is the owner/creator of this page | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



