import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class AppNavigationController extends BaseController {
  final HomeStateManager homeStateManager;
  final ProfileStateManager profileStateManager;

  AppNavigationController(
    AuthService authService,
    this.homeStateManager,
    this.profileStateManager,
  ) : super(authService);

  @override
  Future<void> initController() async {
    super.initController();
    Get.lazyPut(() => HomeController(authService, homeStateManager), fenix: true);
    Get.lazyPut(() => VibesPageController(authService), fenix: true);
    Get.lazyPut(() => VibeUploadController(authService), fenix: true);
    Get.lazyPut(() => ProfileController(authService, profileStateManager), tag: sessionUser.sessionId, fenix: true);
    print('BottomNavBarController has been initialized with user: ${sessionUser.sessionId}');
  }
}
