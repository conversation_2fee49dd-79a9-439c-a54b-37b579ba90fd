//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class HobbyItem {
  /// Returns a new [HobbyItem] instance.
  HobbyItem({
    required this.hobbyId,
    required this.hobbyName,
    required this.parentHobbyId,
    required this.parentHobbyName,
  });

  /// UUID of the hobby
  String hobbyId;

  /// Name of the hobby (sub category)
  String hobbyName;

  /// UUID of the parent hobby
  String parentHobbyId;

  /// Name of the parent hobby (main category)
  String parentHobbyName;

  @override
  bool operator ==(Object other) => identical(this, other) || other is HobbyItem &&
    other.hobbyId == hobbyId &&
    other.hobbyName == hobbyName &&
    other.parentHobbyId == parentHobbyId &&
    other.parentHobbyName == parentHobbyName;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (hobbyId.hashCode) +
    (hobbyName.hashCode) +
    (parentHobbyId.hashCode) +
    (parentHobbyName.hashCode);

  @override
  String toString() => 'HobbyItem[hobbyId=$hobbyId, hobbyName=$hobbyName, parentHobbyId=$parentHobbyId, parentHobbyName=$parentHobbyName]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'hobbyId'] = this.hobbyId;
      json[r'hobbyName'] = this.hobbyName;
      json[r'parentHobbyId'] = this.parentHobbyId;
      json[r'parentHobbyName'] = this.parentHobbyName;
    return json;
  }

  /// Returns a new [HobbyItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static HobbyItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "HobbyItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "HobbyItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return HobbyItem(
        hobbyId: mapValueOfType<String>(json, r'hobbyId')!,
        hobbyName: mapValueOfType<String>(json, r'hobbyName')!,
        parentHobbyId: mapValueOfType<String>(json, r'parentHobbyId')!,
        parentHobbyName: mapValueOfType<String>(json, r'parentHobbyName')!,
      );
    }
    return null;
  }

  static List<HobbyItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <HobbyItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = HobbyItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, HobbyItem> mapFromJson(dynamic json) {
    final map = <String, HobbyItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = HobbyItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of HobbyItem-objects as value to a dart map
  static Map<String, List<HobbyItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<HobbyItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = HobbyItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'hobbyId',
    'hobbyName',
    'parentHobbyId',
    'parentHobbyName',
  };
}

