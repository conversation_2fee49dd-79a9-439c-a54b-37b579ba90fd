# openapi.model.RemoveCollabByIventIdDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**collabId** | **String** | UUID of the collaborator to be removed from the ivent | 
**collabType** | [**AccountTypeEnum**](AccountTypeEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



