import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/hobby_filter_category_box.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class HobbyCatalogue extends StatefulWidget {
  final HomeController controller;

  const HobbyCatalogue({
    super.key,
    required this.controller,
  });

  @override
  State<HobbyCatalogue> createState() => _HobbyCatalogueState();
}

class _HobbyCatalogueState extends State<HobbyCatalogue> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedHobbyIds = widget.controller.state.selectedHobbyIds;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (selectedHobbyIds.isNotEmpty) _buildHobbiesSelected(selectedHobbyIds),
          if (selectedHobbyIds.isNotEmpty) const SizedBox(height: AppDimensions.padding12),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
              child: ListView.separated(
                padding: const EdgeInsets.only(bottom: 100),
                itemCount: Hobby.hobbyListByParentHobbyName.length,
                itemBuilder: (context, index) {
                  final parentHobbyEntry = Hobby.hobbyListByParentHobbyName.entries.elementAt(index);
                  return HobbyFilterCategoryBox(
                    mainCategory: parentHobbyEntry.key,
                    hobbyList: parentHobbyEntry.value,
                    selectedHobbyIds: widget.controller.state.selectedHobbyIds,
                    onHobbyToggle: widget.controller.filterController.toggleHobbySelection,
                    controller: widget.controller,
                  );
                },
                separatorBuilder: IaListTile.separatorBuilder20,
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildHobbiesSelected(List<String> selectedHobbyIds) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      height: AppDimensions.buttonHeightFilterSelectedHobbyTag,
      child: ListView.separated(
        clipBehavior: Clip.none,
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: selectedHobbyIds.length,
        itemBuilder: (context, index) {
          final hobbyId = selectedHobbyIds[index];
          return HomeButtons.filterSelectedHobbyTag(
            onTap: () => widget.controller.filterController.toggleHobbySelection(hobbyId),
            isSelected: true,
            text: Hobby.getHobbyNameFromHobbyId(hobbyId),
          );
        },
        separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
      ),
    );
  }
}
