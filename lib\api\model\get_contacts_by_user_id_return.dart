//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetContactsByUserIdReturn {
  /// Returns a new [GetContactsByUserIdReturn] instance.
  GetContactsByUserIdReturn({
    this.contacts = const [],
    required this.contactCount,
  });

  /// List of user contacts with their phone numbers
  List<UserListItemWithPhoneNumber> contacts;

  /// Total number of contacts found
  ///
  /// Minimum value: 0
  int contactCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetContactsByUserIdReturn &&
    _deepEquality.equals(other.contacts, contacts) &&
    other.contactCount == contactCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (contacts.hashCode) +
    (contactCount.hashCode);

  @override
  String toString() => 'GetContactsByUserIdReturn[contacts=$contacts, contactCount=$contactCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'contacts'] = this.contacts;
      json[r'contactCount'] = this.contactCount;
    return json;
  }

  /// Returns a new [GetContactsByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetContactsByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetContactsByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetContactsByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetContactsByUserIdReturn(
        contacts: UserListItemWithPhoneNumber.listFromJson(json[r'contacts']),
        contactCount: mapValueOfType<int>(json, r'contactCount')!,
      );
    }
    return null;
  }

  static List<GetContactsByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetContactsByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetContactsByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetContactsByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetContactsByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetContactsByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetContactsByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetContactsByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetContactsByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetContactsByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'contacts',
    'contactCount',
  };
}

