//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class HomeApi {
  HomeApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Feedi listeler
  ///
  /// Şu anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [FeedDateEnum] dateType (required):
  ///   Date range to filter the feed
  ///
  /// * [String] categories (required):
  ///
  /// * [int] locationCoeff:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  ///
  /// * [double] lngEnd:
  ///
  /// * [double] lngStart:
  ///
  /// * [double] latEnd:
  ///
  /// * [double] latStart:
  ///
  /// * [Object] endDate:
  ///   End date for the feed
  ///
  /// * [Object] startDate:
  ///   Start date for the feed
  Future<Response> feedWithHttpInfo(FeedDateEnum dateType, String categories, { int? locationCoeff, String? q, int? limit, int? page, double? lngEnd, double? lngStart, double? latEnd, double? latStart, Object? endDate, Object? startDate, }) async {
    // ignore: prefer_const_declarations
    final path = r'/feed';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'dateType', dateType));
      queryParams.addAll(_queryParams('', 'categories', categories));
    if (locationCoeff != null) {
      queryParams.addAll(_queryParams('', 'locationCoeff', locationCoeff));
    }
    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (lngEnd != null) {
      queryParams.addAll(_queryParams('', 'lngEnd', lngEnd));
    }
    if (lngStart != null) {
      queryParams.addAll(_queryParams('', 'lngStart', lngStart));
    }
    if (latEnd != null) {
      queryParams.addAll(_queryParams('', 'latEnd', latEnd));
    }
    if (latStart != null) {
      queryParams.addAll(_queryParams('', 'latStart', latStart));
    }
    if (endDate != null) {
      queryParams.addAll(_queryParams('', 'endDate', endDate));
    }
    if (startDate != null) {
      queryParams.addAll(_queryParams('', 'startDate', startDate));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Feedi listeler
  ///
  /// Şu anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.
  ///
  /// Parameters:
  ///
  /// * [FeedDateEnum] dateType (required):
  ///   Date range to filter the feed
  ///
  /// * [String] categories (required):
  ///
  /// * [int] locationCoeff:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  ///
  /// * [double] lngEnd:
  ///
  /// * [double] lngStart:
  ///
  /// * [double] latEnd:
  ///
  /// * [double] latStart:
  ///
  /// * [Object] endDate:
  ///   End date for the feed
  ///
  /// * [Object] startDate:
  ///   Start date for the feed
  Future<FeedReturn?> feed(FeedDateEnum dateType, String categories, { int? locationCoeff, String? q, int? limit, int? page, double? lngEnd, double? lngStart, double? latEnd, double? latStart, Object? endDate, Object? startDate, }) async {
    final response = await feedWithHttpInfo(dateType, categories,  locationCoeff: locationCoeff, q: q, limit: limit, page: page, lngEnd: lngEnd, lngStart: lngStart, latEnd: latEnd, latStart: latStart, endDate: endDate, startDate: startDate, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'FeedReturn',) as FeedReturn;
    
    }
    return null;
  }

  /// Mapi listeler
  ///
  /// Şu anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] startDate (required):
  ///   Start date for the ivents on map
  ///
  /// * [String] endDate (required):
  ///   End date for the ivents on map
  ///
  /// * [double] latStart (required):
  ///
  /// * [double] latEnd (required):
  ///
  /// * [double] lngStart (required):
  ///
  /// * [double] lngEnd (required):
  ///
  /// * [int] limit:
  Future<Response> mapWithHttpInfo(String startDate, String endDate, double latStart, double latEnd, double lngStart, double lngEnd, { int? limit, }) async {
    // ignore: prefer_const_declarations
    final path = r'/map';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'startDate', startDate));
      queryParams.addAll(_queryParams('', 'endDate', endDate));
      queryParams.addAll(_queryParams('', 'latStart', latStart));
      queryParams.addAll(_queryParams('', 'latEnd', latEnd));
      queryParams.addAll(_queryParams('', 'lngStart', lngStart));
      queryParams.addAll(_queryParams('', 'lngEnd', lngEnd));
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Mapi listeler
  ///
  /// Şu anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.
  ///
  /// Parameters:
  ///
  /// * [String] startDate (required):
  ///   Start date for the ivents on map
  ///
  /// * [String] endDate (required):
  ///   End date for the ivents on map
  ///
  /// * [double] latStart (required):
  ///
  /// * [double] latEnd (required):
  ///
  /// * [double] lngStart (required):
  ///
  /// * [double] lngEnd (required):
  ///
  /// * [int] limit:
  Future<MapReturn?> map(String startDate, String endDate, double latStart, double latEnd, double lngStart, double lngEnd, { int? limit, }) async {
    final response = await mapWithHttpInfo(startDate, endDate, latStart, latEnd, lngStart, lngEnd,  limit: limit, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'MapReturn',) as MapReturn;
    
    }
    return null;
  }

  /// Uygulamada arama yapar
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchAccountWithHttpInfo(String q, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/searchAccount';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'q', q));
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Uygulamada arama yapar
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchAccountReturn?> searchAccount(String q, { int? limit, int? page, }) async {
    final response = await searchAccountWithHttpInfo(q,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchAccountReturn',) as SearchAccountReturn;
    
    }
    return null;
  }

  /// Uygulamada arama yapar
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchIventWithHttpInfo(String q, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/searchIvent';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'q', q));
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Uygulamada arama yapar
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchIventReturn?> searchIvent(String q, { int? limit, int? page, }) async {
    final response = await searchIventWithHttpInfo(q,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchIventReturn',) as SearchIventReturn;
    
    }
    return null;
  }
}

