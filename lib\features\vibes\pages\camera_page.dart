import 'dart:io';
import 'dart:math';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/app/routes/vibes.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraPage extends StatefulWidget {
  const CameraPage({super.key});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> with WidgetsBindingObserver {
  final _vibeUploadController = Get.find<VibeUploadController>();

  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isLoading = true;
  bool _isRecording = false;
  bool _isFlashOn = false;
  CameraLensDirection _direction = CameraLensDirection.back;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initCamera();
  }

  @override
  void dispose() {
    _controller?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }
    if (state == AppLifecycleState.inactive) {
      _controller?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      if (_controller != null) {
        _onNewCameraSelected(_controller!.description);
      }
    }
  }

  Future<void> _initCamera() async {
    // Request permissions
    await [Permission.camera, Permission.storage].request();

    try {
      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        final frontCamera = _cameras!.firstWhere(
          (camera) => camera.lensDirection == _direction,
          orElse: () => _cameras!.first,
        );
        await _onNewCameraSelected(frontCamera);
      }
    } catch (e) {
      print(e);
    }

    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _onNewCameraSelected(CameraDescription description) async {
    if (_controller != null) {
      await _controller!.dispose();
    }

    _controller = CameraController(
      description,
      ResolutionPreset.medium,
      enableAudio: true,
    );

    try {
      await _controller!.initialize();
    } catch (e) {
      print(e);
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      return;
    }

    try {
      final Directory extDir = await getTemporaryDirectory();
      final String dirPath = '${extDir.path}/Pictures/flutter_test';
      await Directory(dirPath).create(recursive: true);
      final String filePath = '$dirPath/${DateTime.now().millisecondsSinceEpoch}.jpg';

      if (_controller!.value.isTakingPicture) {
        return;
      }

      final XFile picture = await _controller!.takePicture();
      await picture.saveTo(filePath);
      _vibeUploadController.capturedMediaPath = filePath;
      Get.toNamed(VibesRoutes.VIBE_UPLOAD_PAGE);
    } catch (e) {
      print(e);
    }
  }

  Future<void> _toggleRecording() async {
    if (_isRecording) {
      final XFile? file = await _controller!.stopVideoRecording();
      setState(() => _isRecording = false);
      if (file != null) {
        // Handle the recorded video file
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Video saved to ${file.path}')),
        );
      }
    } else {
      try {
        final Directory extDir = await getTemporaryDirectory();
        final String dirPath = '${extDir.path}/Movies/flutter_test';
        await Directory(dirPath).create(recursive: true);
        // final String filePath = '$dirPath/${DateTime.now().millisecondsSinceEpoch}.mp4';

        await _controller!.startVideoRecording();
        setState(() => _isRecording = true);
      } catch (e) {
        print(e);
      }
    }
  }

  Future<void> _toggleFlash() async {
    if (_controller == null || !_controller!.value.isInitialized) return;
    if (_direction != CameraLensDirection.back) return;

    try {
      await _controller!.setFlashMode(
        _isFlashOn ? FlashMode.off : FlashMode.torch,
      );
      setState(() => _isFlashOn = !_isFlashOn);
    } catch (e) {
      print(e);
    }
  }

  Future<void> _switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) return;

    final newDirection = _direction == CameraLensDirection.back ? CameraLensDirection.front : CameraLensDirection.back;

    final newCamera = _cameras!.firstWhere(
      (camera) => camera.lensDirection == newDirection,
    );
    _vibeUploadController.isFrontCamera = newDirection == CameraLensDirection.front;

    _direction = newDirection;
    await _onNewCameraSelected(newCamera);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return Scaffold(
      body: Stack(
        children: [
          _buildCamera(),
          _buildCameraOverlay(),
        ],
      ),
    );
  }

  Widget _buildCamera() {
    final isFrontCamera = _direction == CameraLensDirection.front;
    final size = MediaQuery.of(context).size;

    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.rotationY(isFrontCamera ? pi : 0),
      child: Container(
        color: AppColors.black,
        child: SizedBox(
          width: size.width,
          height: size.height,
          child: FittedBox(
            fit: BoxFit.cover,
            child: SizedBox(
              width: _controller!.value.previewSize!.height,
              height: _controller!.value.previewSize!.width,
              child: CameraPreview(_controller!),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCameraOverlay() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: SharedButtons.backButton(),
          ),
          if (_direction == CameraLensDirection.back)
            Align(
              alignment: Alignment.topCenter,
              child: IaCircularButton(
                iconPath: _isFlashOn ? AppAssets.flashOn : AppAssets.flashOff,
                backgroundColor: AppColors.transparent,
                onPressed: _toggleFlash,
              ),
            ),
          Align(
            alignment: Alignment.bottomLeft,
            child: IaCircularButton(
              iconPath: AppAssets.cameraGallery,
              onPressed: () => Get.toNamed(VibesRoutes.GALLERY_PAGE),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: _buildPhotoCaptureButton(),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: IaCircularButton(
              iconPath: AppAssets.switchCamera,
              onPressed: _switchCamera,
            ),
          ),
        ],
      ),
    );
  }

  GestureDetector _buildPhotoCaptureButton() {
    return GestureDetector(
      onTap: _takePicture,
      onLongPress: _toggleRecording,
      onLongPressEnd: (details) => _toggleRecording(),
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          color: AppColors.transparent,
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColors.white,
            width: 4,
          ),
        ),
        child: Center(
          child: Container(
            height: 44,
            decoration: const BoxDecoration(
              color: AppColors.white,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ),
    );
  }
}
