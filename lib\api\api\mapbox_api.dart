//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class MapboxApi {
  MapboxApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Search places by category
  ///
  /// Search for places within a specific category
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] canonicalCategoryId (required):
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [String] proximity:
  ///   Proximity bias as longitude,latitude
  ///
  /// * [String] bbox:
  ///   Bounding box as minX,minY,maxX,maxY
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  ///
  /// * [String] poiCategoryExclusions:
  ///   POI categories to exclude
  ///
  /// * [String] sarType:
  ///   Search along route type
  ///
  /// * [String] route:
  ///   Route geometry for search along route
  ///
  /// * [String] routeGeometry:
  ///   Route geometry format
  ///
  /// * [int] timeDeviation:
  ///   Time deviation in minutes
  Future<Response> searchBoxCategoryWithHttpInfo(String canonicalCategoryId, { String? language, String? proximity, String? bbox, String? country, String? types, String? poiCategoryExclusions, String? sarType, String? route, String? routeGeometry, int? timeDeviation, }) async {
    // ignore: prefer_const_declarations
    final path = r'/mapbox/category/{canonicalCategoryId}'
      .replaceAll('{canonicalCategoryId}', canonicalCategoryId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
    if (proximity != null) {
      queryParams.addAll(_queryParams('', 'proximity', proximity));
    }
    if (bbox != null) {
      queryParams.addAll(_queryParams('', 'bbox', bbox));
    }
    if (country != null) {
      queryParams.addAll(_queryParams('', 'country', country));
    }
    if (types != null) {
      queryParams.addAll(_queryParams('', 'types', types));
    }
    if (poiCategoryExclusions != null) {
      queryParams.addAll(_queryParams('', 'poiCategoryExclusions', poiCategoryExclusions));
    }
    if (sarType != null) {
      queryParams.addAll(_queryParams('', 'sarType', sarType));
    }
    if (route != null) {
      queryParams.addAll(_queryParams('', 'route', route));
    }
    if (routeGeometry != null) {
      queryParams.addAll(_queryParams('', 'routeGeometry', routeGeometry));
    }
    if (timeDeviation != null) {
      queryParams.addAll(_queryParams('', 'timeDeviation', timeDeviation));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Search places by category
  ///
  /// Search for places within a specific category
  ///
  /// Parameters:
  ///
  /// * [String] canonicalCategoryId (required):
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [String] proximity:
  ///   Proximity bias as longitude,latitude
  ///
  /// * [String] bbox:
  ///   Bounding box as minX,minY,maxX,maxY
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  ///
  /// * [String] poiCategoryExclusions:
  ///   POI categories to exclude
  ///
  /// * [String] sarType:
  ///   Search along route type
  ///
  /// * [String] route:
  ///   Route geometry for search along route
  ///
  /// * [String] routeGeometry:
  ///   Route geometry format
  ///
  /// * [int] timeDeviation:
  ///   Time deviation in minutes
  Future<SearchBoxCategoryReturn?> searchBoxCategory(String canonicalCategoryId, { String? language, String? proximity, String? bbox, String? country, String? types, String? poiCategoryExclusions, String? sarType, String? route, String? routeGeometry, int? timeDeviation, }) async {
    final response = await searchBoxCategoryWithHttpInfo(canonicalCategoryId,  language: language, proximity: proximity, bbox: bbox, country: country, types: types, poiCategoryExclusions: poiCategoryExclusions, sarType: sarType, route: route, routeGeometry: routeGeometry, timeDeviation: timeDeviation, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchBoxCategoryReturn',) as SearchBoxCategoryReturn;
    
    }
    return null;
  }

  /// List available categories
  ///
  /// Returns a list of all available POI categories
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  Future<Response> searchBoxCategoryListWithHttpInfo({ String? language, }) async {
    // ignore: prefer_const_declarations
    final path = r'/mapbox/categories';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// List available categories
  ///
  /// Returns a list of all available POI categories
  ///
  /// Parameters:
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  Future<SearchBoxCategoryListReturn?> searchBoxCategoryList({ String? language, }) async {
    final response = await searchBoxCategoryListWithHttpInfo( language: language, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchBoxCategoryListReturn',) as SearchBoxCategoryListReturn;
    
    }
    return null;
  }

  /// Forward geocoding search
  ///
  /// Search for places using forward geocoding
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///   Search query string
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [int] limit:
  ///   Maximum number of results
  ///
  /// * [String] proximity:
  ///   Proximity bias as longitude,latitude
  ///
  /// * [String] bbox:
  ///   Bounding box as minX,minY,maxX,maxY
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  ///
  /// * [String] poiCategory:
  ///   POI category filter
  ///
  /// * [String] poiCategoryExclusions:
  ///   POI categories to exclude
  ///
  /// * [String] autoComplete:
  ///   Auto-complete setting
  ///
  /// * [String] etaType:
  ///   ETA calculation type
  ///
  /// * [String] navigationProfile:
  ///   Navigation profile for ETA
  ///
  /// * [String] origin:
  ///   Origin point for ETA as longitude,latitude
  Future<Response> searchBoxForwardWithHttpInfo(String q, { String? language, int? limit, String? proximity, String? bbox, String? country, String? types, String? poiCategory, String? poiCategoryExclusions, String? autoComplete, String? etaType, String? navigationProfile, String? origin, }) async {
    // ignore: prefer_const_declarations
    final path = r'/mapbox/forward';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'q', q));
    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (proximity != null) {
      queryParams.addAll(_queryParams('', 'proximity', proximity));
    }
    if (bbox != null) {
      queryParams.addAll(_queryParams('', 'bbox', bbox));
    }
    if (country != null) {
      queryParams.addAll(_queryParams('', 'country', country));
    }
    if (types != null) {
      queryParams.addAll(_queryParams('', 'types', types));
    }
    if (poiCategory != null) {
      queryParams.addAll(_queryParams('', 'poiCategory', poiCategory));
    }
    if (poiCategoryExclusions != null) {
      queryParams.addAll(_queryParams('', 'poiCategoryExclusions', poiCategoryExclusions));
    }
    if (autoComplete != null) {
      queryParams.addAll(_queryParams('', 'autoComplete', autoComplete));
    }
    if (etaType != null) {
      queryParams.addAll(_queryParams('', 'etaType', etaType));
    }
    if (navigationProfile != null) {
      queryParams.addAll(_queryParams('', 'navigationProfile', navigationProfile));
    }
    if (origin != null) {
      queryParams.addAll(_queryParams('', 'origin', origin));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Forward geocoding search
  ///
  /// Search for places using forward geocoding
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///   Search query string
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [int] limit:
  ///   Maximum number of results
  ///
  /// * [String] proximity:
  ///   Proximity bias as longitude,latitude
  ///
  /// * [String] bbox:
  ///   Bounding box as minX,minY,maxX,maxY
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  ///
  /// * [String] poiCategory:
  ///   POI category filter
  ///
  /// * [String] poiCategoryExclusions:
  ///   POI categories to exclude
  ///
  /// * [String] autoComplete:
  ///   Auto-complete setting
  ///
  /// * [String] etaType:
  ///   ETA calculation type
  ///
  /// * [String] navigationProfile:
  ///   Navigation profile for ETA
  ///
  /// * [String] origin:
  ///   Origin point for ETA as longitude,latitude
  Future<SearchBoxForwardReturn?> searchBoxForward(String q, { String? language, int? limit, String? proximity, String? bbox, String? country, String? types, String? poiCategory, String? poiCategoryExclusions, String? autoComplete, String? etaType, String? navigationProfile, String? origin, }) async {
    final response = await searchBoxForwardWithHttpInfo(q,  language: language, limit: limit, proximity: proximity, bbox: bbox, country: country, types: types, poiCategory: poiCategory, poiCategoryExclusions: poiCategoryExclusions, autoComplete: autoComplete, etaType: etaType, navigationProfile: navigationProfile, origin: origin, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchBoxForwardReturn',) as SearchBoxForwardReturn;
    
    }
    return null;
  }

  /// Retrieve detailed information about a place
  ///
  /// Returns detailed information about a specific place using its Mapbox ID
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] sessionToken (required):
  ///   Session token for grouping requests
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [String] etaType:
  ///   ETA calculation type
  ///
  /// * [String] navigationProfile:
  ///   Navigation profile for ETA
  ///
  /// * [String] origin:
  ///   Origin point for ETA as longitude,latitude
  Future<Response> searchBoxRetrieveWithHttpInfo(String id, String sessionToken, { String? language, String? etaType, String? navigationProfile, String? origin, }) async {
    // ignore: prefer_const_declarations
    final path = r'/mapbox/retrieve/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'sessionToken', sessionToken));
    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
    if (etaType != null) {
      queryParams.addAll(_queryParams('', 'etaType', etaType));
    }
    if (navigationProfile != null) {
      queryParams.addAll(_queryParams('', 'navigationProfile', navigationProfile));
    }
    if (origin != null) {
      queryParams.addAll(_queryParams('', 'origin', origin));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Retrieve detailed information about a place
  ///
  /// Returns detailed information about a specific place using its Mapbox ID
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] sessionToken (required):
  ///   Session token for grouping requests
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [String] etaType:
  ///   ETA calculation type
  ///
  /// * [String] navigationProfile:
  ///   Navigation profile for ETA
  ///
  /// * [String] origin:
  ///   Origin point for ETA as longitude,latitude
  Future<SearchBoxRetrieveReturn?> searchBoxRetrieve(String id, String sessionToken, { String? language, String? etaType, String? navigationProfile, String? origin, }) async {
    final response = await searchBoxRetrieveWithHttpInfo(id, sessionToken,  language: language, etaType: etaType, navigationProfile: navigationProfile, origin: origin, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchBoxRetrieveReturn',) as SearchBoxRetrieveReturn;
    
    }
    return null;
  }

  /// Reverse geocoding search
  ///
  /// Find places near a specific coordinate
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [double] longitude (required):
  ///   Longitude coordinate
  ///
  /// * [double] latitude (required):
  ///   Latitude coordinate
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [int] limit:
  ///   Maximum number of results
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  Future<Response> searchBoxReverseWithHttpInfo(double longitude, double latitude, { String? language, int? limit, String? country, String? types, }) async {
    // ignore: prefer_const_declarations
    final path = r'/mapbox/reverse';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'longitude', longitude));
      queryParams.addAll(_queryParams('', 'latitude', latitude));
    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (country != null) {
      queryParams.addAll(_queryParams('', 'country', country));
    }
    if (types != null) {
      queryParams.addAll(_queryParams('', 'types', types));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Reverse geocoding search
  ///
  /// Find places near a specific coordinate
  ///
  /// Parameters:
  ///
  /// * [double] longitude (required):
  ///   Longitude coordinate
  ///
  /// * [double] latitude (required):
  ///   Latitude coordinate
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [int] limit:
  ///   Maximum number of results
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  Future<SearchBoxReverseReturn?> searchBoxReverse(double longitude, double latitude, { String? language, int? limit, String? country, String? types, }) async {
    final response = await searchBoxReverseWithHttpInfo(longitude, latitude,  language: language, limit: limit, country: country, types: types, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchBoxReverseReturn',) as SearchBoxReverseReturn;
    
    }
    return null;
  }

  /// Search for place suggestions
  ///
  /// Returns a list of place suggestions based on the search query
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///   Search query string
  ///
  /// * [String] sessionToken (required):
  ///   Session token for grouping requests
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [int] limit:
  ///   Maximum number of results
  ///
  /// * [String] proximity:
  ///   Proximity bias as longitude,latitude
  ///
  /// * [String] bbox:
  ///   Bounding box as minX,minY,maxX,maxY
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  ///
  /// * [String] poiCategory:
  ///   POI category filter
  ///
  /// * [String] poiCategoryExclusions:
  ///   POI categories to exclude
  ///
  /// * [String] etaType:
  ///   ETA calculation type
  ///
  /// * [String] navigationProfile:
  ///   Navigation profile for ETA
  ///
  /// * [String] origin:
  ///   Origin point for ETA as longitude,latitude
  Future<Response> searchBoxSuggestWithHttpInfo(String q, String sessionToken, { String? language, int? limit, String? proximity, String? bbox, String? country, String? types, String? poiCategory, String? poiCategoryExclusions, String? etaType, String? navigationProfile, String? origin, }) async {
    // ignore: prefer_const_declarations
    final path = r'/mapbox/suggest';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'q', q));
      queryParams.addAll(_queryParams('', 'sessionToken', sessionToken));
    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (proximity != null) {
      queryParams.addAll(_queryParams('', 'proximity', proximity));
    }
    if (bbox != null) {
      queryParams.addAll(_queryParams('', 'bbox', bbox));
    }
    if (country != null) {
      queryParams.addAll(_queryParams('', 'country', country));
    }
    if (types != null) {
      queryParams.addAll(_queryParams('', 'types', types));
    }
    if (poiCategory != null) {
      queryParams.addAll(_queryParams('', 'poiCategory', poiCategory));
    }
    if (poiCategoryExclusions != null) {
      queryParams.addAll(_queryParams('', 'poiCategoryExclusions', poiCategoryExclusions));
    }
    if (etaType != null) {
      queryParams.addAll(_queryParams('', 'etaType', etaType));
    }
    if (navigationProfile != null) {
      queryParams.addAll(_queryParams('', 'navigationProfile', navigationProfile));
    }
    if (origin != null) {
      queryParams.addAll(_queryParams('', 'origin', origin));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Search for place suggestions
  ///
  /// Returns a list of place suggestions based on the search query
  ///
  /// Parameters:
  ///
  /// * [String] q (required):
  ///   Search query string
  ///
  /// * [String] sessionToken (required):
  ///   Session token for grouping requests
  ///
  /// * [String] language:
  ///   Language code (ISO 639-1)
  ///
  /// * [int] limit:
  ///   Maximum number of results
  ///
  /// * [String] proximity:
  ///   Proximity bias as longitude,latitude
  ///
  /// * [String] bbox:
  ///   Bounding box as minX,minY,maxX,maxY
  ///
  /// * [String] country:
  ///   Country code filter (ISO 3166-1 alpha-2)
  ///
  /// * [String] types:
  ///   Feature types to include
  ///
  /// * [String] poiCategory:
  ///   POI category filter
  ///
  /// * [String] poiCategoryExclusions:
  ///   POI categories to exclude
  ///
  /// * [String] etaType:
  ///   ETA calculation type
  ///
  /// * [String] navigationProfile:
  ///   Navigation profile for ETA
  ///
  /// * [String] origin:
  ///   Origin point for ETA as longitude,latitude
  Future<SearchBoxSuggestReturn?> searchBoxSuggest(String q, String sessionToken, { String? language, int? limit, String? proximity, String? bbox, String? country, String? types, String? poiCategory, String? poiCategoryExclusions, String? etaType, String? navigationProfile, String? origin, }) async {
    final response = await searchBoxSuggestWithHttpInfo(q, sessionToken,  language: language, limit: limit, proximity: proximity, bbox: bbox, country: country, types: types, poiCategory: poiCategory, poiCategoryExclusions: poiCategoryExclusions, etaType: etaType, navigationProfile: navigationProfile, origin: origin, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchBoxSuggestReturn',) as SearchBoxSuggestReturn;
    
    }
    return null;
  }
}

