import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';

/// Utility class for showing loading overlays across the app
///
/// Provides methods for showing and hiding full-screen loading indicators
/// with a semi-transparent background.
class LoadingOverlay {
  static OverlayEntry? _overlayEntry;

  /// Shows a circular loading indicator in the center of the screen
  /// with a semi-transparent black background
  ///
  /// [context] - The build context to show the overlay in
  static void show(BuildContext context) {
    if (_overlayEntry != null) {
      // Don't show multiple overlays
      return;
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => const Material(
        color: AppColors.semiTransparentBlack,
        child: IaLoadingIndicator(showLoadingText: false),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// Hides the currently shown loading overlay
  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
