# openapi.api.CommentsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createComment**](CommentsApi.md#commentscontrollercreatecomment) | **POST** /comments/create | Yorum oluşturur
[**deleteCommentByCommentId**](CommentsApi.md#commentscontrollerdeletecommentbycommentid) | **DELETE** /comments/{id}/delete | Yorumun IDsi ile yorumu siler


# **createComment**
> CreateCommentReturn createComment(createCommentDto)

Yorum oluşturur

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = CommentsApi();
final createCommentDto = CreateCommentDto(); // CreateCommentDto | 

try {
    final result = api_instance.createComment(createCommentDto);
    print(result);
} catch (e) {
    print('Exception when calling CommentsApi->createComment: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createCommentDto** | [**CreateCommentDto**](CreateCommentDto.md)|  | 

### Return type

[**CreateCommentReturn**](CreateCommentReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteCommentByCommentId**
> deleteCommentByCommentId(id)

Yorumun IDsi ile yorumu siler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = CommentsApi();
final id = id_example; // String | 

try {
    api_instance.deleteCommentByCommentId(id);
} catch (e) {
    print('Exception when calling CommentsApi->deleteCommentByCommentId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


