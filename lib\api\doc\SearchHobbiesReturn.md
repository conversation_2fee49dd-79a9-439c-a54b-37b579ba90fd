# openapi.model.SearchHobbiesReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**hobbies** | [**List<HobbyItem>**](HobbyItem.md) | List of hobby categories | [default to const []]
**hobbyCount** | **int** | Total number of hobby categories | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



