# openapi.api.AuthApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**logout**](AuthApi.md#authcontrollerlogout) | **POST** /auth/logout | Hesaptan çıkış yapar
[**sendVerificationCode**](AuthApi.md#authcontrollersendverificationcode) | **POST** /auth/send-verification-code | Telefon numarasına onay kodu gönderir
[**validate**](AuthApi.md#authcontrollervalidate) | **POST** /auth/validate | Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma say<PERSON>, de<PERSON><PERSON><PERSON> giriş sayfasına yönlendirir


# **logout**
> logout(id)

Hesaptan çı<PERSON> yapar

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = AuthApi();
final id = id_example; // String | 

try {
    api_instance.logout(id);
} catch (e) {
    print('Exception when calling AuthApi->logout: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **sendVerificationCode**
> sendVerificationCode(sendVerificationCodeDto)

Telefon numarasına onay kodu gönderir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = AuthApi();
final sendVerificationCodeDto = SendVerificationCodeDto(); // SendVerificationCodeDto | 

try {
    api_instance.sendVerificationCode(sendVerificationCodeDto);
} catch (e) {
    print('Exception when calling AuthApi->sendVerificationCode: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **sendVerificationCodeDto** | [**SendVerificationCodeDto**](SendVerificationCodeDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **validate**
> ValidateReturn validate(validateDto)

Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma sayfasına, değilse giriş sayfasına yönlendirir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = AuthApi();
final validateDto = ValidateDto(); // ValidateDto | 

try {
    final result = api_instance.validate(validateDto);
    print(result);
} catch (e) {
    print('Exception when calling AuthApi->validate: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **validateDto** | [**ValidateDto**](ValidateDto.md)|  | 

### Return type

[**ValidateReturn**](ValidateReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


