//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxSuggestReturn {
  /// Returns a new [SearchBoxSuggestReturn] instance.
  SearchBoxSuggestReturn({
    this.suggestions = const [],
    required this.attribution,
  });

  /// List of suggested places
  List<SearchBoxSuggestFeature> suggestions;

  /// Attribution text
  String attribution;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxSuggestReturn &&
    _deepEquality.equals(other.suggestions, suggestions) &&
    other.attribution == attribution;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (suggestions.hashCode) +
    (attribution.hashCode);

  @override
  String toString() => 'SearchBoxSuggestReturn[suggestions=$suggestions, attribution=$attribution]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'suggestions'] = this.suggestions;
      json[r'attribution'] = this.attribution;
    return json;
  }

  /// Returns a new [SearchBoxSuggestReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxSuggestReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxSuggestReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxSuggestReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxSuggestReturn(
        suggestions: SearchBoxSuggestFeature.listFromJson(json[r'suggestions']),
        attribution: mapValueOfType<String>(json, r'attribution')!,
      );
    }
    return null;
  }

  static List<SearchBoxSuggestReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxSuggestReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxSuggestReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxSuggestReturn> mapFromJson(dynamic json) {
    final map = <String, SearchBoxSuggestReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxSuggestReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxSuggestReturn-objects as value to a dart map
  static Map<String, List<SearchBoxSuggestReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxSuggestReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxSuggestReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'suggestions',
    'attribution',
  };
}

