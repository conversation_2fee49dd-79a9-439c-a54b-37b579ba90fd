# openapi.model.GetGroupByGroupIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groupId** | **String** | Unique identifier of the group | 
**groupName** | **String** | Name of the group | 
**thumbnailUrl** | **String** | URL to the group thumbnail image | [optional] 
**members** | [**List<UserListItemWithGroupRole>**](UserListItemWithGroupRole.md) | List of group members with their roles and friendship status | [default to const []]
**memberCount** | **int** | Total number of group members | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



