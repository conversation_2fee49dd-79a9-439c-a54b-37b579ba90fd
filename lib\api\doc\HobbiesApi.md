# openapi.api.HobbiesApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**addHobbiesByHobbyId**](HobbiesApi.md#hobbiescontrolleraddhobbiesbyhobbyid) | **POST** /hobbies/add | Yeni hobiler ekler
[**searchHobbies**](HobbiesApi.md#hobbiescontrollersearchhobbies) | **GET** /hobbies/search | Seçilebilecek hobileri listeler


# **addHobbiesByHobbyId**
> addHobbiesByHobbyId(addHobbiesByHobbyIdDto)

Yeni hobiler ekler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = HobbiesApi();
final addHobbiesByHobbyIdDto = AddHobbiesByHobbyIdDto(); // AddHobbiesByHobbyIdDto | 

try {
    api_instance.addHobbiesByHobbyId(addHobbiesByHobbyIdDto);
} catch (e) {
    print('Exception when calling HobbiesApi->addHobbiesByHobbyId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **addHobbiesByHobbyIdDto** | [**AddHobbiesByHobbyIdDto**](AddHobbiesByHobbyIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchHobbies**
> SearchHobbiesReturn searchHobbies(type, q, limit, page)

Seçilebilecek hobileri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = HobbiesApi();
final type = ; // HobbiesSearchOriginEnum | Type of search to perform - either for the profile or for the default search
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchHobbies(type, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling HobbiesApi->searchHobbies: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **type** | [**HobbiesSearchOriginEnum**](.md)| Type of search to perform - either for the profile or for the default search | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchHobbiesReturn**](SearchHobbiesReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


