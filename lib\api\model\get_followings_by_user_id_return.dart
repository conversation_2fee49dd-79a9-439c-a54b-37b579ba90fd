//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetFollowingsByUserIdReturn {
  /// Returns a new [GetFollowingsByUserIdReturn] instance.
  GetFollowingsByUserIdReturn({
    this.followings = const [],
    required this.followingCount,
  });

  /// List of users being followed
  List<UserListItemWithRelationshipStatus> followings;

  /// Total number of users being followed
  ///
  /// Minimum value: 0
  int followingCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetFollowingsByUserIdReturn &&
    _deepEquality.equals(other.followings, followings) &&
    other.followingCount == followingCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (followings.hashCode) +
    (followingCount.hashCode);

  @override
  String toString() => 'GetFollowingsByUserIdReturn[followings=$followings, followingCount=$followingCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'followings'] = this.followings;
      json[r'followingCount'] = this.followingCount;
    return json;
  }

  /// Returns a new [GetFollowingsByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetFollowingsByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetFollowingsByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetFollowingsByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetFollowingsByUserIdReturn(
        followings: UserListItemWithRelationshipStatus.listFromJson(json[r'followings']),
        followingCount: mapValueOfType<int>(json, r'followingCount')!,
      );
    }
    return null;
  }

  static List<GetFollowingsByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetFollowingsByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetFollowingsByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetFollowingsByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetFollowingsByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetFollowingsByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetFollowingsByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetFollowingsByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetFollowingsByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetFollowingsByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'followings',
    'followingCount',
  };
}

