//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxCoordinates {
  /// Returns a new [SearchBoxCoordinates] instance.
  SearchBoxCoordinates({
    required this.longitude,
    required this.latitude,
    this.accuracy,
    this.routablePoints = const [],
  });

  /// Longitude coordinate
  double longitude;

  /// Latitude coordinate
  double latitude;

  /// Accuracy of the coordinates
  String? accuracy;

  /// Routable points for navigation
  List<SearchBoxRoutablePoints>? routablePoints;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxCoordinates &&
    other.longitude == longitude &&
    other.latitude == latitude &&
    other.accuracy == accuracy &&
    _deepEquality.equals(other.routablePoints, routablePoints);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (longitude.hashCode) +
    (latitude.hashCode) +
    (accuracy == null ? 0 : accuracy!.hashCode) +
    (routablePoints == null ? 0 : routablePoints!.hashCode);

  @override
  String toString() => 'SearchBoxCoordinates[longitude=$longitude, latitude=$latitude, accuracy=$accuracy, routablePoints=$routablePoints]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'longitude'] = this.longitude;
      json[r'latitude'] = this.latitude;
    if (this.accuracy != null) {
      json[r'accuracy'] = this.accuracy;
    } else {
      json[r'accuracy'] = null;
    }
    if (this.routablePoints != null) {
      json[r'routable_points'] = this.routablePoints;
    } else {
      json[r'routable_points'] = null;
    }
    return json;
  }

  /// Returns a new [SearchBoxCoordinates] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxCoordinates? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxCoordinates[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxCoordinates[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxCoordinates(
        longitude: mapValueOfType<double>(json, r'longitude')!,
        latitude: mapValueOfType<double>(json, r'latitude')!,
        accuracy: mapValueOfType<String>(json, r'accuracy'),
        routablePoints: SearchBoxRoutablePoints.listFromJson(json[r'routable_points']),
      );
    }
    return null;
  }

  static List<SearchBoxCoordinates> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxCoordinates>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxCoordinates.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxCoordinates> mapFromJson(dynamic json) {
    final map = <String, SearchBoxCoordinates>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxCoordinates.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxCoordinates-objects as value to a dart map
  static Map<String, List<SearchBoxCoordinates>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxCoordinates>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxCoordinates.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'longitude',
    'latitude',
  };
}

