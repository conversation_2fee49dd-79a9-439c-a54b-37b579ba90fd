# openapi.model.GetIventPageByIventIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**thumbnailUrl** | **String** | URL to the ivent thumbnail image | [optional] 
**locationId** | **String** | UUID of the location | 
**mapboxId** | **String** | Mapbox place ID for the location | 
**locationName** | **String** | Name of the location | 
**dates** | **List<String>** | List of dates for the ivent, in ISO 8601 date-time format | [default to const []]
**description** | **String** | Detailed description of the ivent | [optional] 
**categoryTag** | **String** | Name of the category tag | 
**tagNames** | **List<String>** | List of hobby tags associated with the ivent | [default to const []]
**creatorId** | **String** | UUID of the ivent creator | 
**creatorType** | [**IventCreatorTypeEnum**](IventCreatorTypeEnum.md) |  | 
**creatorUsername** | **String** | Username of the ivent creator | 
**creatorImageUrl** | **String** | URL to the ivent creator image | [optional] 
**collabNames** | **List<String>** | List of collaborator names, either page names or usernames | [default to const []]
**collabCount** | **int** | Number of collaborators | 
**memberFirstnames** | **List<String>** | List of member first names | [optional] [default to const []]
**memberAvatarUrls** | **List<String>** | List of member avatar URLs | [default to const []]
**memberCount** | **int** | Number of members | 
**isFavorited** | **bool** | Whether the ivent is favorited by the current user | [optional] 
**favoriteCount** | **int** | Number of favorites the ivent has | [optional] 
**googleFormsUrl** | **String** | URL to Google Forms for registration | [optional] 
**instagramUsername** | **String** | Instagram username for the ivent | [optional] 
**whatsappUrl** | **String** | WhatsApp group URL | [optional] 
**whatsappNumber** | **String** | WhatsApp contact number | [optional] 
**isWhatsappUrlPrivate** | **bool** | Whether the WhatsApp URL should be kept private | [optional] 
**callNumber** | **String** | Phone number for calls | [optional] 
**websiteUrl** | **String** | Website URL for the ivent | [optional] 
**viewType** | [**IventViewTypeEnum**](IventViewTypeEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



