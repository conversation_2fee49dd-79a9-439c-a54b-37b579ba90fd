# openapi.api.LocationsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getLatestLocations**](LocationsApi.md#locationscontrollergetlatestlocations) | **GET** /locations/latest | Son seçilen konumları listeler
[**searchLocations**](LocationsApi.md#locationscontrollersearchlocations) | **GET** /locations/search | Haritadan gözüken bölgedeki konumları listeler (hem GoogleMapsAPI hem de kendi konumlarımız olabilir)


# **getLatestLocations**
> GetLatestLocationsReturn getLatestLocations(limit, page)

Son seçilen konumları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = LocationsApi();
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getLatestLocations(limit, page);
    print(result);
} catch (e) {
    print('Exception when calling LocationsApi->getLatestLocations: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetLatestLocationsReturn**](GetLatestLocationsReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchLocations**
> GetLocationsReturn searchLocations(q, limit, page)

Haritadan gözüken bölgedeki konumları listeler (hem GoogleMapsAPI hem de kendi konumlarımız olabilir)

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = LocationsApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchLocations(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling LocationsApi->searchLocations: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetLocationsReturn**](GetLocationsReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


