import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/ivent_detail.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';

/// Controller for managing iVent participants
///
/// <PERSON>les loading participant data and navigation to participant-related
/// pages. Manages different participant views based on the user's relationship
/// to the iVent (creator, participant, or visitor).
class ParticipantsController extends BaseIventDetailsController {
  // Reactive state
  final _participants = Rxn<SearchParticipantsByIventIdReturn>();

  // Constructor
  ParticipantsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  // Getters
  SearchParticipantsByIventIdReturn? get participants => _participants.value;

  // Setters
  set participants(SearchParticipantsByIventIdReturn? value) => _participants.value = value;

  // Public methods

  /// Loads participants and navigates to appropriate participants page
  ///
  /// [viewType] determines which page to navigate to
  /// [participantCount] is used to check if navigation should occur
  /// [q] optional search query for filtering participants
  Future<void> getParticipantsPage(
    IventViewTypeEnum viewType,
    int participantCount, {
    String? q,
  }) async {
    try {
      // Don't navigate if no participants and default view
      if (viewType == IventViewTypeEnum.default_ && participantCount == 0) {
        return;
      }

      setLoading(true);

      participants = await authService.squadMembershipsApi.searchParticipantsByIventId(
        iventId,
        q: q,
      );

      setLoading(false);
      _navigateToParticipantsPage(viewType);
    } catch (e) {
      handleError(e, 'Katılımcılar yüklenirken bir hata oluştu.');
    }
  }

  // Private methods

  /// Navigates to the appropriate participants page based on view type
  void _navigateToParticipantsPage(IventViewTypeEnum viewType) {
    switch (viewType) {
      case IventViewTypeEnum.joined:
        Get.toNamed(IventDetayRoutes.IVENT_DETAY_SQUAD, arguments: iventId);
        break;
      case IventViewTypeEnum.created:
      default:
        Get.toNamed(IventDetayRoutes.IVENT_DETAY_KISILER, arguments: iventId);
        break;
    }
  }
}
