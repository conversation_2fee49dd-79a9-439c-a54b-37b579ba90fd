//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class HobbiesApi {
  HobbiesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Yeni hobiler ekler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [AddHobbiesByHobbyIdDto] addHobbiesByHobbyIdDto (required):
  Future<Response> addHobbiesByHobbyIdWithHttpInfo(AddHobbiesByHobbyIdDto addHobbiesByHobbyIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/hobbies/add';

    // ignore: prefer_final_locals
    Object? postBody = addHobbiesByHobbyIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Yeni hobiler ekler
  ///
  /// Parameters:
  ///
  /// * [AddHobbiesByHobbyIdDto] addHobbiesByHobbyIdDto (required):
  Future<void> addHobbiesByHobbyId(AddHobbiesByHobbyIdDto addHobbiesByHobbyIdDto,) async {
    final response = await addHobbiesByHobbyIdWithHttpInfo(addHobbiesByHobbyIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Seçilebilecek hobileri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [HobbiesSearchOriginEnum] type (required):
  ///   Type of search to perform - either for the profile or for the default search
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchHobbiesWithHttpInfo(HobbiesSearchOriginEnum type, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/hobbies/search';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'type', type));
    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Seçilebilecek hobileri listeler
  ///
  /// Parameters:
  ///
  /// * [HobbiesSearchOriginEnum] type (required):
  ///   Type of search to perform - either for the profile or for the default search
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchHobbiesReturn?> searchHobbies(HobbiesSearchOriginEnum type, { String? q, int? limit, int? page, }) async {
    final response = await searchHobbiesWithHttpInfo(type,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchHobbiesReturn',) as SearchHobbiesReturn;
    
    }
    return null;
  }
}

