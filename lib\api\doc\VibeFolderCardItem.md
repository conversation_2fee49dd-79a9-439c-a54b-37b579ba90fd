# openapi.model.VibeFolderCardItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**vibeFolderId** | **String** | UUID of the vibe folder | 
**thumbnailUrl** | **String** | URL to the vibe folder thumbnail image | [optional] 
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**vibeId** | **String** | UUID of the latest vibe in the vibe folder | 
**memberCount** | **int** | Number of members in the ivent | 
**memberFirstnames** | **List<String>** | List of member's first names in the ivent | [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



