# openapi.model.VibeItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**vibeId** | **String** | UUID of the vibe | 
**vibeFolderId** | **String** | UUID of the vibe folder | 
**mediaUrl** | **String** | URL to the vibe media | 
**mediaFormat** | [**MediaFormatEnum**](MediaFormatEnum.md) |  | 
**thumbnailUrl** | **String** | URL to the vibe thumbnail | [optional] 
**caption** | **String** | Caption of the vibe | 
**creatorId** | **String** | UUID of the creator | 
**creatorType** | [**AccountTypeEnum**](AccountTypeEnum.md) |  | 
**creatorUsername** | **String** | Username of the creator | 
**creatorAvatarUrl** | **String** | URL to the creator image | [optional] 
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**dates** | **List<String>** | List of dates for the ivent, in ISO 8601 date-time format | [default to const []]
**memberCount** | **int** | Number of members in the ivent | 
**memberFirstnames** | **List<String>** | List of member's first names in the ivent | [default to const []]
**likeCount** | **int** | Number of likes on the vibe | 
**commentCount** | **int** | Number of comments on the vibe | 
**nextVibeId** | **String** | UUID of the next vibe in the vibe folder | [optional] 
**previousVibeId** | **String** | UUID of the previous vibe in the vibe folder | [optional] 
**vibeIndex** | **int** | Index of the vibe in the vibe folder | 
**vibeCount** | **int** | Total number of vibes in the vibe folder | 
**createdAt** | **String** | Date of creation of the vibe, in ISO 8601 date-time format | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



