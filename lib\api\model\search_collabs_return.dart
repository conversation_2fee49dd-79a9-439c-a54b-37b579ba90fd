//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchCollabsReturn {
  /// Returns a new [SearchCollabsReturn] instance.
  SearchCollabsReturn({
    this.collabs = const [],
    required this.collabCount,
  });

  /// List of collaborators with their membership and friendship status
  List<CollabratorListItem> collabs;

  /// Total number of collaborators found
  ///
  /// Minimum value: 0
  int collabCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchCollabsReturn &&
    _deepEquality.equals(other.collabs, collabs) &&
    other.collabCount == collabCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (collabs.hashCode) +
    (collabCount.hashCode);

  @override
  String toString() => 'SearchCollabsReturn[collabs=$collabs, collabCount=$collabCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'collabs'] = this.collabs;
      json[r'collabCount'] = this.collabCount;
    return json;
  }

  /// Returns a new [SearchCollabsReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchCollabsReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchCollabsReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchCollabsReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchCollabsReturn(
        collabs: CollabratorListItem.listFromJson(json[r'collabs']),
        collabCount: mapValueOfType<int>(json, r'collabCount')!,
      );
    }
    return null;
  }

  static List<SearchCollabsReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchCollabsReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchCollabsReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchCollabsReturn> mapFromJson(dynamic json) {
    final map = <String, SearchCollabsReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchCollabsReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchCollabsReturn-objects as value to a dart map
  static Map<String, List<SearchCollabsReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchCollabsReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchCollabsReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'collabs',
    'collabCount',
  };
}

