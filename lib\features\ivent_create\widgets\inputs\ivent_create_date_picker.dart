import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';

/// A specialized date and time picker widget for ivent creation.
/// 
/// This widget provides a Cupertino-style date picker specifically designed
/// for selecting ivent dates and times. It includes validation to prevent
/// selecting past dates and provides a clean interface with cancel and apply actions.
/// 
/// The picker uses 24-hour format and combines both date and time selection
/// in a single interface for better user experience.
class IventCreateDatePicker extends StatefulWidget {
  /// Callback function called when the user confirms their date selection
  final Function(DateTime dateTime) onDone;
  
  /// Optional initial date and time to display in the picker
  /// If null, defaults to the current date and time
  final DateTime? initialDateTime;

  const IventCreateDatePicker({
    super.key,
    required this.onDone,
    this.initialDateTime,
  });

  @override
  State<IventCreateDatePicker> createState() => _IventCreateDatePickerState();
}

/// Private state class for the date picker widget
class _IventCreateDatePickerState extends State<IventCreateDatePicker> {
  /// Currently selected date and time
  late DateTime selectedDateTime;
  
  /// Current date and time for minimum date validation
  final DateTime now = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Initialize with provided date or current date/time
    selectedDateTime = widget.initialDateTime ?? now;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      color: AppColors.white,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(child: _buildDatePicker()),
        ],
      ),
    );
  }

  /// Builds the header section with cancel and apply buttons
  Widget _buildHeader() {
    return IaRoundedContainer(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.padding8,
        vertical: AppDimensions.padding8,
      ),
      border: const Border(
        bottom: BorderSide(color: AppColors.lightGrey),
      ),
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildCancelButton(),
          _buildApplyButton(),
        ],
      ),
    );
  }

  /// Builds the cancel button
  Widget _buildCancelButton() {
    return TextButton(
      onPressed: () => Get.back(),
      child: Text(
        'Vazgeç',
        style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
      ),
    );
  }

  /// Builds the apply button
  Widget _buildApplyButton() {
    return TextButton(
      onPressed: _handleApplyPressed,
      child: Text(
        'Uygula',
        style: AppTextStyles.size16BoldPrimary,
      ),
    );
  }

  /// Builds the Cupertino date picker
  Widget _buildDatePicker() {
    return CupertinoDatePicker(
      minimumDate: now,
      initialDateTime: widget.initialDateTime ?? now,
      mode: CupertinoDatePickerMode.dateAndTime,
      use24hFormat: true,
      onDateTimeChanged: _handleDateTimeChanged,
    );
  }

  /// Handles date/time changes in the picker
  void _handleDateTimeChanged(DateTime dateTime) {
    selectedDateTime = dateTime;
  }

  /// Handles the apply button press
  void _handleApplyPressed() {
    widget.onDone(selectedDateTime);
    Get.back();
  }
}
