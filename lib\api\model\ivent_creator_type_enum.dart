//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Type of the ivent creator
class IventCreatorTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const IventCreatorTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const user = IventCreatorTypeEnum._(r'user');
  static const page = IventCreatorTypeEnum._(r'page');
  static const distributor = IventCreatorTypeEnum._(r'distributor');

  /// List of all possible values in this [enum][IventCreatorTypeEnum].
  static const values = <IventCreatorTypeEnum>[
    user,
    page,
    distributor,
  ];

  static IventCreatorTypeEnum? fromJson(dynamic value) => IventCreatorTypeEnumTypeTransformer().decode(value);

  static List<IventCreatorTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <IventCreatorTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = IventCreatorTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [IventCreatorTypeEnum] to String,
/// and [decode] dynamic data back to [IventCreatorTypeEnum].
class IventCreatorTypeEnumTypeTransformer {
  factory IventCreatorTypeEnumTypeTransformer() => _instance ??= const IventCreatorTypeEnumTypeTransformer._();

  const IventCreatorTypeEnumTypeTransformer._();

  String encode(IventCreatorTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a IventCreatorTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  IventCreatorTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'user': return IventCreatorTypeEnum.user;
        case r'page': return IventCreatorTypeEnum.page;
        case r'distributor': return IventCreatorTypeEnum.distributor;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [IventCreatorTypeEnumTypeTransformer] instance.
  static IventCreatorTypeEnumTypeTransformer? _instance;
}

