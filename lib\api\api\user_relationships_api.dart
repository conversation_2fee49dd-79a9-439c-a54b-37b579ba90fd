//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class UserRelationshipsApi {
  UserRelationshipsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Hesap IDsi ile hesap engellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<Response> blockUserByUserIdWithHttpInfo(String userId,) async {
    // ignore: prefer_const_declarations
    final path = r'/userRelationships/{userId}/block'
      .replaceAll('{userId}', userId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap engellenir
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<void> blockUserByUserId(String userId,) async {
    final response = await blockUserByUserIdWithHttpInfo(userId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Engellenenleri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> getUserBlocklistWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/userRelationships/blocklist';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Engellenenleri listeler
  Future<GetUserBlocklistReturn?> getUserBlocklist() async {
    final response = await getUserBlocklistWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetUserBlocklistReturn',) as GetUserBlocklistReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile arkadaş daveti gönderilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<Response> inviteFriendByUserIdWithHttpInfo(String userId,) async {
    // ignore: prefer_const_declarations
    final path = r'/userRelationships/{userId}/invite'
      .replaceAll('{userId}', userId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile arkadaş daveti gönderilir
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<void> inviteFriendByUserId(String userId,) async {
    final response = await inviteFriendByUserIdWithHttpInfo(userId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile arkadaşlıktan çıkılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<Response> removeFriendByUserIdWithHttpInfo(String userId,) async {
    // ignore: prefer_const_declarations
    final path = r'/userRelationships/{userId}/remove'
      .replaceAll('{userId}', userId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile arkadaşlıktan çıkılır
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<void> removeFriendByUserId(String userId,) async {
    final response = await removeFriendByUserIdWithHttpInfo(userId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile arkadaşlar listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  ///
  /// * [FriendListingTypeEnum] type (required):
  ///   Type of friends to list - either groups or users
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchFriendsByUserIdWithHttpInfo(String userId, FriendListingTypeEnum type, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/userRelationships/{userId}/friends'
      .replaceAll('{userId}', userId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'type', type));
    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile arkadaşlar listelenir
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  ///
  /// * [FriendListingTypeEnum] type (required):
  ///   Type of friends to list - either groups or users
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchFriendsByUserIdReturn?> searchFriendsByUserId(String userId, FriendListingTypeEnum type, { String? q, int? limit, int? page, }) async {
    final response = await searchFriendsByUserIdWithHttpInfo(userId, type,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchFriendsByUserIdReturn',) as SearchFriendsByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesap engeli kaldırılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<Response> unblockUserByUserIdWithHttpInfo(String userId,) async {
    // ignore: prefer_const_declarations
    final path = r'/userRelationships/{userId}/unblock'
      .replaceAll('{userId}', userId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap engeli kaldırılır
  ///
  /// Parameters:
  ///
  /// * [String] userId (required):
  Future<void> unblockUserByUserId(String userId,) async {
    final response = await unblockUserByUserIdWithHttpInfo(userId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

