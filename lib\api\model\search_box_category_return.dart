//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxCategoryReturn {
  /// Returns a new [SearchBoxCategoryReturn] instance.
  SearchBoxCategoryReturn({
    required this.type,
    this.features = const [],
    required this.attribution,
  });

  /// Collection type
  String type;

  /// List of features in the category
  List<SearchBoxFeature> features;

  /// Attribution text
  String attribution;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxCategoryReturn &&
    other.type == type &&
    _deepEquality.equals(other.features, features) &&
    other.attribution == attribution;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (type.hashCode) +
    (features.hashCode) +
    (attribution.hashCode);

  @override
  String toString() => 'SearchBoxCategoryReturn[type=$type, features=$features, attribution=$attribution]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'type'] = this.type;
      json[r'features'] = this.features;
      json[r'attribution'] = this.attribution;
    return json;
  }

  /// Returns a new [SearchBoxCategoryReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxCategoryReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxCategoryReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxCategoryReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxCategoryReturn(
        type: mapValueOfType<String>(json, r'type')!,
        features: SearchBoxFeature.listFromJson(json[r'features']),
        attribution: mapValueOfType<String>(json, r'attribution')!,
      );
    }
    return null;
  }

  static List<SearchBoxCategoryReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxCategoryReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxCategoryReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxCategoryReturn> mapFromJson(dynamic json) {
    final map = <String, SearchBoxCategoryReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxCategoryReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxCategoryReturn-objects as value to a dart map
  static Map<String, List<SearchBoxCategoryReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxCategoryReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxCategoryReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'type',
    'features',
    'attribution',
  };
}

