//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class VibeFolderCardItem {
  /// Returns a new [VibeFolderCardItem] instance.
  VibeFolderCardItem({
    required this.vibeFolderId,
    this.thumbnailUrl,
    required this.iventId,
    required this.iventName,
    required this.vibeId,
    required this.memberCount,
    this.memberFirstnames = const [],
  });

  /// UUID of the vibe folder
  String vibeFolderId;

  /// URL to the vibe folder thumbnail image
  String? thumbnailUrl;

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// UUID of the latest vibe in the vibe folder
  String vibeId;

  /// Number of members in the ivent
  ///
  /// Minimum value: 0
  int memberCount;

  /// List of member's first names in the ivent
  List<String> memberFirstnames;

  @override
  bool operator ==(Object other) => identical(this, other) || other is VibeFolderCardItem &&
    other.vibeFolderId == vibeFolderId &&
    other.thumbnailUrl == thumbnailUrl &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    other.vibeId == vibeId &&
    other.memberCount == memberCount &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (vibeFolderId.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (iventId.hashCode) +
    (iventName.hashCode) +
    (vibeId.hashCode) +
    (memberCount.hashCode) +
    (memberFirstnames.hashCode);

  @override
  String toString() => 'VibeFolderCardItem[vibeFolderId=$vibeFolderId, thumbnailUrl=$thumbnailUrl, iventId=$iventId, iventName=$iventName, vibeId=$vibeId, memberCount=$memberCount, memberFirstnames=$memberFirstnames]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'vibeFolderId'] = this.vibeFolderId;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
      json[r'vibeId'] = this.vibeId;
      json[r'memberCount'] = this.memberCount;
      json[r'memberFirstnames'] = this.memberFirstnames;
    return json;
  }

  /// Returns a new [VibeFolderCardItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static VibeFolderCardItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "VibeFolderCardItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "VibeFolderCardItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return VibeFolderCardItem(
        vibeFolderId: mapValueOfType<String>(json, r'vibeFolderId')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        vibeId: mapValueOfType<String>(json, r'vibeId')!,
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<VibeFolderCardItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <VibeFolderCardItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = VibeFolderCardItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, VibeFolderCardItem> mapFromJson(dynamic json) {
    final map = <String, VibeFolderCardItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = VibeFolderCardItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of VibeFolderCardItem-objects as value to a dart map
  static Map<String, List<VibeFolderCardItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<VibeFolderCardItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = VibeFolderCardItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'vibeFolderId',
    'iventId',
    'iventName',
    'vibeId',
    'memberCount',
    'memberFirstnames',
  };
}

