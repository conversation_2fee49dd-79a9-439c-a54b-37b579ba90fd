/// Authentication feature dimension constants
/// 
/// Contains spacing, sizing, and layout constants specific to the
/// authentication feature for consistent UI dimensions.
class AuthDimensions {
  AuthDimensions._(); // Private constructor to prevent instantiation

  // ==========================================================================
  // PHONE INPUT DIMENSIONS
  // ==========================================================================
  
  /// Width of the phone number input field
  static const double phoneInputWidth = 198.0;
  
  /// Height of the phone number input field
  static const double phoneInputHeight = 50.0;

  // ==========================================================================
  // VALIDATION CODE DIMENSIONS
  // ==========================================================================
  
  /// Size of each validation code input box
  static const double validationCodeBoxSize = 50.0;
  
  /// Spacing between validation code input boxes
  static const double validationCodeSpacing = 8.0;

  // ==========================================================================
  // ONBOARDING DIMENSIONS
  // ==========================================================================
  
  /// Page indicator circle radius
  static const double pageIndicatorRadius = 3.0;
  
  /// Spacing between page indicator circles
  static const double pageIndicatorSpacing = 5.0;

  // ==========================================================================
  // HOBBY SELECTION DIMENSIONS
  // ==========================================================================
  
  /// Height of selected hobby tags container
  static const double selectedHobbyTagsHeight = 40.0;
  
  /// Spacing between hobby tags
  static const double hobbyTagSpacing = 8.0;
  
  /// Spacing between hobby tag rows
  static const double hobbyTagRowSpacing = 8.0;
  
  /// Spacing between hobby categories
  static const double hobbyCategorySpacing = 40.0;

  // ==========================================================================
  // FORM SPACING
  // ==========================================================================
  
  /// Standard vertical spacing between form elements
  static const double formElementSpacing = 20.0;
  
  /// Large vertical spacing for major sections
  static const double sectionSpacing = 40.0;
  
  /// Small vertical spacing for related elements
  static const double relatedElementSpacing = 10.0;

  // ==========================================================================
  // BUTTON DIMENSIONS
  // ==========================================================================
  
  /// Standard button height for auth forms
  static const double standardButtonHeight = 50.0;
  
  /// Floating action button bottom margin
  static const double floatingButtonBottomMargin = 20.0;
}
