# openapi.api.HomeApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**feed**](HomeApi.md#homecontrollerfeed) | **GET** /feed | Feedi listeler
[**map**](HomeApi.md#homecontrollermap) | **GET** /map | Mapi listeler
[**searchAccount**](HomeApi.md#homecontrollersearchaccount) | **GET** /searchAccount | Uygulamada arama yapar
[**searchIvent**](HomeApi.md#homecontrollersearchivent) | **GET** /searchIvent | Uygulamada arama yapar


# **feed**
> FeedReturn feed(dateType, categories, locationCoeff, q, limit, page, lngEnd, lngStart, latEnd, latStart, endDate, startDate)

Feedi listeler

Şu anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = HomeApi();
final dateType = ; // FeedDateEnum | Date range to filter the feed
final categories = categories_example; // String | 
final locationCoeff = 56; // int | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 
final lngEnd = -73.9712; // double | 
final lngStart = -73.9712; // double | 
final latEnd = 40.7766; // double | 
final latStart = 40.7766; // double | 
final endDate = 2021-12-31; // Object | End date for the feed
final startDate = 2021-12-31; // Object | Start date for the feed

try {
    final result = api_instance.feed(dateType, categories, locationCoeff, q, limit, page, lngEnd, lngStart, latEnd, latStart, endDate, startDate);
    print(result);
} catch (e) {
    print('Exception when calling HomeApi->feed: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **dateType** | [**FeedDateEnum**](.md)| Date range to filter the feed | 
 **categories** | **String**|  | 
 **locationCoeff** | **int**|  | [optional] 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 
 **lngEnd** | [**double**](.md)|  | [optional] 
 **lngStart** | [**double**](.md)|  | [optional] 
 **latEnd** | [**double**](.md)|  | [optional] 
 **latStart** | [**double**](.md)|  | [optional] 
 **endDate** | [**Object**](.md)| End date for the feed | [optional] 
 **startDate** | [**Object**](.md)| Start date for the feed | [optional] 

### Return type

[**FeedReturn**](FeedReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **map**
> MapReturn map(startDate, endDate, latStart, latEnd, lngStart, lngEnd, limit)

Mapi listeler

Şu anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = HomeApi();
final startDate = 2021-12-31; // String | Start date for the ivents on map
final endDate = 2021-12-31; // String | End date for the ivents on map
final latStart = 40.7766; // double | 
final latEnd = 40.7766; // double | 
final lngStart = -73.9712; // double | 
final lngEnd = -73.9712; // double | 
final limit = 56; // int | 

try {
    final result = api_instance.map(startDate, endDate, latStart, latEnd, lngStart, lngEnd, limit);
    print(result);
} catch (e) {
    print('Exception when calling HomeApi->map: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **startDate** | **String**| Start date for the ivents on map | 
 **endDate** | **String**| End date for the ivents on map | 
 **latStart** | [**double**](.md)|  | 
 **latEnd** | [**double**](.md)|  | 
 **lngStart** | [**double**](.md)|  | 
 **lngEnd** | [**double**](.md)|  | 
 **limit** | **int**|  | [optional] 

### Return type

[**MapReturn**](MapReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchAccount**
> SearchAccountReturn searchAccount(q, limit, page)

Uygulamada arama yapar

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = HomeApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchAccount(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling HomeApi->searchAccount: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchAccountReturn**](SearchAccountReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchIvent**
> SearchIventReturn searchIvent(q, limit, page)

Uygulamada arama yapar

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = HomeApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchIvent(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling HomeApi->searchIvent: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchIventReturn**](SearchIventReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


