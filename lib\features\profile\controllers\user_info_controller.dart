import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing user information and social interactions
///
/// Handles user profile data, following/unfollowing functionality,
/// and friend relationship management. Manages the social aspects
/// of user profiles.
class UserInfoController extends BaseControllerWithSharedState<ProfileStateManager> {
  // Reactive state
  final _userPageInfo = Rxn<GetUserByUserIdReturn>();
  final _isFollowing = false.obs;
  final _relationshipStatus = Rxn<UserRelationshipStatusEnum>();

  // Constructor
  UserInfoController(AuthService authService, ProfileStateManager state) : super(authService, state);

  // Getters
  GetUserByUserIdReturn? get userPageInfo => _userPageInfo.value;
  bool get isFollowing => _isFollowing.value;
  UserRelationshipStatusEnum? get relationshipStatus => _relationshipStatus.value;

  // Setters
  set userPageInfo(GetUserByUserIdReturn? value) => _userPageInfo.value = value;
  set isFollowing(bool value) => _isFollowing.value = value;
  set relationshipStatus(UserRelationshipStatusEnum? value) => _relationshipStatus.value = value;

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();
    await _loadUserInfo();
  }

  // Public methods

  /// Toggles following status for the user
  Future<void> toggleFollowing() async {
    if (isFollowing) {
      isFollowing = false;
      await authService.usersApi.unfollowByUserId(state.userId);
    } else {
      isFollowing = true;
      await authService.usersApi.followByUserId(state.userId);
    }
  }

  /// Toggles friendship status for the user
  Future<void> toggleFriendship() async {
    if (relationshipStatus == UserRelationshipStatusEnum.accepted) {
      relationshipStatus = null;
      await authService.userRelationshipsApi.removeFriendByUserId(state.userId);
    } else {
      relationshipStatus = UserRelationshipStatusEnum.accepted;
      await authService.userRelationshipsApi.inviteFriendByUserId(state.userId);
    }
  }

  // Private methods

  /// Loads user information and updates state
  Future<void> _loadUserInfo() async {
    userPageInfo = await authService.usersApi.getByUserId(state.userId);
    if (userPageInfo != null) {
      isFollowing = userPageInfo!.isFollowing;
      relationshipStatus = userPageInfo!.relationshipStatus;
      state.userRole = userPageInfo!.userRole;
    }
  }
}
