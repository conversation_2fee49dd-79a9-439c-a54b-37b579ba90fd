# openapi.model.GetBannerByIventIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ivents** | [**List<IventCardItem>**](IventCardItem.md) | Array of ivent card items for banner display | [default to const []]
**iventCount** | **int** | Total number of ivents in the banner | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



