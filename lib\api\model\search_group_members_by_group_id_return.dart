//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchGroupMembersByGroupIdReturn {
  /// Returns a new [SearchGroupMembersByGroupIdReturn] instance.
  SearchGroupMembersByGroupIdReturn({
    this.members = const [],
    required this.memberCount,
  });

  /// List of group members with their roles and friendship status
  List<UserListItemWithGroupRole> members;

  /// Total number of group members
  ///
  /// Minimum value: 0
  int memberCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchGroupMembersByGroupIdReturn &&
    _deepEquality.equals(other.members, members) &&
    other.memberCount == memberCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (members.hashCode) +
    (memberCount.hashCode);

  @override
  String toString() => 'SearchGroupMembersByGroupIdReturn[members=$members, memberCount=$memberCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'members'] = this.members;
      json[r'memberCount'] = this.memberCount;
    return json;
  }

  /// Returns a new [SearchGroupMembersByGroupIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchGroupMembersByGroupIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchGroupMembersByGroupIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchGroupMembersByGroupIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchGroupMembersByGroupIdReturn(
        members: UserListItemWithGroupRole.listFromJson(json[r'members']),
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
      );
    }
    return null;
  }

  static List<SearchGroupMembersByGroupIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchGroupMembersByGroupIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchGroupMembersByGroupIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchGroupMembersByGroupIdReturn> mapFromJson(dynamic json) {
    final map = <String, SearchGroupMembersByGroupIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchGroupMembersByGroupIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchGroupMembersByGroupIdReturn-objects as value to a dart map
  static Map<String, List<SearchGroupMembersByGroupIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchGroupMembersByGroupIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchGroupMembersByGroupIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'members',
    'memberCount',
  };
}

