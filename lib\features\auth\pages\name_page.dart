import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_controller.dart';
import 'package:ivent_app/features/auth/widgets/common/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/form/name_input_widget.dart';

class NamePage extends StatefulWidget {
  const NamePage({super.key});

  @override
  State<NamePage> createState() => _NamePageState();
}

class _NamePageState extends State<NamePage> {
  late final AuthController _controller;
  late final TextEditingController _textFieldController;

  bool _canContinueToNextPage = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          _buildInfoText(),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          _buildNameInput(),
          const Spacer(),
          _buildContinueButton(),
        ],
      ),
    );
  }

  Widget _buildInfoText() {
    return const AuthInfoTextWidget(
      text: AuthStrings.tanisalim,
    );
  }

  Widget _buildNameInput() {
    return NameInputWidget(
      controller: _textFieldController,
      onValidationChanged: _handleValidationChanged,
      onChanged: _handleNameChanged,
    );
  }

  Widget _buildContinueButton() {
    return IaFloatingActionButton(
      isEnabled: _canContinueToNextPage,
      text: AuthStrings.devamEt,
      onPressed: _handleContinuePressed,
    );
  }

  void _handleValidationChanged(bool isValid) {
    setState(() {
      _canContinueToNextPage = isValid;
    });
  }

  void _handleNameChanged(String name) {
    _controller.registrationController.fullname = name;
  }

  void _handleContinuePressed() {
    if (_canContinueToNextPage && _controller.canProceedInAuthFlow()) {
      _controller.goToRegistrationHobbiesView();
    }
  }

  void _initializeControllers() {
    _controller = Get.find<AuthController>();
    _textFieldController = TextEditingController();

    final existingName = _controller.registrationController.fullname;
    if (existingName.isNotEmpty) {
      _textFieldController.text = existingName;
      _canContinueToNextPage = _controller.registrationController.isFullnameValid;
    }
  }

  void _disposeControllers() {
    _textFieldController.dispose();
  }
}
