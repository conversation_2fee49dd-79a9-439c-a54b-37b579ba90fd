import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/searchable/location_controller.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';

class LocationPage extends StatefulWidget {
  const LocationPage({super.key});

  @override
  State<LocationPage> createState() => _LocationPageState();
}

class _LocationPageState extends State<LocationPage> {
  final HomeController _controller = Get.find();

  LocationController get _locationController => _controller.locationController;
  MapboxController get _mapboxController => _locationController.mapboxController;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Lokasyon Seç',
      textEditingController: _locationController.textEditingController,
      body: Column(
        children: [
          _buildUseMyLocationTile(),
          const SizedBox(height: AppDimensions.padding20),
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildUseMyLocationTile() {
    return Obx(() {
      final userLocation = _mapboxController.userLocation;
      return IaListTile.withSvgIcon(
        iconPath: AppAssets.navigation,
        iconColor: AppColors.white,
        avatarColor: AppColors.primary,
        title: 'Mevcut Konumumu Kullan',
        subtitle: userLocation?.address,
        onTap: _controller.locationController.useCurrentLocation,
      );
    });
  }

  Widget _buildSearchResults() {
    return Obx(() {
      final suggestions = _locationController.placeSuggestionResults;
      return IaSearchPlaceholder(
        entityType: 'Konum',
        iconPath: AppAssets.mapPin,
        isSearching: _locationController.isSearching,
        isQueryEmpty: _locationController.isQueryEmpty,
        isResultsEmpty: _locationController.isResultsEmpty,
        isDefaultStateEmpty: true,
        builder: (context) {
          return ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: suggestions.length,
            itemBuilder: (context, index) {
              final suggestion = suggestions[index];
              return IaListTile(
                title: suggestion.name,
                subtitle: suggestion.fullAddress ?? suggestion.placeFormatted,
                onTap: () {
                  _controller.locationController.selectPlace(suggestion);
                  Get.back();
                },
              );
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          );
        },
      );
    });
  }
}
