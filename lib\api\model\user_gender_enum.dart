//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Gender of the user
class UserGenderEnum {
  /// Instantiate a new enum with the provided [value].
  const UserGenderEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const male = UserGenderEnum._(r'male');
  static const female = UserGenderEnum._(r'female');
  static const nonBinary = UserGenderEnum._(r'non-binary');
  static const other = UserGenderEnum._(r'other');
  static const preferNotToSay = UserGenderEnum._(r'prefer_not_to_say');

  /// List of all possible values in this [enum][UserGenderEnum].
  static const values = <UserGenderEnum>[
    male,
    female,
    nonBinary,
    other,
    preferNotToSay,
  ];

  static UserGenderEnum? fromJson(dynamic value) => UserGenderEnumTypeTransformer().decode(value);

  static List<UserGenderEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UserGenderEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UserGenderEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [UserGenderEnum] to String,
/// and [decode] dynamic data back to [UserGenderEnum].
class UserGenderEnumTypeTransformer {
  factory UserGenderEnumTypeTransformer() => _instance ??= const UserGenderEnumTypeTransformer._();

  const UserGenderEnumTypeTransformer._();

  String encode(UserGenderEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a UserGenderEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  UserGenderEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'male': return UserGenderEnum.male;
        case r'female': return UserGenderEnum.female;
        case r'non-binary': return UserGenderEnum.nonBinary;
        case r'other': return UserGenderEnum.other;
        case r'prefer_not_to_say': return UserGenderEnum.preferNotToSay;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [UserGenderEnumTypeTransformer] instance.
  static UserGenderEnumTypeTransformer? _instance;
}

