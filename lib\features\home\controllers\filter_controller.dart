import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class FilterController extends BaseHomeController {
  FilterController(AuthService authService, HomeStateManager state) : super(authService, state);

  void toggleHobbySelection(String hobbyId) {
    if (state.selectedHobbyIds.contains(hobbyId)) {
      state.selectedHobbyIds.remove(hobbyId);
    } else {
      state.selectedHobbyIds.add(hobbyId);
    }
  }

  void clearFilters() => state.selectedHobbyIds = [];
}
