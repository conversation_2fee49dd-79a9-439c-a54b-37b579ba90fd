import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/base_ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IventCreateSubmissionController extends BaseIventCreateController {
  IventCreateSubmissionController(AuthService authService, IventCreateStateManager state) : super(authService, state);

  final PanelController _panelController = PanelController();

  var _selectedPanelIndex = 0.obs;
  var _isPanelVisible = false.obs;

  int get selectedPanelIndex => _selectedPanelIndex.value;
  bool get isPanelVisible => _isPanelVisible.value;
  PanelController get panelController => _panelController;

  set selectedPanelIndex(int value) => _selectedPanelIndex.value = value;
  set isPanelVisible(bool value) => _isPanelVisible.value = value;

  @override
  void initController() async {
    super.initController();
    print('IventCreateSubmissionController has been initialized');
  }

  void openPanel(int panelIndex) {
    isPanelVisible = true;
    selectedPanelIndex = panelIndex;
    panelController.open();
  }

  void closePanel() {
    isPanelVisible = false;
    panelController.close();
  }
}
