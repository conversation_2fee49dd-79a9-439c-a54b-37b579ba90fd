import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/constants/enums/account_enum.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/collabs_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';

class IventDetailCollabrators extends StatelessWidget {
  final String iventId;

  const IventDetailCollabrators({super.key, required this.iventId});

  @override
  Widget build(BuildContext context) {
    final IventDetailsController controller = Get.find(tag: iventId);
    final CollabsController collabsController = controller.collabsController;

    return IaScaffold.search(
      title: 'Paydaşlar',
      textEditingController: collabsController.textEditingController,
      body: Obx(() {
        final collabratorsResult = collabsController.collabsResult;
        return IaSearchPlaceholder(
          entityType: 'Paydaş',
          isSearching: collabsController.isSearching,
          isQueryEmpty: collabsController.isQueryEmpty,
          isResultsEmpty: collabsController.isResultsEmpty,
          isDefaultStateEmpty: false,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: collabratorsResult!.collabCount,
              itemBuilder: (context, index) {
                final collabrator = collabratorsResult.collabs[index];
                return IaListTile.withImageUrl(
                  avatarUrl: collabrator.collabImageUrl,
                  title: collabrator.collabName,
                  trailing: _buildTrailing(collabrator, controller),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }

  Widget _buildTrailing(CollabratorListItem collabrator, IventDetailsController controller) {
    if (collabrator.collabType == AccountEnum.USER) {
      return SharedButtons.addFriendListTile(
        onTap: () {},
        relationshipStatus: collabrator.relationshipStatus,
      );
    } else {
      return Text('Etkinlik Sahibi', style: AppTextStyles.size12RegularTextSecondary);
    }
  }
}
