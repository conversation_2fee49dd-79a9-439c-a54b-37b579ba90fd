//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class InviteFriendsByIventIdDto {
  /// Returns a new [InviteFriendsByIventIdDto] instance.
  InviteFriendsByIventIdDto({
    this.groupIds = const [],
    this.userIds = const [],
  });

  /// Array of group UUIDs to invite to the ivent
  List<String> groupIds;

  /// Array of user UUIDs to invite to the ivent
  List<String> userIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is InviteFriendsByIventIdDto &&
    _deepEquality.equals(other.groupIds, groupIds) &&
    _deepEquality.equals(other.userIds, userIds);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (groupIds.hashCode) +
    (userIds.hashCode);

  @override
  String toString() => 'InviteFriendsByIventIdDto[groupIds=$groupIds, userIds=$userIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'groupIds'] = this.groupIds;
      json[r'userIds'] = this.userIds;
    return json;
  }

  /// Returns a new [InviteFriendsByIventIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static InviteFriendsByIventIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "InviteFriendsByIventIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "InviteFriendsByIventIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return InviteFriendsByIventIdDto(
        groupIds: json[r'groupIds'] is Iterable
            ? (json[r'groupIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        userIds: json[r'userIds'] is Iterable
            ? (json[r'userIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<InviteFriendsByIventIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <InviteFriendsByIventIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = InviteFriendsByIventIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, InviteFriendsByIventIdDto> mapFromJson(dynamic json) {
    final map = <String, InviteFriendsByIventIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = InviteFriendsByIventIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of InviteFriendsByIventIdDto-objects as value to a dart map
  static Map<String, List<InviteFriendsByIventIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<InviteFriendsByIventIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = InviteFriendsByIventIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'groupIds',
    'userIds',
  };
}

