# openapi.api.MapboxApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**searchBoxCategory**](MapboxApi.md#mapboxcontrollersearchboxcategory) | **GET** /mapbox/category/{canonicalCategoryId} | Search places by category
[**searchBoxCategoryList**](MapboxApi.md#mapboxcontrollersearchboxcategorylist) | **GET** /mapbox/categories | List available categories
[**searchBoxForward**](MapboxApi.md#mapboxcontrollersearchboxforward) | **GET** /mapbox/forward | Forward geocoding search
[**searchBoxRetrieve**](MapboxApi.md#mapboxcontrollersearchboxretrieve) | **GET** /mapbox/retrieve/{id} | Retrieve detailed information about a place
[**searchBoxReverse**](MapboxApi.md#mapboxcontrollersearchboxreverse) | **GET** /mapbox/reverse | Reverse geocoding search
[**searchBoxSuggest**](MapboxApi.md#mapboxcontrollersearchboxsuggest) | **GET** /mapbox/suggest | Search for place suggestions


# **searchBoxCategory**
> SearchBoxCategoryReturn searchBoxCategory(canonicalCategoryId, language, proximity, bbox, country, types, poiCategoryExclusions, sarType, route, routeGeometry, timeDeviation)

Search places by category

Search for places within a specific category

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MapboxApi();
final canonicalCategoryId = canonicalCategoryId_example; // String | 
final language = en; // String | Language code (ISO 639-1)
final proximity = 28.9784,41.0082; // String | Proximity bias as longitude,latitude
final bbox = 28.9,40.9,29.1,41.1; // String | Bounding box as minX,minY,maxX,maxY
final country = TR; // String | Country code filter (ISO 3166-1 alpha-2)
final types = poi; // String | Feature types to include
final poiCategoryExclusions = gas_station; // String | POI categories to exclude
final sarType = isochrone; // String | Search along route type
final route = route_example; // String | Route geometry for search along route
final routeGeometry = polyline; // String | Route geometry format
final timeDeviation = 15; // int | Time deviation in minutes

try {
    final result = api_instance.searchBoxCategory(canonicalCategoryId, language, proximity, bbox, country, types, poiCategoryExclusions, sarType, route, routeGeometry, timeDeviation);
    print(result);
} catch (e) {
    print('Exception when calling MapboxApi->searchBoxCategory: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **canonicalCategoryId** | **String**|  | 
 **language** | **String**| Language code (ISO 639-1) | [optional] 
 **proximity** | **String**| Proximity bias as longitude,latitude | [optional] 
 **bbox** | **String**| Bounding box as minX,minY,maxX,maxY | [optional] 
 **country** | **String**| Country code filter (ISO 3166-1 alpha-2) | [optional] 
 **types** | **String**| Feature types to include | [optional] 
 **poiCategoryExclusions** | **String**| POI categories to exclude | [optional] 
 **sarType** | **String**| Search along route type | [optional] 
 **route** | **String**| Route geometry for search along route | [optional] 
 **routeGeometry** | **String**| Route geometry format | [optional] 
 **timeDeviation** | **int**| Time deviation in minutes | [optional] 

### Return type

[**SearchBoxCategoryReturn**](SearchBoxCategoryReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchBoxCategoryList**
> SearchBoxCategoryListReturn searchBoxCategoryList(language)

List available categories

Returns a list of all available POI categories

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MapboxApi();
final language = en; // String | Language code (ISO 639-1)

try {
    final result = api_instance.searchBoxCategoryList(language);
    print(result);
} catch (e) {
    print('Exception when calling MapboxApi->searchBoxCategoryList: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **language** | **String**| Language code (ISO 639-1) | [optional] 

### Return type

[**SearchBoxCategoryListReturn**](SearchBoxCategoryListReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchBoxForward**
> SearchBoxForwardReturn searchBoxForward(q, language, limit, proximity, bbox, country, types, poiCategory, poiCategoryExclusions, autoComplete, etaType, navigationProfile, origin)

Forward geocoding search

Search for places using forward geocoding

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MapboxApi();
final q = coffee shop; // String | Search query string
final language = en; // String | Language code (ISO 639-1)
final limit = 10; // int | Maximum number of results
final proximity = 28.9784,41.0082; // String | Proximity bias as longitude,latitude
final bbox = 28.9,40.9,29.1,41.1; // String | Bounding box as minX,minY,maxX,maxY
final country = TR; // String | Country code filter (ISO 3166-1 alpha-2)
final types = poi; // String | Feature types to include
final poiCategory = food_and_drink; // String | POI category filter
final poiCategoryExclusions = gas_station; // String | POI categories to exclude
final autoComplete = true; // String | Auto-complete setting
final etaType = navigation; // String | ETA calculation type
final navigationProfile = driving; // String | Navigation profile for ETA
final origin = 28.9784,41.0082; // String | Origin point for ETA as longitude,latitude

try {
    final result = api_instance.searchBoxForward(q, language, limit, proximity, bbox, country, types, poiCategory, poiCategoryExclusions, autoComplete, etaType, navigationProfile, origin);
    print(result);
} catch (e) {
    print('Exception when calling MapboxApi->searchBoxForward: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**| Search query string | 
 **language** | **String**| Language code (ISO 639-1) | [optional] 
 **limit** | **int**| Maximum number of results | [optional] 
 **proximity** | **String**| Proximity bias as longitude,latitude | [optional] 
 **bbox** | **String**| Bounding box as minX,minY,maxX,maxY | [optional] 
 **country** | **String**| Country code filter (ISO 3166-1 alpha-2) | [optional] 
 **types** | **String**| Feature types to include | [optional] 
 **poiCategory** | **String**| POI category filter | [optional] 
 **poiCategoryExclusions** | **String**| POI categories to exclude | [optional] 
 **autoComplete** | **String**| Auto-complete setting | [optional] 
 **etaType** | **String**| ETA calculation type | [optional] 
 **navigationProfile** | **String**| Navigation profile for ETA | [optional] 
 **origin** | **String**| Origin point for ETA as longitude,latitude | [optional] 

### Return type

[**SearchBoxForwardReturn**](SearchBoxForwardReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchBoxRetrieve**
> SearchBoxRetrieveReturn searchBoxRetrieve(id, sessionToken, language, etaType, navigationProfile, origin)

Retrieve detailed information about a place

Returns detailed information about a specific place using its Mapbox ID

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MapboxApi();
final id = id_example; // String | 
final sessionToken = session_123; // String | Session token for grouping requests
final language = en; // String | Language code (ISO 639-1)
final etaType = navigation; // String | ETA calculation type
final navigationProfile = driving; // String | Navigation profile for ETA
final origin = 28.9784,41.0082; // String | Origin point for ETA as longitude,latitude

try {
    final result = api_instance.searchBoxRetrieve(id, sessionToken, language, etaType, navigationProfile, origin);
    print(result);
} catch (e) {
    print('Exception when calling MapboxApi->searchBoxRetrieve: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **sessionToken** | **String**| Session token for grouping requests | 
 **language** | **String**| Language code (ISO 639-1) | [optional] 
 **etaType** | **String**| ETA calculation type | [optional] 
 **navigationProfile** | **String**| Navigation profile for ETA | [optional] 
 **origin** | **String**| Origin point for ETA as longitude,latitude | [optional] 

### Return type

[**SearchBoxRetrieveReturn**](SearchBoxRetrieveReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchBoxReverse**
> SearchBoxReverseReturn searchBoxReverse(longitude, latitude, language, limit, country, types)

Reverse geocoding search

Find places near a specific coordinate

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MapboxApi();
final longitude = 28.9784; // double | Longitude coordinate
final latitude = 41.0082; // double | Latitude coordinate
final language = en; // String | Language code (ISO 639-1)
final limit = 10; // int | Maximum number of results
final country = TR; // String | Country code filter (ISO 3166-1 alpha-2)
final types = poi; // String | Feature types to include

try {
    final result = api_instance.searchBoxReverse(longitude, latitude, language, limit, country, types);
    print(result);
} catch (e) {
    print('Exception when calling MapboxApi->searchBoxReverse: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **longitude** | [**double**](.md)| Longitude coordinate | 
 **latitude** | [**double**](.md)| Latitude coordinate | 
 **language** | **String**| Language code (ISO 639-1) | [optional] 
 **limit** | **int**| Maximum number of results | [optional] 
 **country** | **String**| Country code filter (ISO 3166-1 alpha-2) | [optional] 
 **types** | **String**| Feature types to include | [optional] 

### Return type

[**SearchBoxReverseReturn**](SearchBoxReverseReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchBoxSuggest**
> SearchBoxSuggestReturn searchBoxSuggest(q, sessionToken, language, limit, proximity, bbox, country, types, poiCategory, poiCategoryExclusions, etaType, navigationProfile, origin)

Search for place suggestions

Returns a list of place suggestions based on the search query

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MapboxApi();
final q = coffee shop; // String | Search query string
final sessionToken = session_123; // String | Session token for grouping requests
final language = en; // String | Language code (ISO 639-1)
final limit = 10; // int | Maximum number of results
final proximity = 28.9784,41.0082; // String | Proximity bias as longitude,latitude
final bbox = 28.9,40.9,29.1,41.1; // String | Bounding box as minX,minY,maxX,maxY
final country = TR; // String | Country code filter (ISO 3166-1 alpha-2)
final types = poi; // String | Feature types to include
final poiCategory = food_and_drink; // String | POI category filter
final poiCategoryExclusions = gas_station; // String | POI categories to exclude
final etaType = navigation; // String | ETA calculation type
final navigationProfile = driving; // String | Navigation profile for ETA
final origin = 28.9784,41.0082; // String | Origin point for ETA as longitude,latitude

try {
    final result = api_instance.searchBoxSuggest(q, sessionToken, language, limit, proximity, bbox, country, types, poiCategory, poiCategoryExclusions, etaType, navigationProfile, origin);
    print(result);
} catch (e) {
    print('Exception when calling MapboxApi->searchBoxSuggest: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**| Search query string | 
 **sessionToken** | **String**| Session token for grouping requests | 
 **language** | **String**| Language code (ISO 639-1) | [optional] 
 **limit** | **int**| Maximum number of results | [optional] 
 **proximity** | **String**| Proximity bias as longitude,latitude | [optional] 
 **bbox** | **String**| Bounding box as minX,minY,maxX,maxY | [optional] 
 **country** | **String**| Country code filter (ISO 3166-1 alpha-2) | [optional] 
 **types** | **String**| Feature types to include | [optional] 
 **poiCategory** | **String**| POI category filter | [optional] 
 **poiCategoryExclusions** | **String**| POI categories to exclude | [optional] 
 **etaType** | **String**| ETA calculation type | [optional] 
 **navigationProfile** | **String**| Navigation profile for ETA | [optional] 
 **origin** | **String**| Origin point for ETA as longitude,latitude | [optional] 

### Return type

[**SearchBoxSuggestReturn**](SearchBoxSuggestReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


