//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class VibeItem {
  /// Returns a new [VibeItem] instance.
  VibeItem({
    required this.vibeId,
    required this.vibeFolderId,
    required this.mediaUrl,
    required this.mediaFormat,
    this.thumbnailUrl,
    required this.caption,
    required this.creatorId,
    required this.creatorType,
    required this.creatorUsername,
    this.creatorAvatarUrl,
    required this.iventId,
    required this.iventName,
    this.dates = const [],
    required this.memberCount,
    this.memberFirstnames = const [],
    required this.likeCount,
    required this.commentCount,
    this.nextVibeId,
    this.previousVibeId,
    required this.vibeIndex,
    required this.vibeCount,
    this.createdAt,
  });

  /// UUID of the vibe
  String vibeId;

  /// UUID of the vibe folder
  String vibeFolderId;

  /// URL to the vibe media
  String mediaUrl;

  MediaFormatEnum mediaFormat;

  /// URL to the vibe thumbnail
  String? thumbnailUrl;

  /// Caption of the vibe
  String caption;

  /// UUID of the creator
  String creatorId;

  AccountTypeEnum creatorType;

  /// Username of the creator
  String creatorUsername;

  /// URL to the creator image
  String? creatorAvatarUrl;

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// List of dates for the ivent, in ISO 8601 date-time format
  List<String> dates;

  /// Number of members in the ivent
  ///
  /// Minimum value: 0
  int memberCount;

  /// List of member's first names in the ivent
  List<String> memberFirstnames;

  /// Number of likes on the vibe
  ///
  /// Minimum value: 0
  int likeCount;

  /// Number of comments on the vibe
  ///
  /// Minimum value: 0
  int commentCount;

  /// UUID of the next vibe in the vibe folder
  String? nextVibeId;

  /// UUID of the previous vibe in the vibe folder
  String? previousVibeId;

  /// Index of the vibe in the vibe folder
  ///
  /// Minimum value: 0
  int vibeIndex;

  /// Total number of vibes in the vibe folder
  ///
  /// Minimum value: 0
  int vibeCount;

  /// Date of creation of the vibe, in ISO 8601 date-time format
  String? createdAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is VibeItem &&
    other.vibeId == vibeId &&
    other.vibeFolderId == vibeFolderId &&
    other.mediaUrl == mediaUrl &&
    other.mediaFormat == mediaFormat &&
    other.thumbnailUrl == thumbnailUrl &&
    other.caption == caption &&
    other.creatorId == creatorId &&
    other.creatorType == creatorType &&
    other.creatorUsername == creatorUsername &&
    other.creatorAvatarUrl == creatorAvatarUrl &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    _deepEquality.equals(other.dates, dates) &&
    other.memberCount == memberCount &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames) &&
    other.likeCount == likeCount &&
    other.commentCount == commentCount &&
    other.nextVibeId == nextVibeId &&
    other.previousVibeId == previousVibeId &&
    other.vibeIndex == vibeIndex &&
    other.vibeCount == vibeCount &&
    other.createdAt == createdAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (vibeId.hashCode) +
    (vibeFolderId.hashCode) +
    (mediaUrl.hashCode) +
    (mediaFormat.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (caption.hashCode) +
    (creatorId.hashCode) +
    (creatorType.hashCode) +
    (creatorUsername.hashCode) +
    (creatorAvatarUrl == null ? 0 : creatorAvatarUrl!.hashCode) +
    (iventId.hashCode) +
    (iventName.hashCode) +
    (dates.hashCode) +
    (memberCount.hashCode) +
    (memberFirstnames.hashCode) +
    (likeCount.hashCode) +
    (commentCount.hashCode) +
    (nextVibeId == null ? 0 : nextVibeId!.hashCode) +
    (previousVibeId == null ? 0 : previousVibeId!.hashCode) +
    (vibeIndex.hashCode) +
    (vibeCount.hashCode) +
    (createdAt == null ? 0 : createdAt!.hashCode);

  @override
  String toString() => 'VibeItem[vibeId=$vibeId, vibeFolderId=$vibeFolderId, mediaUrl=$mediaUrl, mediaFormat=$mediaFormat, thumbnailUrl=$thumbnailUrl, caption=$caption, creatorId=$creatorId, creatorType=$creatorType, creatorUsername=$creatorUsername, creatorAvatarUrl=$creatorAvatarUrl, iventId=$iventId, iventName=$iventName, dates=$dates, memberCount=$memberCount, memberFirstnames=$memberFirstnames, likeCount=$likeCount, commentCount=$commentCount, nextVibeId=$nextVibeId, previousVibeId=$previousVibeId, vibeIndex=$vibeIndex, vibeCount=$vibeCount, createdAt=$createdAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'vibeId'] = this.vibeId;
      json[r'vibeFolderId'] = this.vibeFolderId;
      json[r'mediaUrl'] = this.mediaUrl;
      json[r'mediaFormat'] = this.mediaFormat;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'caption'] = this.caption;
      json[r'creatorId'] = this.creatorId;
      json[r'creatorType'] = this.creatorType;
      json[r'creatorUsername'] = this.creatorUsername;
    if (this.creatorAvatarUrl != null) {
      json[r'creatorAvatarUrl'] = this.creatorAvatarUrl;
    } else {
      json[r'creatorAvatarUrl'] = null;
    }
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
      json[r'dates'] = this.dates;
      json[r'memberCount'] = this.memberCount;
      json[r'memberFirstnames'] = this.memberFirstnames;
      json[r'likeCount'] = this.likeCount;
      json[r'commentCount'] = this.commentCount;
    if (this.nextVibeId != null) {
      json[r'nextVibeId'] = this.nextVibeId;
    } else {
      json[r'nextVibeId'] = null;
    }
    if (this.previousVibeId != null) {
      json[r'previousVibeId'] = this.previousVibeId;
    } else {
      json[r'previousVibeId'] = null;
    }
      json[r'vibeIndex'] = this.vibeIndex;
      json[r'vibeCount'] = this.vibeCount;
    if (this.createdAt != null) {
      json[r'createdAt'] = this.createdAt;
    } else {
      json[r'createdAt'] = null;
    }
    return json;
  }

  /// Returns a new [VibeItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static VibeItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "VibeItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "VibeItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return VibeItem(
        vibeId: mapValueOfType<String>(json, r'vibeId')!,
        vibeFolderId: mapValueOfType<String>(json, r'vibeFolderId')!,
        mediaUrl: mapValueOfType<String>(json, r'mediaUrl')!,
        mediaFormat: MediaFormatEnum.fromJson(json[r'mediaFormat'])!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        caption: mapValueOfType<String>(json, r'caption')!,
        creatorId: mapValueOfType<String>(json, r'creatorId')!,
        creatorType: AccountTypeEnum.fromJson(json[r'creatorType'])!,
        creatorUsername: mapValueOfType<String>(json, r'creatorUsername')!,
        creatorAvatarUrl: mapValueOfType<String>(json, r'creatorAvatarUrl'),
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        dates: json[r'dates'] is Iterable
            ? (json[r'dates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        likeCount: mapValueOfType<int>(json, r'likeCount')!,
        commentCount: mapValueOfType<int>(json, r'commentCount')!,
        nextVibeId: mapValueOfType<String>(json, r'nextVibeId'),
        previousVibeId: mapValueOfType<String>(json, r'previousVibeId'),
        vibeIndex: mapValueOfType<int>(json, r'vibeIndex')!,
        vibeCount: mapValueOfType<int>(json, r'vibeCount')!,
        createdAt: mapValueOfType<String>(json, r'createdAt'),
      );
    }
    return null;
  }

  static List<VibeItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <VibeItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = VibeItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, VibeItem> mapFromJson(dynamic json) {
    final map = <String, VibeItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = VibeItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of VibeItem-objects as value to a dart map
  static Map<String, List<VibeItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<VibeItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = VibeItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'vibeId',
    'vibeFolderId',
    'mediaUrl',
    'mediaFormat',
    'caption',
    'creatorId',
    'creatorType',
    'creatorUsername',
    'iventId',
    'iventName',
    'dates',
    'memberCount',
    'memberFirstnames',
    'likeCount',
    'commentCount',
    'vibeIndex',
    'vibeCount',
  };
}

