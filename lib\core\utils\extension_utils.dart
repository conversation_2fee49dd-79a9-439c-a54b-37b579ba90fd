extension ConvertMapExtension on Map<dynamic, dynamic>? {
  Map<String, dynamic>? get asStringDynamicMap {
    if (this == null) return null;

    return {
      for (var entry in this!.entries)
        if (entry.key != null) entry.key!: _convertValue(entry.value)
    };
  }

  dynamic _convertValue(dynamic value) {
    if (value is Map) {
      return ConvertMapExtension(value).asStringDynamicMap;
    } else if (value is List) {
      return value.map((e) => _convertValue(e)).toList();
    }
    return value;
  }
}

extension EnumExtension<T extends Enum> on T {
  String toShortString() => name.toLowerCase();
}

extension EnumByNameExtension<T extends Enum> on Iterable<T> {
  T byString(String name) {
    return byName(name.toUpperCase());
  }

  T byStringOrNone(String? name) {
    if (name == null) return byName('NONE');
    try {
      return byName(name.toUpperCase());
    } catch (e) {
      return byName('NONE');
    }
  }
}
