//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class IventCardItem {
  /// Returns a new [IventCardItem] instance.
  IventCardItem({
    required this.iventId,
    required this.iventName,
    this.thumbnailUrl,
    required this.locationName,
    required this.creatorId,
    required this.creatorType,
    required this.creatorUsername,
    this.creatorImageUrl,
    required this.isFavorited,
  });

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// URL to the ivent thumbnail image
  String? thumbnailUrl;

  /// Name of the ivent location
  String locationName;

  /// UUID of the ivent creator
  String creatorId;

  IventCreatorTypeEnum creatorType;

  /// Username of the ivent creator
  String creatorUsername;

  /// URL to the ivent creator image
  String? creatorImageUrl;

  /// Whether the ivent is favorited by the current user
  bool isFavorited;

  @override
  bool operator ==(Object other) => identical(this, other) || other is IventCardItem &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.locationName == locationName &&
    other.creatorId == creatorId &&
    other.creatorType == creatorType &&
    other.creatorUsername == creatorUsername &&
    other.creatorImageUrl == creatorImageUrl &&
    other.isFavorited == isFavorited;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (iventId.hashCode) +
    (iventName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (locationName.hashCode) +
    (creatorId.hashCode) +
    (creatorType.hashCode) +
    (creatorUsername.hashCode) +
    (creatorImageUrl == null ? 0 : creatorImageUrl!.hashCode) +
    (isFavorited.hashCode);

  @override
  String toString() => 'IventCardItem[iventId=$iventId, iventName=$iventName, thumbnailUrl=$thumbnailUrl, locationName=$locationName, creatorId=$creatorId, creatorType=$creatorType, creatorUsername=$creatorUsername, creatorImageUrl=$creatorImageUrl, isFavorited=$isFavorited]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'locationName'] = this.locationName;
      json[r'creatorId'] = this.creatorId;
      json[r'creatorType'] = this.creatorType;
      json[r'creatorUsername'] = this.creatorUsername;
    if (this.creatorImageUrl != null) {
      json[r'creatorImageUrl'] = this.creatorImageUrl;
    } else {
      json[r'creatorImageUrl'] = null;
    }
      json[r'isFavorited'] = this.isFavorited;
    return json;
  }

  /// Returns a new [IventCardItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static IventCardItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "IventCardItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "IventCardItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return IventCardItem(
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        locationName: mapValueOfType<String>(json, r'locationName')!,
        creatorId: mapValueOfType<String>(json, r'creatorId')!,
        creatorType: IventCreatorTypeEnum.fromJson(json[r'creatorType'])!,
        creatorUsername: mapValueOfType<String>(json, r'creatorUsername')!,
        creatorImageUrl: mapValueOfType<String>(json, r'creatorImageUrl'),
        isFavorited: mapValueOfType<bool>(json, r'isFavorited')!,
      );
    }
    return null;
  }

  static List<IventCardItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <IventCardItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = IventCardItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, IventCardItem> mapFromJson(dynamic json) {
    final map = <String, IventCardItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = IventCardItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of IventCardItem-objects as value to a dart map
  static Map<String, List<IventCardItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<IventCardItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = IventCardItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'iventId',
    'iventName',
    'locationName',
    'creatorId',
    'creatorType',
    'creatorUsername',
    'isFavorited',
  };
}

