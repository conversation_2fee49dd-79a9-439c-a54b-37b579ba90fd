//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchModeratorsForPageCreationReturn {
  /// Returns a new [SearchModeratorsForPageCreationReturn] instance.
  SearchModeratorsForPageCreationReturn({
    this.users = const [],
    required this.userCount,
  });

  /// List of users available for page creation
  List<UserListItem> users;

  /// Total number of users available for page creation
  ///
  /// Minimum value: 0
  int userCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchModeratorsForPageCreationReturn &&
    _deepEquality.equals(other.users, users) &&
    other.userCount == userCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (users.hashCode) +
    (userCount.hashCode);

  @override
  String toString() => 'SearchModeratorsForPageCreationReturn[users=$users, userCount=$userCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'users'] = this.users;
      json[r'userCount'] = this.userCount;
    return json;
  }

  /// Returns a new [SearchModeratorsForPageCreationReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchModeratorsForPageCreationReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchModeratorsForPageCreationReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchModeratorsForPageCreationReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchModeratorsForPageCreationReturn(
        users: UserListItem.listFromJson(json[r'users']),
        userCount: mapValueOfType<int>(json, r'userCount')!,
      );
    }
    return null;
  }

  static List<SearchModeratorsForPageCreationReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchModeratorsForPageCreationReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchModeratorsForPageCreationReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchModeratorsForPageCreationReturn> mapFromJson(dynamic json) {
    final map = <String, SearchModeratorsForPageCreationReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchModeratorsForPageCreationReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchModeratorsForPageCreationReturn-objects as value to a dart map
  static Map<String, List<SearchModeratorsForPageCreationReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchModeratorsForPageCreationReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchModeratorsForPageCreationReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'users',
    'userCount',
  };
}

