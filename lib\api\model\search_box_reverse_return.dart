//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxReverseReturn {
  /// Returns a new [SearchBoxReverseReturn] instance.
  SearchBoxReverseReturn({
    required this.type,
    this.features = const [],
    required this.attribution,
  });

  /// Collection type
  String type;

  /// List of features found at the location
  List<SearchBoxFeature> features;

  /// Attribution text
  String attribution;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxReverseReturn &&
    other.type == type &&
    _deepEquality.equals(other.features, features) &&
    other.attribution == attribution;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (type.hashCode) +
    (features.hashCode) +
    (attribution.hashCode);

  @override
  String toString() => 'SearchBoxReverseReturn[type=$type, features=$features, attribution=$attribution]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'type'] = this.type;
      json[r'features'] = this.features;
      json[r'attribution'] = this.attribution;
    return json;
  }

  /// Returns a new [SearchBoxReverseReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxReverseReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxReverseReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxReverseReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxReverseReturn(
        type: mapValueOfType<String>(json, r'type')!,
        features: SearchBoxFeature.listFromJson(json[r'features']),
        attribution: mapValueOfType<String>(json, r'attribution')!,
      );
    }
    return null;
  }

  static List<SearchBoxReverseReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxReverseReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxReverseReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxReverseReturn> mapFromJson(dynamic json) {
    final map = <String, SearchBoxReverseReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxReverseReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxReverseReturn-objects as value to a dart map
  static Map<String, List<SearchBoxReverseReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxReverseReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxReverseReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'type',
    'features',
    'attribution',
  };
}

