# openapi.model.SearchParticipantsByIventIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**users** | [**List<UserListItemWithRelationshipStatus>**](UserListItemWithRelationshipStatus.md) | List of users with their relationship status | [default to const []]
**userCount** | **int** | Total number of users | 
**viewType** | [**IventViewTypeEnum**](IventViewTypeEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



