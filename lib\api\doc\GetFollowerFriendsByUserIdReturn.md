# openapi.model.GetFollowerFriendsByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**friends** | [**List<UserListItem>**](UserListItem.md) | List of friends who are also followers | [default to const []]
**friendCount** | **int** | Total number of follower friends | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



