//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class PageBlacklistsApi {
  PageBlacklistsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Sayfa IDsi ile bir hesab<PERSON> engeller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [BlockUserByPageIdDto] blockUserByPageIdDto (required):
  Future<Response> blockUserByPageIdWithHttpInfo(String pageId, BlockUserByPageIdDto blockUserByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageBlacklists/{pageId}/block'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody = blockUserByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile bir hesabı engeller
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [BlockUserByPageIdDto] blockUserByPageIdDto (required):
  Future<void> blockUserByPageId(String pageId, BlockUserByPageIdDto blockUserByPageIdDto,) async {
    final response = await blockUserByPageIdWithHttpInfo(pageId, blockUserByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile engellenenleri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchPageBlocklistByPageIdWithHttpInfo(String pageId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pageBlacklists/{pageId}/blocklist'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile engellenenleri listeler
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchPageBlocklistByPageIdReturn?> searchPageBlocklistByPageId(String pageId, { String? q, int? limit, int? page, }) async {
    final response = await searchPageBlocklistByPageIdWithHttpInfo(pageId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchPageBlocklistByPageIdReturn',) as SearchPageBlocklistByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile bir hesabın engelini kaldırır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [UnblockUserByPageIdDto] unblockUserByPageIdDto (required):
  Future<Response> unblockUserByPageIdWithHttpInfo(String pageId, UnblockUserByPageIdDto unblockUserByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageBlacklists/{pageId}/unblock'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody = unblockUserByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile bir hesabın engelini kaldırır
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [UnblockUserByPageIdDto] unblockUserByPageIdDto (required):
  Future<void> unblockUserByPageId(String pageId, UnblockUserByPageIdDto unblockUserByPageIdDto,) async {
    final response = await unblockUserByPageIdWithHttpInfo(pageId, unblockUserByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

