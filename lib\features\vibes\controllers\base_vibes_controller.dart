import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Base controller for all Vibes feature controllers
///
/// Provides common functionality and shared resources for all controllers
/// within the Vibes feature. This includes video management, caching,
/// authentication service access, and common error handling patterns.
///
/// All Vibes controllers should extend this base class to ensure
/// consistent behavior and access to shared resources.
abstract class BaseVibesController extends BaseController {
  // Shared resources
  final VideoManager videoManager = VideoManager();
  final CacheManager cacheManager = CacheManager(
    Config(
      'vibeCache',
      stalePeriod: const Duration(hours: 1),
      maxNrOfCacheObjects: 100,
    ),
  );

  // Constructor
  BaseVibesController(AuthService authService) : super(authService);

  // Common methods

  /// Handles common error scenarios for Vibes operations
  void handleVibesError(dynamic error, [String? customMessage]) {
    final message = customMessage ?? 'Vibes yüklenirken bir hata oluştu.';
    print('Vibes Error: $error - $message');
    // Additional error handling can be added here
  }

  /// Cleans up video resources
  void cleanupVideoResources() {
    videoManager.disposeAll();
  }

  @override
  void closeController() {
    cleanupVideoResources();
    super.closeController();
  }
}
