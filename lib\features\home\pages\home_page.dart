import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_screen.dart';
import 'package:ivent_app/features/home/<USER>/filter_screen.dart';
import 'package:ivent_app/features/home/<USER>/map_screen.dart';
import 'package:ivent_app/features/home/<USER>/search_screen.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

/// Main home page with sliding panel interface
///
/// Displays a map as the main background with a sliding panel
/// that can show different screens (feed, search, filter).
/// Manages the overall home feature layout and navigation.
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // Controllers
  late final HomeController _controller;

  // Screen configurations
  static const List<Widget> _screens = [
    FeedScreen(),
    SearchScreen(),
    FilterScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _controller = Get.find<HomeController>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        final panelsController = _controller.homePanelsController;

        return IaSlidingPanel(
          defaultPanelState: PanelState.CLOSED,
          panelController: panelsController.panelController,
          isDraggable: panelsController.isPanelDraggable,
          maxHeight: Get.height,
          minHeight: 70,
          onPanelOpened: panelsController.setPanelOpen,
          onPanelClosed: panelsController.setPanelClosed,
          panel: IaBottomPanel(
            showSlideIndicator: panelsController.isPanelDraggable,
            body: _screens[panelsController.screenIndex],
          ),
          body: const SafeArea(child: MapScreen()),
        );
      }),
    );
  }
}
