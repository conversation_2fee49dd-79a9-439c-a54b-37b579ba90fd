# openapi.model.SearchUsersToAddByPageIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**users** | [**List<UserListItem>**](UserListItem.md) | List of users available to be added as page members | [default to const []]
**userCount** | **int** | Total number of users available to be added as page members | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



