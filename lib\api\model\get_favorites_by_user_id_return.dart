//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetFavoritesByUserIdReturn {
  /// Returns a new [GetFavoritesByUserIdReturn] instance.
  GetFavoritesByUserIdReturn({
    this.ivents = const [],
    required this.iventCount,
  });

  /// List of user's favorite ivents
  List<IventListItemWithIsFavorited> ivents;

  /// Total number of favorite ivents
  ///
  /// Minimum value: 0
  int iventCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetFavoritesByUserIdReturn &&
    _deepEquality.equals(other.ivents, ivents) &&
    other.iventCount == iventCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (ivents.hashCode) +
    (iventCount.hashCode);

  @override
  String toString() => 'GetFavoritesByUserIdReturn[ivents=$ivents, iventCount=$iventCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'ivents'] = this.ivents;
      json[r'iventCount'] = this.iventCount;
    return json;
  }

  /// Returns a new [GetFavoritesByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetFavoritesByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetFavoritesByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetFavoritesByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetFavoritesByUserIdReturn(
        ivents: IventListItemWithIsFavorited.listFromJson(json[r'ivents']),
        iventCount: mapValueOfType<int>(json, r'iventCount')!,
      );
    }
    return null;
  }

  static List<GetFavoritesByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetFavoritesByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetFavoritesByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetFavoritesByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetFavoritesByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetFavoritesByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetFavoritesByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetFavoritesByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetFavoritesByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetFavoritesByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'ivents',
    'iventCount',
  };
}

