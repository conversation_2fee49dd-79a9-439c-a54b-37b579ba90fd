# openapi.model.IventListItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**thumbnailUrl** | **String** | URL to the ivent thumbnail image | [optional] 
**locationName** | **String** | Name of the ivent location | 
**dates** | **List<String>** | List of dates for the ivent in ISO 8601 date-time format | [default to const []]
**creatorId** | **String** | UUID of the ivent creator | 
**creatorType** | [**IventCreatorTypeEnum**](IventCreatorTypeEnum.md) |  | 
**creatorUsername** | **String** | Username of the ivent creator | 
**creatorImageUrl** | **String** | URL to the ivent creator image | [optional] 
**memberCount** | **int** | Number of members in the ivent | 
**memberFirstnames** | **List<String>** | List of member's first names in the ivent | [default to const []]
**memberAvatarUrls** | **List<String>** | List of member avatar URLs in the ivent | [default to const []]
**viewType** | [**IventViewTypeEnum**](IventViewTypeEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



