# openapi.model.LocationItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**locationId** | **String** | UUID of the location | 
**mapboxId** | **String** | Mapbox place identifier | 
**locationName** | **String** | Name of the location | 
**openAddress** | **String** | Detailed address of the location | 
**latitude** | **double** | Latitude coordinate of the location | 
**longitude** | **double** | Longitude coordinate of the location | 
**state** | **String** | State where the location is situated | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



