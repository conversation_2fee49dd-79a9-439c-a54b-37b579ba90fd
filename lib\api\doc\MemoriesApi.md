# openapi.api.MemoriesApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createMemory**](MemoriesApi.md#memoriescontrollercreatememory) | **POST** /memories/create | Memory oluşturur
[**deleteMemoryByMemoryId**](MemoriesApi.md#memoriescontrollerdeletememorybymemoryid) | **DELETE** /memories/{id}/delete | Memory IDsi ile memory siler
[**getMemoryByMemoryId**](MemoriesApi.md#memoriescontrollergetmemorybymemoryid) | **GET** /memories/{id} | ID ile memory getirir


# **createMemory**
> CreateMemoryReturn createMemory(createMemoryDto)

Memory oluşturur

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MemoriesApi();
final createMemoryDto = CreateMemoryDto(); // CreateMemoryDto | 

try {
    final result = api_instance.createMemory(createMemoryDto);
    print(result);
} catch (e) {
    print('Exception when calling MemoriesApi->createMemory: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createMemoryDto** | [**CreateMemoryDto**](CreateMemoryDto.md)|  | 

### Return type

[**CreateMemoryReturn**](CreateMemoryReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteMemoryByMemoryId**
> deleteMemoryByMemoryId(id)

Memory IDsi ile memory siler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MemoriesApi();
final id = id_example; // String | 

try {
    api_instance.deleteMemoryByMemoryId(id);
} catch (e) {
    print('Exception when calling MemoriesApi->deleteMemoryByMemoryId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getMemoryByMemoryId**
> GetMemoryByMemoryIdReturn getMemoryByMemoryId(id, origin)

ID ile memory getirir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = MemoriesApi();
final id = id_example; // String | 
final origin = ; // MemoryOriginEnum | Origin of the memory

try {
    final result = api_instance.getMemoryByMemoryId(id, origin);
    print(result);
} catch (e) {
    print('Exception when calling MemoriesApi->getMemoryByMemoryId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **origin** | [**MemoryOriginEnum**](.md)| Origin of the memory | 

### Return type

[**GetMemoryByMemoryIdReturn**](GetMemoryByMemoryIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


