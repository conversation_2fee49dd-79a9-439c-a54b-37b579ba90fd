//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ApiClient {
  ApiClient({this.basePath = 'http://localhost', this.authentication,});

  final String basePath;
  final Authentication? authentication;

  var _client = Client();
  final _defaultHeaderMap = <String, String>{};

  /// Returns the current HTTP [Client] instance to use in this class.
  ///
  /// The return value is guaranteed to never be null.
  Client get client => _client;

  /// Requests to use a new HTTP [Client] in this class.
  set client(Client newClient) {
    _client = newClient;
  }

  Map<String, String> get defaultHeaderMap => _defaultHeaderMap;

  void addDefaultHeader(String key, String value) {
     _defaultHeaderMap[key] = value;
  }

  // We don't use a Map<String, String> for queryParams.
  // If collectionFormat is 'multi', a key might appear multiple times.
  Future<Response> invokeAPI(
    String path,
    String method,
    List<QueryParam> queryParams,
    Object? body,
    Map<String, String> headerParams,
    Map<String, String> formParams,
    String? contentType,
  ) async {
    await authentication?.applyToParams(queryParams, headerParams);

    headerParams.addAll(_defaultHeaderMap);
    if (contentType != null) {
      headerParams['Content-Type'] = contentType;
    }

    final urlEncodedQueryParams = queryParams.map((param) => '$param');
    final queryString = urlEncodedQueryParams.isNotEmpty ? '?${urlEncodedQueryParams.join('&')}' : '';
    final uri = Uri.parse('$basePath$path$queryString');

    try {
      // Special case for uploading a single file which isn't a 'multipart/form-data'.
      if (
        body is MultipartFile && (contentType == null ||
        !contentType.toLowerCase().startsWith('multipart/form-data'))
      ) {
        final request = StreamedRequest(method, uri);
        request.headers.addAll(headerParams);
        request.contentLength = body.length;
        body.finalize().listen(
          request.sink.add,
          onDone: request.sink.close,
          // ignore: avoid_types_on_closure_parameters
          onError: (Object error, StackTrace trace) => request.sink.close(),
          cancelOnError: true,
        );
        final response = await _client.send(request);
        return Response.fromStream(response);
      }

      if (body is MultipartRequest) {
        final request = MultipartRequest(method, uri);
        request.fields.addAll(body.fields);
        request.files.addAll(body.files);
        request.headers.addAll(body.headers);
        request.headers.addAll(headerParams);
        final response = await _client.send(request);
        return Response.fromStream(response);
      }

      final msgBody = contentType == 'application/x-www-form-urlencoded'
        ? formParams
        : await serializeAsync(body);
      final nullableHeaderParams = headerParams.isEmpty ? null : headerParams;

      switch(method) {
        case 'POST': return await _client.post(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'PUT': return await _client.put(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'DELETE': return await _client.delete(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'PATCH': return await _client.patch(uri, headers: nullableHeaderParams, body: msgBody,);
        case 'HEAD': return await _client.head(uri, headers: nullableHeaderParams,);
        case 'GET': return await _client.get(uri, headers: nullableHeaderParams,);
      }
    } on SocketException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'Socket operation failed: $method $path',
        error,
        trace,
      );
    } on TlsException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'TLS/SSL communication failed: $method $path',
        error,
        trace,
      );
    } on IOException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'I/O operation failed: $method $path',
        error,
        trace,
      );
    } on ClientException catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'HTTP connection failed: $method $path',
        error,
        trace,
      );
    } on Exception catch (error, trace) {
      throw ApiException.withInner(
        HttpStatus.badRequest,
        'Exception occurred: $method $path',
        error,
        trace,
      );
    }

    throw ApiException(
      HttpStatus.badRequest,
      'Invalid HTTP operation: $method $path',
    );
  }

  Future<dynamic> deserializeAsync(String value, String targetType, {bool growable = false,}) async =>
    // ignore: deprecated_member_use_from_same_package
    deserialize(value, targetType, growable: growable);

  @Deprecated('Scheduled for removal in OpenAPI Generator 6.x. Use deserializeAsync() instead.')
  dynamic deserialize(String value, String targetType, {bool growable = false,}) {
    // Remove all spaces. Necessary for regular expressions as well.
    targetType = targetType.replaceAll(' ', ''); // ignore: parameter_assignments

    // If the expected target type is String, nothing to do...
    return targetType == 'String'
      ? value
      : fromJson(json.decode(value), targetType, growable: growable);
  }

  // ignore: deprecated_member_use_from_same_package
  Future<String> serializeAsync(Object? value) async => serialize(value);

  @Deprecated('Scheduled for removal in OpenAPI Generator 6.x. Use serializeAsync() instead.')
  String serialize(Object? value) => value == null ? '' : json.encode(value);

  /// Returns a native instance of an OpenAPI class matching the [specified type][targetType].
  static dynamic fromJson(dynamic value, String targetType, {bool growable = false,}) {
    try {
      switch (targetType) {
        case 'String':
          return value is String ? value : value.toString();
        case 'int':
          return value is int ? value : int.parse('$value');
        case 'double':
          return value is double ? value : double.parse('$value');
        case 'bool':
          if (value is bool) {
            return value;
          }
          final valueString = '$value'.toLowerCase();
          return valueString == 'true' || valueString == '1';
        case 'DateTime':
          return value is DateTime ? value : DateTime.tryParse(value);
        case 'AccountTypeEnum':
          return AccountTypeEnumTypeTransformer().decode(value);
        case 'AddHobbiesByHobbyIdDto':
          return AddHobbiesByHobbyIdDto.fromJson(value);
        case 'AddModeratorByGroupIdDto':
          return AddModeratorByGroupIdDto.fromJson(value);
        case 'AddPageMembersByPageIdDto':
          return AddPageMembersByPageIdDto.fromJson(value);
        case 'AuthEnum':
          return AuthEnumTypeTransformer().decode(value);
        case 'BasicAccountListItem':
          return BasicAccountListItem.fromJson(value);
        case 'BlockUserByPageIdDto':
          return BlockUserByPageIdDto.fromJson(value);
        case 'CollabDto':
          return CollabDto.fromJson(value);
        case 'CollabratorListItem':
          return CollabratorListItem.fromJson(value);
        case 'CommentItem':
          return CommentItem.fromJson(value);
        case 'CreateCommentDto':
          return CreateCommentDto.fromJson(value);
        case 'CreateCommentReturn':
          return CreateCommentReturn.fromJson(value);
        case 'CreateGroupDto':
          return CreateGroupDto.fromJson(value);
        case 'CreateGroupReturn':
          return CreateGroupReturn.fromJson(value);
        case 'CreateIventDto':
          return CreateIventDto.fromJson(value);
        case 'CreateIventReturn':
          return CreateIventReturn.fromJson(value);
        case 'CreateMemoryDto':
          return CreateMemoryDto.fromJson(value);
        case 'CreateMemoryReturn':
          return CreateMemoryReturn.fromJson(value);
        case 'CreatePageDto':
          return CreatePageDto.fromJson(value);
        case 'CreatePageReturn':
          return CreatePageReturn.fromJson(value);
        case 'CreateVibeReturn':
          return CreateVibeReturn.fromJson(value);
        case 'FeedDateEnum':
          return FeedDateEnumTypeTransformer().decode(value);
        case 'FeedReturn':
          return FeedReturn.fromJson(value);
        case 'FriendListingTypeEnum':
          return FriendListingTypeEnumTypeTransformer().decode(value);
        case 'GetBannerByIventIdDto':
          return GetBannerByIventIdDto.fromJson(value);
        case 'GetBannerByIventIdReturn':
          return GetBannerByIventIdReturn.fromJson(value);
        case 'GetCommentsByVibeIdReturn':
          return GetCommentsByVibeIdReturn.fromJson(value);
        case 'GetContactsByUserIdDto':
          return GetContactsByUserIdDto.fromJson(value);
        case 'GetContactsByUserIdReturn':
          return GetContactsByUserIdReturn.fromJson(value);
        case 'GetFavoritesByUserIdReturn':
          return GetFavoritesByUserIdReturn.fromJson(value);
        case 'GetFollowerFriendsByUserIdReturn':
          return GetFollowerFriendsByUserIdReturn.fromJson(value);
        case 'GetFollowersByUserIdReturn':
          return GetFollowersByUserIdReturn.fromJson(value);
        case 'GetFollowingsByUserIdReturn':
          return GetFollowingsByUserIdReturn.fromJson(value);
        case 'GetGroupByGroupIdReturn':
          return GetGroupByGroupIdReturn.fromJson(value);
        case 'GetIventPageByIventIdReturn':
          return GetIventPageByIventIdReturn.fromJson(value);
        case 'GetIventsByUserIdReturn':
          return GetIventsByUserIdReturn.fromJson(value);
        case 'GetIventsCreatedByPageIdReturn':
          return GetIventsCreatedByPageIdReturn.fromJson(value);
        case 'GetLatestIventsReturn':
          return GetLatestIventsReturn.fromJson(value);
        case 'GetLatestLocationsReturn':
          return GetLatestLocationsReturn.fromJson(value);
        case 'GetLevelByUserIdReturn':
          return GetLevelByUserIdReturn.fromJson(value);
        case 'GetLikesByVibeIdReturn':
          return GetLikesByVibeIdReturn.fromJson(value);
        case 'GetLocationsReturn':
          return GetLocationsReturn.fromJson(value);
        case 'GetMemoryByMemoryIdReturn':
          return GetMemoryByMemoryIdReturn.fromJson(value);
        case 'GetMemoryFoldersByUserIdReturn':
          return GetMemoryFoldersByUserIdReturn.fromJson(value);
        case 'GetNotificationsReturn':
          return GetNotificationsReturn.fromJson(value);
        case 'GetPageByPageIdReturn':
          return GetPageByPageIdReturn.fromJson(value);
        case 'GetPageDetailsByPageIdReturn':
          return GetPageDetailsByPageIdReturn.fromJson(value);
        case 'GetPagesByUserIdReturn':
          return GetPagesByUserIdReturn.fromJson(value);
        case 'GetSuggestedImagesReturn':
          return GetSuggestedImagesReturn.fromJson(value);
        case 'GetUserBannerByUserIdReturn':
          return GetUserBannerByUserIdReturn.fromJson(value);
        case 'GetUserBlocklistReturn':
          return GetUserBlocklistReturn.fromJson(value);
        case 'GetUserByUserIdReturn':
          return GetUserByUserIdReturn.fromJson(value);
        case 'GetVibeByVibeIdReturn':
          return GetVibeByVibeIdReturn.fromJson(value);
        case 'GetVibeFoldersByPageIdReturn':
          return GetVibeFoldersByPageIdReturn.fromJson(value);
        case 'GetVibeFoldersByUserIdReturn':
          return GetVibeFoldersByUserIdReturn.fromJson(value);
        case 'GetVibesReturn':
          return GetVibesReturn.fromJson(value);
        case 'GroupListItem':
          return GroupListItem.fromJson(value);
        case 'GroupMembershipStatusEnum':
          return GroupMembershipStatusEnumTypeTransformer().decode(value);
        case 'HobbiesSearchOriginEnum':
          return HobbiesSearchOriginEnumTypeTransformer().decode(value);
        case 'HobbyItem':
          return HobbyItem.fromJson(value);
        case 'InviteFriendsByIventIdDto':
          return InviteFriendsByIventIdDto.fromJson(value);
        case 'InviteMembersByGroupIdDto':
          return InviteMembersByGroupIdDto.fromJson(value);
        case 'IventCardItem':
          return IventCardItem.fromJson(value);
        case 'IventCreatorTypeEnum':
          return IventCreatorTypeEnumTypeTransformer().decode(value);
        case 'IventListItem':
          return IventListItem.fromJson(value);
        case 'IventListItemWithIsFavorited':
          return IventListItemWithIsFavorited.fromJson(value);
        case 'IventListingTypeEnum':
          return IventListingTypeEnumTypeTransformer().decode(value);
        case 'IventPrivacyEnum':
          return IventPrivacyEnumTypeTransformer().decode(value);
        case 'IventViewTypeEnum':
          return IventViewTypeEnumTypeTransformer().decode(value);
        case 'JoinIventAndCreateSquadByIventIdDto':
          return JoinIventAndCreateSquadByIventIdDto.fromJson(value);
        case 'LocationItem':
          return LocationItem.fromJson(value);
        case 'MapReturn':
          return MapReturn.fromJson(value);
        case 'MarkerItem':
          return MarkerItem.fromJson(value);
        case 'MediaFormatEnum':
          return MediaFormatEnumTypeTransformer().decode(value);
        case 'MemoryFolderCardItem':
          return MemoryFolderCardItem.fromJson(value);
        case 'MemoryOriginEnum':
          return MemoryOriginEnumTypeTransformer().decode(value);
        case 'NotificationEnum':
          return NotificationEnumTypeTransformer().decode(value);
        case 'NotificationItem':
          return NotificationItem.fromJson(value);
        case 'NotificationReplyTypeEnum':
          return NotificationReplyTypeEnumTypeTransformer().decode(value);
        case 'PageMembershipStatusEnum':
          return PageMembershipStatusEnumTypeTransformer().decode(value);
        case 'RegisterDto':
          return RegisterDto.fromJson(value);
        case 'RegisterReturn':
          return RegisterReturn.fromJson(value);
        case 'RemoveCollabByIventIdDto':
          return RemoveCollabByIventIdDto.fromJson(value);
        case 'RemoveFollowerByPageIdDto':
          return RemoveFollowerByPageIdDto.fromJson(value);
        case 'RemoveFollowerByUserIdDto':
          return RemoveFollowerByUserIdDto.fromJson(value);
        case 'RemoveMemberByGroupIdDto':
          return RemoveMemberByGroupIdDto.fromJson(value);
        case 'RemoveModeratorByGroupIdDto':
          return RemoveModeratorByGroupIdDto.fromJson(value);
        case 'RemovePageMemberByPageIdDto':
          return RemovePageMemberByPageIdDto.fromJson(value);
        case 'RemovePageModeratorByPageIdDto':
          return RemovePageModeratorByPageIdDto.fromJson(value);
        case 'SearchAccountReturn':
          return SearchAccountReturn.fromJson(value);
        case 'SearchAdministrationByPageIdReturn':
          return SearchAdministrationByPageIdReturn.fromJson(value);
        case 'SearchBoxCategoryItem':
          return SearchBoxCategoryItem.fromJson(value);
        case 'SearchBoxCategoryListReturn':
          return SearchBoxCategoryListReturn.fromJson(value);
        case 'SearchBoxCategoryReturn':
          return SearchBoxCategoryReturn.fromJson(value);
        case 'SearchBoxContext':
          return SearchBoxContext.fromJson(value);
        case 'SearchBoxContextAddress':
          return SearchBoxContextAddress.fromJson(value);
        case 'SearchBoxContextDistrict':
          return SearchBoxContextDistrict.fromJson(value);
        case 'SearchBoxContextLocality':
          return SearchBoxContextLocality.fromJson(value);
        case 'SearchBoxContextNeighborhood':
          return SearchBoxContextNeighborhood.fromJson(value);
        case 'SearchBoxContextPlace':
          return SearchBoxContextPlace.fromJson(value);
        case 'SearchBoxContextPostcode':
          return SearchBoxContextPostcode.fromJson(value);
        case 'SearchBoxContextRegion':
          return SearchBoxContextRegion.fromJson(value);
        case 'SearchBoxContextStreet':
          return SearchBoxContextStreet.fromJson(value);
        case 'SearchBoxCoordinates':
          return SearchBoxCoordinates.fromJson(value);
        case 'SearchBoxFeature':
          return SearchBoxFeature.fromJson(value);
        case 'SearchBoxForwardReturn':
          return SearchBoxForwardReturn.fromJson(value);
        case 'SearchBoxGeometry':
          return SearchBoxGeometry.fromJson(value);
        case 'SearchBoxProperties':
          return SearchBoxProperties.fromJson(value);
        case 'SearchBoxRetrieveReturn':
          return SearchBoxRetrieveReturn.fromJson(value);
        case 'SearchBoxReverseReturn':
          return SearchBoxReverseReturn.fromJson(value);
        case 'SearchBoxRoutablePoints':
          return SearchBoxRoutablePoints.fromJson(value);
        case 'SearchBoxSuggestFeature':
          return SearchBoxSuggestFeature.fromJson(value);
        case 'SearchBoxSuggestReturn':
          return SearchBoxSuggestReturn.fromJson(value);
        case 'SearchCollabsForIventCreationReturn':
          return SearchCollabsForIventCreationReturn.fromJson(value);
        case 'SearchCollabsReturn':
          return SearchCollabsReturn.fromJson(value);
        case 'SearchFollowersByPageIdReturn':
          return SearchFollowersByPageIdReturn.fromJson(value);
        case 'SearchFriendsByUserIdReturn':
          return SearchFriendsByUserIdReturn.fromJson(value);
        case 'SearchGroupMembersByGroupIdReturn':
          return SearchGroupMembersByGroupIdReturn.fromJson(value);
        case 'SearchHobbiesReturn':
          return SearchHobbiesReturn.fromJson(value);
        case 'SearchInvitableUsersByIventIdReturn':
          return SearchInvitableUsersByIventIdReturn.fromJson(value);
        case 'SearchIventReturn':
          return SearchIventReturn.fromJson(value);
        case 'SearchModeratorsForPageCreationReturn':
          return SearchModeratorsForPageCreationReturn.fromJson(value);
        case 'SearchPageBlocklistByPageIdReturn':
          return SearchPageBlocklistByPageIdReturn.fromJson(value);
        case 'SearchPageMembersByPageIdReturn':
          return SearchPageMembersByPageIdReturn.fromJson(value);
        case 'SearchParticipantsByIventIdReturn':
          return SearchParticipantsByIventIdReturn.fromJson(value);
        case 'SearchUniversitiesReturn':
          return SearchUniversitiesReturn.fromJson(value);
        case 'SearchUsersForGroupCreationReturn':
          return SearchUsersForGroupCreationReturn.fromJson(value);
        case 'SearchUsersToAddByPageIdReturn':
          return SearchUsersToAddByPageIdReturn.fromJson(value);
        case 'SendVerificationCodeDto':
          return SendVerificationCodeDto.fromJson(value);
        case 'SideMenuPageItem':
          return SideMenuPageItem.fromJson(value);
        case 'TransferAdministrationByGroupIdDto':
          return TransferAdministrationByGroupIdDto.fromJson(value);
        case 'TransferPageAdministrationByPageIdDto':
          return TransferPageAdministrationByPageIdDto.fromJson(value);
        case 'UnblockUserByPageIdDto':
          return UnblockUserByPageIdDto.fromJson(value);
        case 'UniversityItem':
          return UniversityItem.fromJson(value);
        case 'UpdateByUserIdDto':
          return UpdateByUserIdDto.fromJson(value);
        case 'UpdateByVibeIdDto':
          return UpdateByVibeIdDto.fromJson(value);
        case 'UpdateDateByIventIdDto':
          return UpdateDateByIventIdDto.fromJson(value);
        case 'UpdateDescriptionByPageIdDto':
          return UpdateDescriptionByPageIdDto.fromJson(value);
        case 'UpdateDetailsByIventIdDto':
          return UpdateDetailsByIventIdDto.fromJson(value);
        case 'UpdateEmailByUserIdDto':
          return UpdateEmailByUserIdDto.fromJson(value);
        case 'UpdateGradByUserIdDto':
          return UpdateGradByUserIdDto.fromJson(value);
        case 'UpdateLinksByPageIdDto':
          return UpdateLinksByPageIdDto.fromJson(value);
        case 'UpdateLocationByIventIdDto':
          return UpdateLocationByIventIdDto.fromJson(value);
        case 'UpdateLocationByPageIdDto':
          return UpdateLocationByPageIdDto.fromJson(value);
        case 'UpdatePhoneNumberByUserIdDto':
          return UpdatePhoneNumberByUserIdDto.fromJson(value);
        case 'UserEduVerificationEnum':
          return UserEduVerificationEnumTypeTransformer().decode(value);
        case 'UserGenderEnum':
          return UserGenderEnumTypeTransformer().decode(value);
        case 'UserListItem':
          return UserListItem.fromJson(value);
        case 'UserListItemWithGroupRole':
          return UserListItemWithGroupRole.fromJson(value);
        case 'UserListItemWithPageRole':
          return UserListItemWithPageRole.fromJson(value);
        case 'UserListItemWithPhoneNumber':
          return UserListItemWithPhoneNumber.fromJson(value);
        case 'UserListItemWithRelationshipStatus':
          return UserListItemWithRelationshipStatus.fromJson(value);
        case 'UserRelationshipStatusEnum':
          return UserRelationshipStatusEnumTypeTransformer().decode(value);
        case 'UserRoleEnum':
          return UserRoleEnumTypeTransformer().decode(value);
        case 'ValidateDto':
          return ValidateDto.fromJson(value);
        case 'ValidateReturn':
          return ValidateReturn.fromJson(value);
        case 'VibeFolderCardItem':
          return VibeFolderCardItem.fromJson(value);
        case 'VibeItem':
          return VibeItem.fromJson(value);
        case 'VibePrivacyEnum':
          return VibePrivacyEnumTypeTransformer().decode(value);
        default:
          dynamic match;
          if (value is List && (match = _regList.firstMatch(targetType)?.group(1)) != null) {
            return value
              .map<dynamic>((dynamic v) => fromJson(v, match, growable: growable,))
              .toList(growable: growable);
          }
          if (value is Set && (match = _regSet.firstMatch(targetType)?.group(1)) != null) {
            return value
              .map<dynamic>((dynamic v) => fromJson(v, match, growable: growable,))
              .toSet();
          }
          if (value is Map && (match = _regMap.firstMatch(targetType)?.group(1)) != null) {
            return Map<String, dynamic>.fromIterables(
              value.keys.cast<String>(),
              value.values.map<dynamic>((dynamic v) => fromJson(v, match, growable: growable,)),
            );
          }
      }
    } on Exception catch (error, trace) {
      throw ApiException.withInner(HttpStatus.internalServerError, 'Exception during deserialization.', error, trace,);
    }
    throw ApiException(HttpStatus.internalServerError, 'Could not find a suitable class for deserialization',);
  }
}

/// Primarily intended for use in an isolate.
class DeserializationMessage {
  const DeserializationMessage({
    required this.json,
    required this.targetType,
    this.growable = false,
  });

  /// The JSON value to deserialize.
  final String json;

  /// Target type to deserialize to.
  final String targetType;

  /// Whether to make deserialized lists or maps growable.
  final bool growable;
}

/// Primarily intended for use in an isolate.
Future<dynamic> decodeAsync(DeserializationMessage message) async {
  // Remove all spaces. Necessary for regular expressions as well.
  final targetType = message.targetType.replaceAll(' ', '');

  // If the expected target type is String, nothing to do...
  return targetType == 'String'
    ? message.json
    : json.decode(message.json);
}

/// Primarily intended for use in an isolate.
Future<dynamic> deserializeAsync(DeserializationMessage message) async {
  // Remove all spaces. Necessary for regular expressions as well.
  final targetType = message.targetType.replaceAll(' ', '');

  // If the expected target type is String, nothing to do...
  return targetType == 'String'
    ? message.json
    : ApiClient.fromJson(
        json.decode(message.json),
        targetType,
        growable: message.growable,
      );
}

/// Primarily intended for use in an isolate.
Future<String> serializeAsync(Object? value) async => value == null ? '' : json.encode(value);
