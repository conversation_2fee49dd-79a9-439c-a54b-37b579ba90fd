//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Status of the relationship between the current user and the user in the list
class UserRelationshipStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const UserRelationshipStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const pending = UserRelationshipStatusEnum._(r'pending');
  static const accepted = UserRelationshipStatusEnum._(r'accepted');
  static const blocked = UserRelationshipStatusEnum._(r'blocked');

  /// List of all possible values in this [enum][UserRelationshipStatusEnum].
  static const values = <UserRelationshipStatusEnum>[
    pending,
    accepted,
    blocked,
  ];

  static UserRelationshipStatusEnum? fromJson(dynamic value) => UserRelationshipStatusEnumTypeTransformer().decode(value);

  static List<UserRelationshipStatusEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UserRelationshipStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UserRelationshipStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [UserRelationshipStatusEnum] to String,
/// and [decode] dynamic data back to [UserRelationshipStatusEnum].
class UserRelationshipStatusEnumTypeTransformer {
  factory UserRelationshipStatusEnumTypeTransformer() => _instance ??= const UserRelationshipStatusEnumTypeTransformer._();

  const UserRelationshipStatusEnumTypeTransformer._();

  String encode(UserRelationshipStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a UserRelationshipStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  UserRelationshipStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'pending': return UserRelationshipStatusEnum.pending;
        case r'accepted': return UserRelationshipStatusEnum.accepted;
        case r'blocked': return UserRelationshipStatusEnum.blocked;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [UserRelationshipStatusEnumTypeTransformer] instance.
  static UserRelationshipStatusEnumTypeTransformer? _instance;
}

