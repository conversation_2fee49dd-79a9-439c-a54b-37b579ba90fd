# openapi.model.CreateMemoryDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**mediaFormat** | [**MediaFormatEnum**](MediaFormatEnum.md) |  | 
**caption** | **String** | Caption or description for the memory | [optional] 
**squadId** | **String** |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



