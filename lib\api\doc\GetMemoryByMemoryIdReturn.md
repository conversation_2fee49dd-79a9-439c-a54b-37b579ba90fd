# openapi.model.GetMemoryByMemoryIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**memoryId** | **String** | UUID of the memory | 
**mediaUrl** | **String** | URL to the memory media | 
**thumbnailUrl** | **String** | URL to the memory thumbnail | [optional] 
**caption** | **String** | Caption of the memory | 
**creatorId** | **String** | UUID of the memory creator | 
**creatorUsername** | **String** | Username of the memory creator | 
**creatorAvatarUrl** | **String** | URL to the memory creator image | [optional] 
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**dates** | **List<String>** | List of dates for the ivent in ISO 8601 date-time format | [optional] [default to const []]
**memberCount** | **int** | Number of members in the ivent | [optional] 
**memberFirstnames** | **List<String>** | List of member's first names in the ivent | [optional] [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



