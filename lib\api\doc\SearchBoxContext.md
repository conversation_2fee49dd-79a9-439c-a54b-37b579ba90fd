# openapi.model.SearchBoxContext

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**region** | [**SearchBoxContextRegion**](SearchBoxContextRegion.md) | Region context information | [optional] 
**postcode** | [**SearchBoxContextPostcode**](SearchBoxContextPostcode.md) | Postcode context information | [optional] 
**district** | [**SearchBoxContextDistrict**](SearchBoxContextDistrict.md) | District context information | [optional] 
**place** | [**SearchBoxContextPlace**](SearchBoxContextPlace.md) | Place context information | [optional] 
**locality** | [**SearchBoxContextLocality**](SearchBoxContextLocality.md) | Locality context information | [optional] 
**neighborhood** | [**SearchBoxContextNeighborhood**](SearchBoxContextNeighborhood.md) | Neighborhood context information | [optional] 
**address** | [**SearchBoxContextAddress**](SearchBoxContextAddress.md) | Address context information | [optional] 
**street** | [**SearchBoxContextStreet**](SearchBoxContextStreet.md) | Street context information | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



