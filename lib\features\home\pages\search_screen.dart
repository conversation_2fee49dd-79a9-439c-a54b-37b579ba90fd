import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/profile.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/utils/share_utils.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/searchable/home_search_controller.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({Key? key}) : super(key: key);

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final HomeController _controller = Get.find();

  HomeSearchController get _homeSearchController => _controller.homeSearchController;

  /// Shares an ivent using the ShareUtils
  void _shareIvent(String iventId, List<IventCardItem> ivents) {
    final ivent = ivents.firstWhereOrNull((item) => item.iventId == iventId);
    if (ivent != null) {
      ShareUtils.shareIventWithLoading(
        iventId: ivent.iventId,
        iventName: ivent.iventName,
        thumbnailUrl: ivent.thumbnailUrl,
        context: context,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) => _controller.goToFeedPage(),
      child: Obx(() {
        return IaSearchScreen(
          margin: const EdgeInsets.all(AppDimensions.padding20),
          textEditingController: _homeSearchController.searchTabIndex == 0
              ? _homeSearchController.textEditingControllerIvent
              : _homeSearchController.textEditingControllerAccount,
          body: _buildBody(),
        );
      }),
    );
  }

  Widget _buildBody() {
    return DefaultTabController(
      initialIndex: 0,
      length: 2,
      child: Column(
        children: [
          Container(
            height: 40,
            child: TabBar(
              dividerHeight: 4,
              indicator: UnderlineTabIndicator(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                borderSide: const BorderSide(width: 4, color: AppColors.primary),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: AppColors.lightGrey,
              overlayColor: WidgetStateProperty.all(Colors.transparent),
              onTap: (index) => _homeSearchController.searchTabIndex = index,
              labelStyle: AppTextStyles.size16Medium,
              unselectedLabelStyle: AppTextStyles.size16MediumTextSecondary,
              tabs: const [
                Tab(text: 'iVent\'ler'),
                Tab(text: 'Hesaplar'),
              ],
            ),
          ),
          const SizedBox(height: AppDimensions.padding16),
          Expanded(
            child: TabBarView(
              physics: const ScrollPhysics(),
              children: [
                _buildIventTab(),
                _buildAccountTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIventTab() {
    return Obx(() {
      final ivents = _homeSearchController.searchedIventResults;
      return IaSearchPlaceholder(
        entityType: 'iVent',
        isSearching: _homeSearchController.isSearchingIvent,
        isQueryEmpty: _homeSearchController.isQueryEmptyIvent,
        isResultsEmpty: _homeSearchController.isResultsEmptyIvent,
        isDefaultStateEmpty: true,
        builder: (context) {
          return Column(
            children: [
              IaIventGrid(
                iventItems: ivents,
                onLoadMore: () {},
                onRefresh: () {},
                onShare: (iventId) => _shareIvent(iventId, ivents),
              ),
            ],
          );
        },
      );
    });
  }

  Widget _buildAccountTab() {
    return Obx(() {
      final accounts = _homeSearchController.searchedAccountResults;
      return IaSearchPlaceholder(
        entityType: 'Hesap',
        isSearching: _homeSearchController.isSearchingAccount,
        isQueryEmpty: _homeSearchController.isQueryEmptyAccount,
        isResultsEmpty: _homeSearchController.isResultsEmptyAccount,
        isDefaultStateEmpty: true,
        builder: (context) {
          return ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: accounts.length,
            itemBuilder: (context, index) {
              final account = accounts[index];
              return IaListTile.withImageUrl(
                onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: account.accountId),
                avatarUrl: account.accountImageUrl,
                title: account.accountName,
              );
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          );
        },
      );
    });
  }
}
