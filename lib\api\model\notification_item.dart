//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class NotificationItem {
  /// Returns a new [NotificationItem] instance.
  NotificationItem({
    required this.notificationType,
    required this.notificationId,
    required this.createdAt,
    required this.accountType,
    required this.accountId,
    required this.accountUsername,
    this.accountAvatarUrl,
    required this.contentType,
    this.contentId,
    this.contentThumbnailUrl,
    required this.contentName,
    required this.contentItem,
    required this.actionType,
    required this.actionId,
  });

  NotificationEnum notificationType;

  /// Unique identifier of the notification
  String notificationId;

  /// Timestamp when the notification was created, in ISO 8601 date-time format
  String createdAt;

  AccountTypeEnum accountType;

  /// UUID of the account that triggered the notification
  String accountId;

  /// Username of the account that triggered the notification
  String accountUsername;

  /// Avatar URL of the account that triggered the notification
  String? accountAvatarUrl;

  /// Type of content related to the notification
  String contentType;

  /// UUID of the content related to the notification
  String? contentId;

  /// Thumbnail URL of the content
  String? contentThumbnailUrl;

  /// Name or title of the content
  String contentName;

  /// Description of the content item
  String contentItem;

  /// Type of action that triggered the notification
  String actionType;

  /// UUID of the item to navigate to when notification is tapped
  String actionId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is NotificationItem &&
    other.notificationType == notificationType &&
    other.notificationId == notificationId &&
    other.createdAt == createdAt &&
    other.accountType == accountType &&
    other.accountId == accountId &&
    other.accountUsername == accountUsername &&
    other.accountAvatarUrl == accountAvatarUrl &&
    other.contentType == contentType &&
    other.contentId == contentId &&
    other.contentThumbnailUrl == contentThumbnailUrl &&
    other.contentName == contentName &&
    other.contentItem == contentItem &&
    other.actionType == actionType &&
    other.actionId == actionId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (notificationType.hashCode) +
    (notificationId.hashCode) +
    (createdAt.hashCode) +
    (accountType.hashCode) +
    (accountId.hashCode) +
    (accountUsername.hashCode) +
    (accountAvatarUrl == null ? 0 : accountAvatarUrl!.hashCode) +
    (contentType.hashCode) +
    (contentId == null ? 0 : contentId!.hashCode) +
    (contentThumbnailUrl == null ? 0 : contentThumbnailUrl!.hashCode) +
    (contentName.hashCode) +
    (contentItem.hashCode) +
    (actionType.hashCode) +
    (actionId.hashCode);

  @override
  String toString() => 'NotificationItem[notificationType=$notificationType, notificationId=$notificationId, createdAt=$createdAt, accountType=$accountType, accountId=$accountId, accountUsername=$accountUsername, accountAvatarUrl=$accountAvatarUrl, contentType=$contentType, contentId=$contentId, contentThumbnailUrl=$contentThumbnailUrl, contentName=$contentName, contentItem=$contentItem, actionType=$actionType, actionId=$actionId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'notificationType'] = this.notificationType;
      json[r'notificationId'] = this.notificationId;
      json[r'createdAt'] = this.createdAt;
      json[r'accountType'] = this.accountType;
      json[r'accountId'] = this.accountId;
      json[r'accountUsername'] = this.accountUsername;
    if (this.accountAvatarUrl != null) {
      json[r'accountAvatarUrl'] = this.accountAvatarUrl;
    } else {
      json[r'accountAvatarUrl'] = null;
    }
      json[r'contentType'] = this.contentType;
    if (this.contentId != null) {
      json[r'contentId'] = this.contentId;
    } else {
      json[r'contentId'] = null;
    }
    if (this.contentThumbnailUrl != null) {
      json[r'contentThumbnailUrl'] = this.contentThumbnailUrl;
    } else {
      json[r'contentThumbnailUrl'] = null;
    }
      json[r'contentName'] = this.contentName;
      json[r'contentItem'] = this.contentItem;
      json[r'actionType'] = this.actionType;
      json[r'actionId'] = this.actionId;
    return json;
  }

  /// Returns a new [NotificationItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static NotificationItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "NotificationItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "NotificationItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return NotificationItem(
        notificationType: NotificationEnum.fromJson(json[r'notificationType'])!,
        notificationId: mapValueOfType<String>(json, r'notificationId')!,
        createdAt: mapValueOfType<String>(json, r'createdAt')!,
        accountType: AccountTypeEnum.fromJson(json[r'accountType'])!,
        accountId: mapValueOfType<String>(json, r'accountId')!,
        accountUsername: mapValueOfType<String>(json, r'accountUsername')!,
        accountAvatarUrl: mapValueOfType<String>(json, r'accountAvatarUrl'),
        contentType: mapValueOfType<String>(json, r'contentType')!,
        contentId: mapValueOfType<String>(json, r'contentId'),
        contentThumbnailUrl: mapValueOfType<String>(json, r'contentThumbnailUrl'),
        contentName: mapValueOfType<String>(json, r'contentName')!,
        contentItem: mapValueOfType<String>(json, r'contentItem')!,
        actionType: mapValueOfType<String>(json, r'actionType')!,
        actionId: mapValueOfType<String>(json, r'actionId')!,
      );
    }
    return null;
  }

  static List<NotificationItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <NotificationItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = NotificationItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, NotificationItem> mapFromJson(dynamic json) {
    final map = <String, NotificationItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = NotificationItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of NotificationItem-objects as value to a dart map
  static Map<String, List<NotificationItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<NotificationItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = NotificationItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'notificationType',
    'notificationId',
    'createdAt',
    'accountType',
    'accountId',
    'accountUsername',
    'contentType',
    'contentName',
    'contentItem',
    'actionType',
    'actionId',
  };
}

