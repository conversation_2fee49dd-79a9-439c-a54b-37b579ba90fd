# openapi.model.CreateIventDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**creatorType** | [**AccountTypeEnum**](AccountTypeEnum.md) |  | 
**iventName** | **String** | Ivent name can only contain letters, numbers, underscores, and hyphens | 
**thumbnailUrl** | **String** | URL to the ivent thumbnail image | [optional] 
**thumbnailBuffer** | **String** | Base64 encoded thumbnail image buffer | [optional] 
**dates** | **List<String>** | Array of date strings in ISO 8601 date-time format | [default to const []]
**mapboxId** | **String** | Mapbox place ID for location | 
**latitude** | **double** | Latitude coordinate of the ivent location | 
**longitude** | **double** | Longitude coordinate of the ivent location | 
**description** | **String** | Detailed description of the ivent | [optional] 
**categoryTagId** | **String** | UUID of the category tag for this ivent | 
**tagIds** | **List<String>** | Array of hobby tag UUIDs associated with this ivent | [default to const []]
**privacy** | [**IventPrivacyEnum**](IventPrivacyEnum.md) |  | 
**allowedUniversityCodes** | **List<String>** | Array of university codes that are allowed to join this ivent | [default to const []]
**collabs** | [**List<CollabDto>**](CollabDto.md) | Array of collaborators for this ivent | [default to const []]
**googleFormsUrl** | **String** | URL to Google Forms for registration | [optional] 
**instagramUsername** | **String** | Instagram username for the ivent | [optional] 
**whatsappUrl** | **String** | WhatsApp group URL | [optional] 
**isWhatsappUrlPrivate** | **bool** | Whether the WhatsApp URL should be kept private | [optional] 
**whatsappNumber** | **String** | WhatsApp contact number | [optional] 
**callNumber** | **String** | Phone number for calls | [optional] 
**websiteUrl** | **String** | Website URL for the ivent | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



