# openapi.model.GetLikesByVibeIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**likes** | [**List<UserListItemWithRelationshipStatus>**](UserListItemWithRelationshipStatus.md) | List of users who liked the vibe | [default to const []]
**likeCount** | **int** | Total number of likes on the vibe | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



