//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CollabDto {
  /// Returns a new [CollabDto] instance.
  CollabDto({
    required this.id,
    required this.type,
  });

  /// UUID of the collaborator
  String id;

  AccountTypeEnum type;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CollabDto &&
    other.id == id &&
    other.type == type;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id.hashCode) +
    (type.hashCode);

  @override
  String toString() => 'CollabDto[id=$id, type=$type]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'id'] = this.id;
      json[r'type'] = this.type;
    return json;
  }

  /// Returns a new [CollabDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CollabDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CollabDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CollabDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CollabDto(
        id: mapValueOfType<String>(json, r'id')!,
        type: AccountTypeEnum.fromJson(json[r'type'])!,
      );
    }
    return null;
  }

  static List<CollabDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CollabDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CollabDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CollabDto> mapFromJson(dynamic json) {
    final map = <String, CollabDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CollabDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CollabDto-objects as value to a dart map
  static Map<String, List<CollabDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CollabDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CollabDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
    'type',
  };
}

