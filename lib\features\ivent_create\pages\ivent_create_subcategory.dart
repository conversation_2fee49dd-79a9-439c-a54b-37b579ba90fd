import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class IventCreateSubcategory extends StatefulWidget {
  @override
  State<IventCreateSubcategory> createState() => _IventCreateSubcategoryState();
}

class _IventCreateSubcategoryState extends State<IventCreateSubcategory> {
  final IventCreateController _controller = Get.find();
  int _selectedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final list = Hobby.filterHobbies(level: 2, parentHobbyName: _controller.formController.selectedCategory!.hobbyName);
    return IaScaffold.noSearch(
      title: 'Alt Kategori Seçiniz',
      showDivider: false,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: ListView.separated(
        padding: const EdgeInsets.only(top: AppDimensions.padding12, bottom: 100),
        itemCount: list.length,
        itemBuilder: (context, index) {
          final element = list[index];
          return IventCreateButtons.categoryBar(
            onTap: () => setState(() {
              _selectedIndex = index;
              _controller.formController.selectedSubCategory = element;
              _controller.goToImageSelectionPage();
            }),
            isSelected: _selectedIndex == index,
            text: element.hobbyName,
          );
        },
        separatorBuilder: IaListTile.separatorBuilder20,
      ),
    );
  }
}
