//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class UserListItem {
  /// Returns a new [UserListItem] instance.
  UserListItem({
    required this.userId,
    required this.username,
    this.avatarUrl,
    this.university,
  });

  /// UUID of the user
  String userId;

  /// Username of the user
  String username;

  /// URL to the user's avatar image
  String? avatarUrl;

  /// Name of the user's university
  String? university;

  @override
  bool operator ==(Object other) => identical(this, other) || other is UserListItem &&
    other.userId == userId &&
    other.username == username &&
    other.avatarUrl == avatarUrl &&
    other.university == university;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userId.hashCode) +
    (username.hashCode) +
    (avatarUrl == null ? 0 : avatarUrl!.hashCode) +
    (university == null ? 0 : university!.hashCode);

  @override
  String toString() => 'UserListItem[userId=$userId, username=$username, avatarUrl=$avatarUrl, university=$university]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'userId'] = this.userId;
      json[r'username'] = this.username;
    if (this.avatarUrl != null) {
      json[r'avatarUrl'] = this.avatarUrl;
    } else {
      json[r'avatarUrl'] = null;
    }
    if (this.university != null) {
      json[r'university'] = this.university;
    } else {
      json[r'university'] = null;
    }
    return json;
  }

  /// Returns a new [UserListItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static UserListItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "UserListItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "UserListItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return UserListItem(
        userId: mapValueOfType<String>(json, r'userId')!,
        username: mapValueOfType<String>(json, r'username')!,
        avatarUrl: mapValueOfType<String>(json, r'avatarUrl'),
        university: mapValueOfType<String>(json, r'university'),
      );
    }
    return null;
  }

  static List<UserListItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UserListItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UserListItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, UserListItem> mapFromJson(dynamic json) {
    final map = <String, UserListItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = UserListItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of UserListItem-objects as value to a dart map
  static Map<String, List<UserListItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<UserListItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = UserListItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'userId',
    'username',
  };
}

