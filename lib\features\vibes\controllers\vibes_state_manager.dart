import 'package:get/get.dart';

/// State manager for Vibes feature
///
/// Manages shared reactive state across all Vibes controllers.
/// This includes navigation state, UI state, and shared data that needs
/// to be accessible across multiple controllers within the feature.
class VibesStateManager extends GetxController {
  // Constants
  final String vibeId;

  // Reactive state
  final _isLoading = false.obs;
  final _hasError = false.obs;
  final _errorMessage = ''.obs;
  final _currentVibeIndex = 0.obs;
  final _isPlaying = false.obs;

  // Constructor
  VibesStateManager(this.vibeId);

  // Getters
  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  int get currentVibeIndex => _currentVibeIndex.value;
  bool get isPlaying => _isPlaying.value;

  // Setters
  set isLoading(bool value) => _isLoading.value = value;
  set hasError(bool value) => _hasError.value = value;
  set errorMessage(String value) => _errorMessage.value = value;
  set currentVibeIndex(int value) => _currentVibeIndex.value = value;
  set isPlaying(bool value) => _isPlaying.value = value;

  // Methods

  /// Clears any error state
  void clearError() {
    hasError = false;
    errorMessage = '';
  }

  /// Sets error state with message
  void setError(String message) {
    hasError = true;
    errorMessage = message;
    isLoading = false;
  }

  /// Sets loading state
  void setLoading(bool loading) {
    isLoading = loading;
    if (loading) {
      clearError();
    }
  }

  /// Toggles play/pause state
  void togglePlayState() {
    isPlaying = !isPlaying;
  }

  /// Sets play state
  void setPlayState(bool playing) {
    isPlaying = playing;
  }
}
