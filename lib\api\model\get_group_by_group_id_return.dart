//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetGroupByGroupIdReturn {
  /// Returns a new [GetGroupByGroupIdReturn] instance.
  GetGroupByGroupIdReturn({
    required this.groupId,
    required this.groupName,
    this.thumbnailUrl,
    this.members = const [],
    required this.memberCount,
  });

  /// Unique identifier of the group
  String groupId;

  /// Name of the group
  String groupName;

  /// URL to the group thumbnail image
  String? thumbnailUrl;

  /// List of group members with their roles and friendship status
  List<UserListItemWithGroupRole> members;

  /// Total number of group members
  ///
  /// Minimum value: 0
  int memberCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetGroupByGroupIdReturn &&
    other.groupId == groupId &&
    other.groupName == groupName &&
    other.thumbnailUrl == thumbnailUrl &&
    _deepEquality.equals(other.members, members) &&
    other.memberCount == memberCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (groupId.hashCode) +
    (groupName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (members.hashCode) +
    (memberCount.hashCode);

  @override
  String toString() => 'GetGroupByGroupIdReturn[groupId=$groupId, groupName=$groupName, thumbnailUrl=$thumbnailUrl, members=$members, memberCount=$memberCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'groupId'] = this.groupId;
      json[r'groupName'] = this.groupName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'members'] = this.members;
      json[r'memberCount'] = this.memberCount;
    return json;
  }

  /// Returns a new [GetGroupByGroupIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetGroupByGroupIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetGroupByGroupIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetGroupByGroupIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetGroupByGroupIdReturn(
        groupId: mapValueOfType<String>(json, r'groupId')!,
        groupName: mapValueOfType<String>(json, r'groupName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        members: UserListItemWithGroupRole.listFromJson(json[r'members']),
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
      );
    }
    return null;
  }

  static List<GetGroupByGroupIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetGroupByGroupIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetGroupByGroupIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetGroupByGroupIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetGroupByGroupIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetGroupByGroupIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetGroupByGroupIdReturn-objects as value to a dart map
  static Map<String, List<GetGroupByGroupIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetGroupByGroupIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetGroupByGroupIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'groupId',
    'groupName',
    'members',
    'memberCount',
  };
}

