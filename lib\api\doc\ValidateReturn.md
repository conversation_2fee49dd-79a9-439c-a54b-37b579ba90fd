# openapi.model.ValidateReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**token** | **String** | JWT authentication token for authenticated users | [optional] 
**userId** | **String** | Unique identifier of the authenticated user | [optional] 
**role** | [**UserRoleEnum**](UserRoleEnum.md) |  | [optional] 
**username** | **String** | Username of the authenticated user | [optional] 
**fullname** | **String** | Full name of the authenticated user | [optional] 
**avatarUrl** | **String** | URL to the user's avatar image | [optional] 
**type** | [**AuthEnum**](AuthEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



