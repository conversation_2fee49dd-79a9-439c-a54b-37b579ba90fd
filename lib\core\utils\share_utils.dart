import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import 'loading_overlay.dart';

/// Utility class for handling sharing functionality across the app
///
/// Provides methods for sharing ivent URLs and other content through
/// the native share dialog. Uses the share_plus package to handle
/// platform-specific sharing implementations.
class ShareUtils {
  ShareUtils._();

  /// Base URL template for ivent sharing
  /// This should match the deep link structure used by the app
  static const String _iventUrlTemplate = 'https://ivent.app/ivent/{iventId}';

  /// Shares an ivent URL using the native share dialog
  ///
  /// Creates a shareable URL for the given ivent and opens the native
  /// share panel where users can choose how to share the link.
  ///
  /// [iventId] - The unique identifier of the ivent to share
  /// [iventName] - Name of the ivent to include in share text
  /// [description] - Optional additional context for the share
  /// [thumbnailUrl] - Optional thumbnail URL to download and share
  /// [onLoadingStart] - Optional callback when thumbnail loading starts
  /// [onLoadingEnd] - Optional callback when thumbnail loading ends
  static Future<void> _shareIvent({
    required String iventId,
    required String iventName,
    String? description,
    String? thumbnailUrl,
    VoidCallback? onLoadingStart,
    VoidCallback? onLoadingEnd,
  }) async {
    try {
      final String iventUrl = _iventUrlTemplate.replaceAll('{iventId}', iventId);

      String shareText = iventUrl;

      if (iventName.isNotEmpty) {
        shareText = '$iventName\n\n$iventUrl';
      }

      if (description != null && description.trim().isNotEmpty) {
        const maxContextLength = 500;
        final truncatedContext = description.length > maxContextLength
            ? '${description.substring(0, maxContextLength).trim()}...'
            : description;
        shareText = '$shareText\n\n$truncatedContext';
      }

      var files = <XFile>[];
      if (thumbnailUrl != null && thumbnailUrl.isNotEmpty) {
        try {
          // Show loading indicator when starting thumbnail download
          onLoadingStart?.call();

          final tempDir = await getTemporaryDirectory();
          final filePath = '${tempDir.path}/ivent_share_$iventId.jpg';
          final response = await http.get(Uri.parse(thumbnailUrl));
          final file = File(filePath);
          await file.writeAsBytes(response.bodyBytes);
          files = [XFile(filePath)];

          // Hide loading indicator when thumbnail download is complete
          onLoadingEnd?.call();
        } catch (e) {
          print('Error loading thumbnail: $e');
          // Hide loading indicator even if there's an error
          onLoadingEnd?.call();
          // Continue without thumbnail if there's an error
        }
      }

      await SharePlus.instance.share(
        ShareParams(
          text: shareText,
          subject: iventName,
          files: files.isEmpty ? null : files,
        ),
      );
    } catch (e) {
      // Handle sharing errors gracefully
      print('Error sharing ivent: $e');
    }
  }

  /// Shares a custom text with optional subject
  ///
  /// [text] - The text content to share
  /// [subject] - Optional subject for the share
  static Future<void> shareText({
    required String text,
    String? subject,
  }) async {
    try {
      await SharePlus.instance.share(
        ShareParams(
          text: text,
          subject: subject,
        ),
      );
    } catch (e) {
      print('Error sharing text: $e');
    }
  }

  /// Generates an ivent URL for the given ivent ID
  ///
  /// [iventId] - The unique identifier of the ivent
  /// Returns the formatted URL string
  static String generateIventUrl(String iventId) {
    return _iventUrlTemplate.replaceAll('{iventId}', iventId);
  }

  /// Shares an ivent URL with automatic loading overlay
  ///
  /// This is a convenience method that automatically shows and hides
  /// a loading overlay while downloading the thumbnail.
  ///
  /// [context] - The build context for showing the loading overlay
  /// [iventId] - The unique identifier of the ivent to share
  /// [iventName] - Name of the ivent to include in share text
  /// [thumbnailUrl] - Optional thumbnail URL to download and share
  /// [description] - Optional additional context for the share
  static Future<void> shareIventWithLoading({
    required BuildContext context,
    required String iventId,
    required String iventName,
    String? thumbnailUrl,
    String? description,
  }) async {
    await _shareIvent(
      iventId: iventId,
      iventName: iventName,
      thumbnailUrl: thumbnailUrl,
      description: description,
      onLoadingStart: () => LoadingOverlay.show(context),
      onLoadingEnd: () => LoadingOverlay.hide(),
    );
  }
}
