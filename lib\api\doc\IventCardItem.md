# openapi.model.IventCardItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**iventId** | **String** | UUID of the ivent | 
**iventName** | **String** | Name of the ivent | 
**thumbnailUrl** | **String** | URL to the ivent thumbnail image | [optional] 
**locationName** | **String** | Name of the ivent location | 
**creatorId** | **String** | UUID of the ivent creator | 
**creatorType** | [**IventCreatorTypeEnum**](IventCreatorTypeEnum.md) |  | 
**creatorUsername** | **String** | Username of the ivent creator | 
**creatorImageUrl** | **String** | URL to the ivent creator image | [optional] 
**isFavorited** | **bool** | Whether the ivent is favorited by the current user | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



