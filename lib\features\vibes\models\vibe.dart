import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/features/vibes/models/video_vibe_item.dart';
import 'package:video_player/video_player.dart';

class Vibe {
  final VibeItem content;
  final String vibeFolderId;
  final VideoManager? videoManager;
  final VideoVibeItem? video;

  VideoPlayerController? get videoController => video?.controller;
  bool get isVideo => content.mediaFormat == MediaFormatEnum.video;

  Vibe({
    required this.content,
    required this.vibeFolderId,
    this.videoManager,
  })  : assert(videoManager != null || content.mediaFormat != MediaFormatEnum.video),
        video = content.mediaFormat == MediaFormatEnum.video
            ? VideoVibeItem(videoUrl: content.mediaUrl, vibeFolderId: vibeFolderId, videoManager: videoManager!)
            : null;

  @override
  String toString() {
    return 'Vibe{content: $content, vibeFolderId: $vibeFolderId, isVideo: $isVideo}';
  }
}
