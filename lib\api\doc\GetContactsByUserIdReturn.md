# openapi.model.GetContactsByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**contacts** | [**List<UserListItemWithPhoneNumber>**](UserListItemWithPhoneNumber.md) | List of user contacts with their phone numbers | [default to const []]
**contactCount** | **int** | Total number of contacts found | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



