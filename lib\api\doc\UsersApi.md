# openapi.api.UsersApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**deleteByUserId**](UsersApi.md#userscontrollerdeletebyuserid) | **DELETE** /users/{id}/delete | Hesap IDsi ile hesap silinir
[**followByUserId**](UsersApi.md#userscontrollerfollowbyuserid) | **POST** /users/{id}/follow | Hesap IDsi ile hesap takip edilir
[**getByUserId**](UsersApi.md#userscontrollergetbyuserid) | **GET** /users/{id} | Hesap IDsi ile hesap bilgileri listelenir
[**getContactsByUserId**](UsersApi.md#userscontrollergetcontactsbyuserid) | **POST** /users/{id}/contacts | Hesap IDsi ile rehber bağlantıları listelenir
[**getFavoritesByUserId**](UsersApi.md#userscontrollergetfavoritesbyuserid) | **GET** /users/{id}/favorites | Hesap IDsi ile hesabın favoriledikleri listelenir
[**getFollowerFriendsByUserId**](UsersApi.md#userscontrollergetfollowerfriendsbyuserid) | **GET** /users/{id}/followers/friends | Hesap IDsi ile takipçilerden arkadaş olunanları listeler
[**getFollowersByUserId**](UsersApi.md#userscontrollergetfollowersbyuserid) | **GET** /users/{id}/followers | Hesap IDsi ile takipçileri listeler
[**getFollowingsByUserId**](UsersApi.md#userscontrollergetfollowingsbyuserid) | **GET** /users/{id}/followings | Hesap IDsi ile hesabın takip ettikleri listelenir
[**getIventsByUserId**](UsersApi.md#userscontrollergetiventsbyuserid) | **GET** /users/{id}/ivents | Hesap IDsi ile hesabın katıldığı ya da oluşturduğu iventler listelenir
[**getLevelByUserId**](UsersApi.md#userscontrollergetlevelbyuserid) | **GET** /users/{id}/level | Hesap IDsi ile hesabın aşaması gösterilir
[**getMemoryFoldersByUserId**](UsersApi.md#userscontrollergetmemoryfoldersbyuserid) | **GET** /users/{id}/memories | Hesap IDsi ile hesabın memoriesleri listelenir
[**getPagesByUserId**](UsersApi.md#userscontrollergetpagesbyuserid) | **GET** /users/{id}/pages | Hesap IDsi ile hesaba ait sayfalar gösterilir
[**getUserBannerByUserId**](UsersApi.md#userscontrollergetuserbannerbyuserid) | **GET** /users/{id}/banner | Hesap IDsi ile hesabın avatar URL'si ve ismi listelenir
[**getVibeFoldersByUserId**](UsersApi.md#userscontrollergetvibefoldersbyuserid) | **GET** /users/{id}/vibes | Hesap IDsi ile hesabın vibeları listelenir
[**register**](UsersApi.md#userscontrollerregister) | **POST** /users/register | Hesaba giriş yapılır
[**removeFollowerByUserId**](UsersApi.md#userscontrollerremovefollowerbyuserid) | **POST** /users/{id}/followers/remove | Hesap IDsi ile hesabın takipçilerinden kaldırılır
[**sendCreatorRequestForm**](UsersApi.md#userscontrollersendcreatorrequestform) | **POST** /users/{id}/send-creator-request | Creator başvuru formu gönderilir
[**sendVerificationEmail**](UsersApi.md#userscontrollersendverificationemail) | **POST** /users/send-verification-email | Email doğrulaması için kod gönderilir
[**subscribeByUserId**](UsersApi.md#userscontrollersubscribebyuserid) | **POST** /users/{id}/subscribe | Hesap IDsi ile hesap bildirimleri açılır
[**unfollowByUserId**](UsersApi.md#userscontrollerunfollowbyuserid) | **POST** /users/{id}/unfollow | Hesap IDsi ile hesabın takibinden çıkılır
[**unsubscribeByUserId**](UsersApi.md#userscontrollerunsubscribebyuserid) | **POST** /users/{id}/unsubscribe | Hesap IDsi ile hesap bildirimleri kapatılır
[**updateByUserId**](UsersApi.md#userscontrollerupdatebyuserid) | **PUT** /users/{id}/update | Hesap IDsi ile hesabın detayları güncellenir
[**updateEmailByUserId**](UsersApi.md#userscontrollerupdateemailbyuserid) | **PUT** /users/{id}/update/email | Hesap IDsi ile emaili güncellenir
[**updateGradByUserId**](UsersApi.md#userscontrollerupdategradbyuserid) | **PUT** /users/{id}/update/grad | Hesap IDsi ile mezuniyet durumu güncellenir
[**updateNotificationsByUserId**](UsersApi.md#userscontrollerupdatenotificationsbyuserid) | **PUT** /users/{id}/update/notifications | Hesap IDsi ile bildirim ayarları güncellenir
[**updatePhoneNumberByUserId**](UsersApi.md#userscontrollerupdatephonenumberbyuserid) | **PUT** /users/{id}/update/phone-number | Hesap IDsi ile telefon numarası güncellenir
[**updatePrivacyByUserId**](UsersApi.md#userscontrollerupdateprivacybyuserid) | **PUT** /users/{id}/update/privacy | Hesap IDsi ile gizlilik ayarları güncellenir
[**validateEmail**](UsersApi.md#userscontrollervalidateemail) | **POST** /users/emailVerification | Email doğrulaması için gönderilen kod doğrulanır


# **deleteByUserId**
> deleteByUserId(id)

Hesap IDsi ile hesap silinir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.deleteByUserId(id);
} catch (e) {
    print('Exception when calling UsersApi->deleteByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **followByUserId**
> followByUserId(id)

Hesap IDsi ile hesap takip edilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.followByUserId(id);
} catch (e) {
    print('Exception when calling UsersApi->followByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getByUserId**
> GetUserByUserIdReturn getByUserId(id)

Hesap IDsi ile hesap bilgileri listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    final result = api_instance.getByUserId(id);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetUserByUserIdReturn**](GetUserByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getContactsByUserId**
> GetContactsByUserIdReturn getContactsByUserId(id, getContactsByUserIdDto, limit, page)

Hesap IDsi ile rehber bağlantıları listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final getContactsByUserIdDto = GetContactsByUserIdDto(); // GetContactsByUserIdDto | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getContactsByUserId(id, getContactsByUserIdDto, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getContactsByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **getContactsByUserIdDto** | [**GetContactsByUserIdDto**](GetContactsByUserIdDto.md)|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetContactsByUserIdReturn**](GetContactsByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getFavoritesByUserId**
> GetFavoritesByUserIdReturn getFavoritesByUserId(id, q, limit, page)

Hesap IDsi ile hesabın favoriledikleri listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getFavoritesByUserId(id, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getFavoritesByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetFavoritesByUserIdReturn**](GetFavoritesByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getFollowerFriendsByUserId**
> GetFollowerFriendsByUserIdReturn getFollowerFriendsByUserId(id, q, limit, page)

Hesap IDsi ile takipçilerden arkadaş olunanları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getFollowerFriendsByUserId(id, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getFollowerFriendsByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetFollowerFriendsByUserIdReturn**](GetFollowerFriendsByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getFollowersByUserId**
> GetFollowersByUserIdReturn getFollowersByUserId(id, q, limit, page)

Hesap IDsi ile takipçileri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getFollowersByUserId(id, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getFollowersByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetFollowersByUserIdReturn**](GetFollowersByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getFollowingsByUserId**
> GetFollowingsByUserIdReturn getFollowingsByUserId(id, q, limit, page)

Hesap IDsi ile hesabın takip ettikleri listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getFollowingsByUserId(id, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getFollowingsByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetFollowingsByUserIdReturn**](GetFollowingsByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getIventsByUserId**
> GetIventsByUserIdReturn getIventsByUserId(id, type, q, limit, page)

Hesap IDsi ile hesabın katıldığı ya da oluşturduğu iventler listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final type = ; // IventListingTypeEnum | Type of ivents to list - either joined or created by the user
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getIventsByUserId(id, type, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getIventsByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **type** | [**IventListingTypeEnum**](.md)| Type of ivents to list - either joined or created by the user | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetIventsByUserIdReturn**](GetIventsByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getLevelByUserId**
> GetLevelByUserIdReturn getLevelByUserId(id)

Hesap IDsi ile hesabın aşaması gösterilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    final result = api_instance.getLevelByUserId(id);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getLevelByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetLevelByUserIdReturn**](GetLevelByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getMemoryFoldersByUserId**
> GetMemoryFoldersByUserIdReturn getMemoryFoldersByUserId(id, limit, page)

Hesap IDsi ile hesabın memoriesleri listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getMemoryFoldersByUserId(id, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getMemoryFoldersByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetMemoryFoldersByUserIdReturn**](GetMemoryFoldersByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getPagesByUserId**
> GetPagesByUserIdReturn getPagesByUserId(id, limit, page)

Hesap IDsi ile hesaba ait sayfalar gösterilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getPagesByUserId(id, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getPagesByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetPagesByUserIdReturn**](GetPagesByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getUserBannerByUserId**
> GetUserBannerByUserIdReturn getUserBannerByUserId(id)

Hesap IDsi ile hesabın avatar URL'si ve ismi listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    final result = api_instance.getUserBannerByUserId(id);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getUserBannerByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetUserBannerByUserIdReturn**](GetUserBannerByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getVibeFoldersByUserId**
> GetVibeFoldersByUserIdReturn getVibeFoldersByUserId(id, limit, page)

Hesap IDsi ile hesabın vibeları listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getVibeFoldersByUserId(id, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->getVibeFoldersByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetVibeFoldersByUserIdReturn**](GetVibeFoldersByUserIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **register**
> RegisterReturn register(registerDto)

Hesaba giriş yapılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final registerDto = RegisterDto(); // RegisterDto | 

try {
    final result = api_instance.register(registerDto);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->register: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **registerDto** | [**RegisterDto**](RegisterDto.md)|  | 

### Return type

[**RegisterReturn**](RegisterReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeFollowerByUserId**
> removeFollowerByUserId(id, removeFollowerByUserIdDto)

Hesap IDsi ile hesabın takipçilerinden kaldırılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final removeFollowerByUserIdDto = RemoveFollowerByUserIdDto(); // RemoveFollowerByUserIdDto | 

try {
    api_instance.removeFollowerByUserId(id, removeFollowerByUserIdDto);
} catch (e) {
    print('Exception when calling UsersApi->removeFollowerByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **removeFollowerByUserIdDto** | [**RemoveFollowerByUserIdDto**](RemoveFollowerByUserIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **sendCreatorRequestForm**
> sendCreatorRequestForm(id)

Creator başvuru formu gönderilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.sendCreatorRequestForm(id);
} catch (e) {
    print('Exception when calling UsersApi->sendCreatorRequestForm: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **sendVerificationEmail**
> sendVerificationEmail(id)

Email doğrulaması için kod gönderilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.sendVerificationEmail(id);
} catch (e) {
    print('Exception when calling UsersApi->sendVerificationEmail: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **subscribeByUserId**
> subscribeByUserId(id)

Hesap IDsi ile hesap bildirimleri açılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.subscribeByUserId(id);
} catch (e) {
    print('Exception when calling UsersApi->subscribeByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unfollowByUserId**
> unfollowByUserId(id)

Hesap IDsi ile hesabın takibinden çıkılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.unfollowByUserId(id);
} catch (e) {
    print('Exception when calling UsersApi->unfollowByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unsubscribeByUserId**
> unsubscribeByUserId(id)

Hesap IDsi ile hesap bildirimleri kapatılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.unsubscribeByUserId(id);
} catch (e) {
    print('Exception when calling UsersApi->unsubscribeByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateByUserId**
> updateByUserId(id, updateByUserIdDto)

Hesap IDsi ile hesabın detayları güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final updateByUserIdDto = UpdateByUserIdDto(); // UpdateByUserIdDto | 

try {
    api_instance.updateByUserId(id, updateByUserIdDto);
} catch (e) {
    print('Exception when calling UsersApi->updateByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateByUserIdDto** | [**UpdateByUserIdDto**](UpdateByUserIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateEmailByUserId**
> updateEmailByUserId(id, updateEmailByUserIdDto)

Hesap IDsi ile emaili güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final updateEmailByUserIdDto = UpdateEmailByUserIdDto(); // UpdateEmailByUserIdDto | 

try {
    api_instance.updateEmailByUserId(id, updateEmailByUserIdDto);
} catch (e) {
    print('Exception when calling UsersApi->updateEmailByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateEmailByUserIdDto** | [**UpdateEmailByUserIdDto**](UpdateEmailByUserIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateGradByUserId**
> updateGradByUserId(id, updateGradByUserIdDto)

Hesap IDsi ile mezuniyet durumu güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final updateGradByUserIdDto = UpdateGradByUserIdDto(); // UpdateGradByUserIdDto | 

try {
    api_instance.updateGradByUserId(id, updateGradByUserIdDto);
} catch (e) {
    print('Exception when calling UsersApi->updateGradByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateGradByUserIdDto** | [**UpdateGradByUserIdDto**](UpdateGradByUserIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateNotificationsByUserId**
> updateNotificationsByUserId(id, body)

Hesap IDsi ile bildirim ayarları güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final body = Object(); // Object | 

try {
    api_instance.updateNotificationsByUserId(id, body);
} catch (e) {
    print('Exception when calling UsersApi->updateNotificationsByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **body** | **Object**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updatePhoneNumberByUserId**
> updatePhoneNumberByUserId(id, updatePhoneNumberByUserIdDto)

Hesap IDsi ile telefon numarası güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 
final updatePhoneNumberByUserIdDto = UpdatePhoneNumberByUserIdDto(); // UpdatePhoneNumberByUserIdDto | 

try {
    api_instance.updatePhoneNumberByUserId(id, updatePhoneNumberByUserIdDto);
} catch (e) {
    print('Exception when calling UsersApi->updatePhoneNumberByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updatePhoneNumberByUserIdDto** | [**UpdatePhoneNumberByUserIdDto**](UpdatePhoneNumberByUserIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updatePrivacyByUserId**
> updatePrivacyByUserId(id)

Hesap IDsi ile gizlilik ayarları güncellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.updatePrivacyByUserId(id);
} catch (e) {
    print('Exception when calling UsersApi->updatePrivacyByUserId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **validateEmail**
> validateEmail(id)

Email doğrulaması için gönderilen kod doğrulanır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final id = id_example; // String | 

try {
    api_instance.validateEmail(id);
} catch (e) {
    print('Exception when calling UsersApi->validateEmail: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


