import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/features/ivent_detail/constants/strings.dart';

/// A specialized button widget for contact and communication actions in ivent details.
///
/// This widget provides a consistent interface for various communication methods
/// such as WhatsApp, Instagram, phone calls, and external links. It follows the
/// project's design patterns and provides factory constructors for common use cases.
///
/// The button displays an icon and text with customizable colors and handles
/// tap interactions through the provided callback.
///
/// Usage:
/// ```dart
/// ContactButton.whatsappGroup(
///   onPressed: () => launchUrl(whatsappUrl),
/// )
/// ```
class ContactButton extends StatelessWidget {
  /// The text to display on the button
  final String text;
  
  /// The icon to display on the button
  final IconData icon;
  
  /// Callback function called when the button is pressed
  final VoidCallback onPressed;
  
  /// Background color of the button
  final Color color;
  
  /// Text and icon color (defaults to white if not specified)
  final Color? textColor;

  /// Creates a contact button with the specified properties.
  ///
  /// All parameters except [textColor] are required. The [textColor] defaults
  /// to white if not provided.
  const ContactButton({
    super.key,
    required this.text,
    required this.icon,
    required this.onPressed,
    required this.color,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: color,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(icon, color: textColor ?? AppColors.white),
            const SizedBox(width: 5),
            Text(
              text,
              style: TextStyle(
                color: textColor ?? AppColors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // ============================================================================
  // FACTORY CONSTRUCTORS
  // ============================================================================

  /// Creates a button for Google Forms registration.
  ///
  /// Displays a registration form button with appropriate styling for
  /// external form links.
  static ContactButton googleForms({
    required VoidCallback onPressed,
  }) {
    return ContactButton(
      text: IventDetailStrings.kayitFormu,
      icon: Icons.assignment,
      onPressed: onPressed,
      color: AppColors.grey500,
      textColor: AppColors.white,
    );
  }

  /// Creates a button for Instagram DM contact.
  ///
  /// Displays an Instagram direct message button with platform-appropriate
  /// styling and branding.
  static ContactButton instagram({
    required VoidCallback onPressed,
  }) {
    return ContactButton(
      text: IventDetailStrings.dMileUlas,
      icon: Icons.camera_alt,
      onPressed: onPressed,
      color: AppColors.black,
    );
  }

  /// Creates a button for WhatsApp group joining.
  ///
  /// Displays a WhatsApp group button with appropriate messaging and
  /// platform colors.
  static ContactButton whatsappGroup({
    required VoidCallback onPressed,
  }) {
    return ContactButton(
      text: IventDetailStrings.grubaKatil,
      icon: Icons.group,
      onPressed: onPressed,
      color: AppColors.grey500,
    );
  }

  /// Creates a button for WhatsApp direct messaging.
  ///
  /// Displays a WhatsApp direct message button for one-on-one communication.
  static ContactButton whatsappMessage({
    required VoidCallback onPressed,
  }) {
    return ContactButton(
      text: IventDetailStrings.mesajGonder,
      icon: Icons.message,
      onPressed: onPressed,
      color: AppColors.grey500,
    );
  }

  /// Creates a button for phone calls.
  ///
  /// Displays a call button with primary color styling to emphasize
  /// direct communication.
  static ContactButton phoneCall({
    required VoidCallback onPressed,
  }) {
    return ContactButton(
      text: IventDetailStrings.sesliArama,
      icon: Icons.phone,
      onPressed: onPressed,
      color: AppColors.primary,
    );
  }

  /// Creates a button for external website links.
  ///
  /// Displays a website link button with primary color styling for
  /// external navigation.
  static ContactButton website({
    required VoidCallback onPressed,
  }) {
    return ContactButton(
      text: IventDetailStrings.linkeGit,
      icon: Icons.link,
      onPressed: onPressed,
      color: AppColors.primary,
    );
  }
}
