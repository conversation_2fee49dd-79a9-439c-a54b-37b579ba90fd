//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Authentication result type
class AuthEnum {
  /// Instantiate a new enum with the provided [value].
  const AuthEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const login = AuthEnum._(r'login');
  static const register = AuthEnum._(r'register');

  /// List of all possible values in this [enum][AuthEnum].
  static const values = <AuthEnum>[
    login,
    register,
  ];

  static AuthEnum? fromJson(dynamic value) => AuthEnumTypeTransformer().decode(value);

  static List<AuthEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <AuthEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = AuthEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [AuthEnum] to String,
/// and [decode] dynamic data back to [AuthEnum].
class AuthEnumTypeTransformer {
  factory AuthEnumTypeTransformer() => _instance ??= const AuthEnumTypeTransformer._();

  const AuthEnumTypeTransformer._();

  String encode(AuthEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a AuthEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  AuthEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'login': return AuthEnum.login;
        case r'register': return AuthEnum.register;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [AuthEnumTypeTransformer] instance.
  static AuthEnumTypeTransformer? _instance;
}

