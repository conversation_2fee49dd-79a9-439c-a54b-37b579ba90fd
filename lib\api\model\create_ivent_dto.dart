//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateIventDto {
  /// Returns a new [CreateIventDto] instance.
  CreateIventDto({
    required this.creatorType,
    required this.iventName,
    this.thumbnailUrl,
    this.thumbnailBuffer,
    this.dates = const [],
    required this.mapboxId,
    required this.latitude,
    required this.longitude,
    this.description,
    required this.categoryTagId,
    this.tagIds = const [],
    required this.privacy,
    this.allowedUniversityCodes = const [],
    this.collabs = const [],
    this.googleFormsUrl,
    this.instagramUsername,
    this.whatsappUrl,
    this.isWhatsappUrlPrivate,
    this.whatsappNumber,
    this.callNumber,
    this.websiteUrl,
  });

  AccountTypeEnum creatorType;

  /// Ivent name can only contain letters, numbers, underscores, and hyphens
  String iventName;

  /// URL to the ivent thumbnail image
  String? thumbnailUrl;

  /// Base64 encoded thumbnail image buffer
  String? thumbnailBuffer;

  /// Array of date strings in ISO 8601 date-time format
  List<String> dates;

  /// Mapbox place ID for location
  String mapboxId;

  /// Latitude coordinate of the ivent location
  double latitude;

  /// Longitude coordinate of the ivent location
  double longitude;

  /// Detailed description of the ivent
  String? description;

  /// UUID of the category tag for this ivent
  String categoryTagId;

  /// Array of hobby tag UUIDs associated with this ivent
  List<String> tagIds;

  IventPrivacyEnum privacy;

  /// Array of university codes that are allowed to join this ivent
  List<String> allowedUniversityCodes;

  /// Array of collaborators for this ivent
  List<CollabDto> collabs;

  /// URL to Google Forms for registration
  String? googleFormsUrl;

  /// Instagram username for the ivent
  String? instagramUsername;

  /// WhatsApp group URL
  String? whatsappUrl;

  /// Whether the WhatsApp URL should be kept private
  bool? isWhatsappUrlPrivate;

  /// WhatsApp contact number
  String? whatsappNumber;

  /// Phone number for calls
  String? callNumber;

  /// Website URL for the ivent
  String? websiteUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateIventDto &&
    other.creatorType == creatorType &&
    other.iventName == iventName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.thumbnailBuffer == thumbnailBuffer &&
    _deepEquality.equals(other.dates, dates) &&
    other.mapboxId == mapboxId &&
    other.latitude == latitude &&
    other.longitude == longitude &&
    other.description == description &&
    other.categoryTagId == categoryTagId &&
    _deepEquality.equals(other.tagIds, tagIds) &&
    other.privacy == privacy &&
    _deepEquality.equals(other.allowedUniversityCodes, allowedUniversityCodes) &&
    _deepEquality.equals(other.collabs, collabs) &&
    other.googleFormsUrl == googleFormsUrl &&
    other.instagramUsername == instagramUsername &&
    other.whatsappUrl == whatsappUrl &&
    other.isWhatsappUrlPrivate == isWhatsappUrlPrivate &&
    other.whatsappNumber == whatsappNumber &&
    other.callNumber == callNumber &&
    other.websiteUrl == websiteUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (creatorType.hashCode) +
    (iventName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (thumbnailBuffer == null ? 0 : thumbnailBuffer!.hashCode) +
    (dates.hashCode) +
    (mapboxId.hashCode) +
    (latitude.hashCode) +
    (longitude.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (categoryTagId.hashCode) +
    (tagIds.hashCode) +
    (privacy.hashCode) +
    (allowedUniversityCodes.hashCode) +
    (collabs.hashCode) +
    (googleFormsUrl == null ? 0 : googleFormsUrl!.hashCode) +
    (instagramUsername == null ? 0 : instagramUsername!.hashCode) +
    (whatsappUrl == null ? 0 : whatsappUrl!.hashCode) +
    (isWhatsappUrlPrivate == null ? 0 : isWhatsappUrlPrivate!.hashCode) +
    (whatsappNumber == null ? 0 : whatsappNumber!.hashCode) +
    (callNumber == null ? 0 : callNumber!.hashCode) +
    (websiteUrl == null ? 0 : websiteUrl!.hashCode);

  @override
  String toString() => 'CreateIventDto[creatorType=$creatorType, iventName=$iventName, thumbnailUrl=$thumbnailUrl, thumbnailBuffer=$thumbnailBuffer, dates=$dates, mapboxId=$mapboxId, latitude=$latitude, longitude=$longitude, description=$description, categoryTagId=$categoryTagId, tagIds=$tagIds, privacy=$privacy, allowedUniversityCodes=$allowedUniversityCodes, collabs=$collabs, googleFormsUrl=$googleFormsUrl, instagramUsername=$instagramUsername, whatsappUrl=$whatsappUrl, isWhatsappUrlPrivate=$isWhatsappUrlPrivate, whatsappNumber=$whatsappNumber, callNumber=$callNumber, websiteUrl=$websiteUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'creatorType'] = this.creatorType;
      json[r'iventName'] = this.iventName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
    if (this.thumbnailBuffer != null) {
      json[r'thumbnailBuffer'] = this.thumbnailBuffer;
    } else {
      json[r'thumbnailBuffer'] = null;
    }
      json[r'dates'] = this.dates;
      json[r'mapboxId'] = this.mapboxId;
      json[r'latitude'] = this.latitude;
      json[r'longitude'] = this.longitude;
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'categoryTagId'] = this.categoryTagId;
      json[r'tagIds'] = this.tagIds;
      json[r'privacy'] = this.privacy;
      json[r'allowedUniversityCodes'] = this.allowedUniversityCodes;
      json[r'collabs'] = this.collabs;
    if (this.googleFormsUrl != null) {
      json[r'googleFormsUrl'] = this.googleFormsUrl;
    } else {
      json[r'googleFormsUrl'] = null;
    }
    if (this.instagramUsername != null) {
      json[r'instagramUsername'] = this.instagramUsername;
    } else {
      json[r'instagramUsername'] = null;
    }
    if (this.whatsappUrl != null) {
      json[r'whatsappUrl'] = this.whatsappUrl;
    } else {
      json[r'whatsappUrl'] = null;
    }
    if (this.isWhatsappUrlPrivate != null) {
      json[r'isWhatsappUrlPrivate'] = this.isWhatsappUrlPrivate;
    } else {
      json[r'isWhatsappUrlPrivate'] = null;
    }
    if (this.whatsappNumber != null) {
      json[r'whatsappNumber'] = this.whatsappNumber;
    } else {
      json[r'whatsappNumber'] = null;
    }
    if (this.callNumber != null) {
      json[r'callNumber'] = this.callNumber;
    } else {
      json[r'callNumber'] = null;
    }
    if (this.websiteUrl != null) {
      json[r'websiteUrl'] = this.websiteUrl;
    } else {
      json[r'websiteUrl'] = null;
    }
    return json;
  }

  /// Returns a new [CreateIventDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateIventDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateIventDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateIventDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateIventDto(
        creatorType: AccountTypeEnum.fromJson(json[r'creatorType'])!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        thumbnailBuffer: mapValueOfType<String>(json, r'thumbnailBuffer'),
        dates: json[r'dates'] is Iterable
            ? (json[r'dates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        mapboxId: mapValueOfType<String>(json, r'mapboxId')!,
        latitude: mapValueOfType<double>(json, r'latitude')!,
        longitude: mapValueOfType<double>(json, r'longitude')!,
        description: mapValueOfType<String>(json, r'description'),
        categoryTagId: mapValueOfType<String>(json, r'categoryTagId')!,
        tagIds: json[r'tagIds'] is Iterable
            ? (json[r'tagIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        privacy: IventPrivacyEnum.fromJson(json[r'privacy'])!,
        allowedUniversityCodes: json[r'allowedUniversityCodes'] is Iterable
            ? (json[r'allowedUniversityCodes'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        collabs: CollabDto.listFromJson(json[r'collabs']),
        googleFormsUrl: mapValueOfType<String>(json, r'googleFormsUrl'),
        instagramUsername: mapValueOfType<String>(json, r'instagramUsername'),
        whatsappUrl: mapValueOfType<String>(json, r'whatsappUrl'),
        isWhatsappUrlPrivate: mapValueOfType<bool>(json, r'isWhatsappUrlPrivate'),
        whatsappNumber: mapValueOfType<String>(json, r'whatsappNumber'),
        callNumber: mapValueOfType<String>(json, r'callNumber'),
        websiteUrl: mapValueOfType<String>(json, r'websiteUrl'),
      );
    }
    return null;
  }

  static List<CreateIventDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateIventDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateIventDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateIventDto> mapFromJson(dynamic json) {
    final map = <String, CreateIventDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateIventDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateIventDto-objects as value to a dart map
  static Map<String, List<CreateIventDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateIventDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateIventDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'creatorType',
    'iventName',
    'dates',
    'mapboxId',
    'latitude',
    'longitude',
    'categoryTagId',
    'tagIds',
    'privacy',
    'allowedUniversityCodes',
    'collabs',
  };
}

