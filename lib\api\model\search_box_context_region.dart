//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxContextRegion {
  /// Returns a new [SearchBoxContextRegion] instance.
  SearchBoxContextRegion({
    this.id,
    this.name,
    this.regionCode,
    this.regionCodeFull,
  });

  /// Region ID
  String? id;

  /// Region name
  String? name;

  /// Region code
  String? regionCode;

  /// Full region code
  String? regionCodeFull;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxContextRegion &&
    other.id == id &&
    other.name == name &&
    other.regionCode == regionCode &&
    other.regionCodeFull == regionCodeFull;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id == null ? 0 : id!.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (regionCode == null ? 0 : regionCode!.hashCode) +
    (regionCodeFull == null ? 0 : regionCodeFull!.hashCode);

  @override
  String toString() => 'SearchBoxContextRegion[id=$id, name=$name, regionCode=$regionCode, regionCodeFull=$regionCodeFull]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.id != null) {
      json[r'id'] = this.id;
    } else {
      json[r'id'] = null;
    }
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    if (this.regionCode != null) {
      json[r'region_code'] = this.regionCode;
    } else {
      json[r'region_code'] = null;
    }
    if (this.regionCodeFull != null) {
      json[r'region_code_full'] = this.regionCodeFull;
    } else {
      json[r'region_code_full'] = null;
    }
    return json;
  }

  /// Returns a new [SearchBoxContextRegion] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxContextRegion? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxContextRegion[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxContextRegion[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxContextRegion(
        id: mapValueOfType<String>(json, r'id'),
        name: mapValueOfType<String>(json, r'name'),
        regionCode: mapValueOfType<String>(json, r'region_code'),
        regionCodeFull: mapValueOfType<String>(json, r'region_code_full'),
      );
    }
    return null;
  }

  static List<SearchBoxContextRegion> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxContextRegion>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxContextRegion.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxContextRegion> mapFromJson(dynamic json) {
    final map = <String, SearchBoxContextRegion>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxContextRegion.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxContextRegion-objects as value to a dart map
  static Map<String, List<SearchBoxContextRegion>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxContextRegion>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxContextRegion.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

