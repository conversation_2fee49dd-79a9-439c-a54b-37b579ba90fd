import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class VibeButtons {
  VibeButtons._();

  static IaRoundedButton _vibes({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isSecondary,
    String? text,
    String? leadingIconPath,
    String? trailingIconPath,
    bool? expand,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightVibes,
      roundness: AppDimensions.radiusXL,
      onTap: onTap,
      color: isSecondary ? AppColors.secondary : AppColors.grey800,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium,
      leading: leadingIconPath != null ? IaSvgIcon(iconPath: leadingIconPath) : null,
      trailing: trailingIconPath != null ? IaSvgIcon(iconPath: trailingIconPath) : null,
      expand: expand ?? true,
    );
  }

  static IaRoundedButton vibesDownload({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isDownloaded,
  }) {
    return _vibes(
      key: key,
      margin: margin,
      onTap: onTap,
      isSecondary: false,
      leadingIconPath: isDownloaded ? AppAssets.check : AppAssets.download,
      expand: false,
    );
  }

  static IaRoundedButton vibesAddMemories({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isMemoriesAdded,
  }) {
    return _vibes(
      key: key,
      margin: margin,
      onTap: onTap,
      isSecondary: false,
      text: isMemoriesAdded ? 'Memories Eklendi!' : 'Memories Ekle',
      leadingIconPath: isMemoriesAdded ? null : AppAssets.film,
    );
  }

  static IaRoundedButton vibesShare({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return _vibes(
      key: key,
      margin: margin,
      onTap: onTap,
      isSecondary: true,
      text: 'Vibes Paylaş',
      trailingIconPath: AppAssets.caretCircleRight,
    );
  }

  static IaBlurredButton privacyVibe({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required String text,
    required String iconPath,
  }) {
    return IaBlurredButton(
      key: key,
      margin: margin,
      height: AppDimensions.buttonHeightPrivacyVibe,
      roundness: AppDimensions.radiusXS,
      onTap: onTap,
      color: AppColors.grey400.withValues(alpha: 0.5),
      text: text,
      textStyle: AppTextStyles.buttonTextMedium,
      leading: IaSvgIcon(iconPath: iconPath),
      trailing: const IaSvgIcon(iconPath: AppAssets.caretDownSM),
    );
  }

  static IaRoundedButton addMemory({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      height: AppDimensions.buttonHeightAddMemory,
      roundness: AppDimensions.radiusXL,
      onTap: onTap,
      color: AppColors.white,
      text: 'Memories Ekle',
      textStyle: AppTextStyles.buttonTextMedium.copyWith(color: AppColors.primary),
      leading: const IaSvgIcon(iconPath: AppAssets.casette, iconColor: AppColors.primary),
    );
  }

  static IaCircularButton memoriesCheck({
    Key? key,
    VoidCallback? onTap,
    required bool isSelected,
  }) {
    return IaCircularButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      backgroundColor: isSelected ? AppColors.primary : AppColors.transparent,
      iconPath: isSelected ? AppAssets.check : null,
      iconSize: AppDimensions.defaultIconButtonSize * 2 / 3,
      iconColor: AppColors.white,
      border: isSelected ? null : Border.all(color: AppColors.white, width: 2),
    );
  }

  static IaIconButton like({
    Key? key,
    VoidCallback? onTap,
    required bool isLiked,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      iconPath: AppAssets.heart01,
      iconColor: isLiked ? AppColors.heartRed : AppColors.white,
      iconSize: 30,
    );
  }

  static IaIconButton comment({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      iconPath: AppAssets.chat,
      iconSize: 30,
    );
  }

  static IaIconButton send({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      iconPath: AppAssets.paperPlane,
      iconSize: 30,
    );
  }
}
