# openapi.api.IventsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createIvent**](IventsApi.md#iventscontrollercreateivent) | **POST** /ivents/create | Ivent oluşturur
[**deleteIventByIventId**](IventsApi.md#iventscontrollerdeleteiventbyiventid) | **DELETE** /ivents/{id}/delete | Ivent IDsi ile iventi siler
[**favoriteIventByIventId**](IventsApi.md#iventscontrollerfavoriteiventbyiventid) | **POST** /ivents/{id}/favorite | Ivent IDsi ile iventi favorilere ekler
[**getBannerByIventId**](IventsApi.md#iventscontrollergetbannerbyiventid) | **POST** /ivents/banner | Ivent IDsi ile iventin küçük kart bilgilerini listeler
[**getIventPageByIventId**](IventsApi.md#iventscontrollergetiventpagebyiventid) | **GET** /ivents/{id} | Ivent IDsi ile iventin sayfa bilgilerini listeler
[**getLatestIvents**](IventsApi.md#iventscontrollergetlatestivents) | **GET** /ivents/latest | Kullanıcının katıldığı son iventleri listeler
[**getSuggestedImages**](IventsApi.md#iventscontrollergetsuggestedimages) | **GET** /ivents/suggestedImages | Ivent oluşturulurken resim önerilerini listeler
[**getUpcomingIvent**](IventsApi.md#iventscontrollergetupcomingivent) | **POST** /ivents/upcoming | Kullanıcının katılacağı en yakın iventi listeler
[**unfavoriteIventByIventId**](IventsApi.md#iventscontrollerunfavoriteiventbyiventid) | **POST** /ivents/{id}/unfavorite | Ivent IDsi ile iventi favorilerden çıkarır
[**updateDateByIventId**](IventsApi.md#iventscontrollerupdatedatebyiventid) | **PUT** /ivents/{id}/update/date | Ivent IDsi ile iventin tarih bilgisini günceller
[**updateDetailsByIventId**](IventsApi.md#iventscontrollerupdatedetailsbyiventid) | **PUT** /ivents/{id}/update/details | Ivent IDsi ile iventin detaylarını günceller
[**updateLocationByIventId**](IventsApi.md#iventscontrollerupdatelocationbyiventid) | **PUT** /ivents/{id}/update/location | Ivent IDsi ile iventin konumunu günceller


# **createIvent**
> CreateIventReturn createIvent(createIventDto)

Ivent oluşturur

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final createIventDto = CreateIventDto(); // CreateIventDto | 

try {
    final result = api_instance.createIvent(createIventDto);
    print(result);
} catch (e) {
    print('Exception when calling IventsApi->createIvent: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createIventDto** | [**CreateIventDto**](CreateIventDto.md)|  | 

### Return type

[**CreateIventReturn**](CreateIventReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteIventByIventId**
> deleteIventByIventId(id)

Ivent IDsi ile iventi siler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 

try {
    api_instance.deleteIventByIventId(id);
} catch (e) {
    print('Exception when calling IventsApi->deleteIventByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **favoriteIventByIventId**
> favoriteIventByIventId(id)

Ivent IDsi ile iventi favorilere ekler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 

try {
    api_instance.favoriteIventByIventId(id);
} catch (e) {
    print('Exception when calling IventsApi->favoriteIventByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getBannerByIventId**
> GetBannerByIventIdReturn getBannerByIventId(getBannerByIventIdDto)

Ivent IDsi ile iventin küçük kart bilgilerini listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final getBannerByIventIdDto = GetBannerByIventIdDto(); // GetBannerByIventIdDto | 

try {
    final result = api_instance.getBannerByIventId(getBannerByIventIdDto);
    print(result);
} catch (e) {
    print('Exception when calling IventsApi->getBannerByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **getBannerByIventIdDto** | [**GetBannerByIventIdDto**](GetBannerByIventIdDto.md)|  | 

### Return type

[**GetBannerByIventIdReturn**](GetBannerByIventIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getIventPageByIventId**
> GetIventPageByIventIdReturn getIventPageByIventId(id)

Ivent IDsi ile iventin sayfa bilgilerini listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 

try {
    final result = api_instance.getIventPageByIventId(id);
    print(result);
} catch (e) {
    print('Exception when calling IventsApi->getIventPageByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetIventPageByIventIdReturn**](GetIventPageByIventIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getLatestIvents**
> GetLatestIventsReturn getLatestIvents(q, limit, page)

Kullanıcının katıldığı son iventleri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getLatestIvents(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling IventsApi->getLatestIvents: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetLatestIventsReturn**](GetLatestIventsReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getSuggestedImages**
> GetSuggestedImagesReturn getSuggestedImages(criterias)

Ivent oluşturulurken resim önerilerini listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final criterias = criterias_example; // String | 

try {
    final result = api_instance.getSuggestedImages(criterias);
    print(result);
} catch (e) {
    print('Exception when calling IventsApi->getSuggestedImages: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **criterias** | **String**|  | 

### Return type

[**GetSuggestedImagesReturn**](GetSuggestedImagesReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getUpcomingIvent**
> getUpcomingIvent()

Kullanıcının katılacağı en yakın iventi listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();

try {
    api_instance.getUpcomingIvent();
} catch (e) {
    print('Exception when calling IventsApi->getUpcomingIvent: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unfavoriteIventByIventId**
> unfavoriteIventByIventId(id)

Ivent IDsi ile iventi favorilerden çıkarır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 

try {
    api_instance.unfavoriteIventByIventId(id);
} catch (e) {
    print('Exception when calling IventsApi->unfavoriteIventByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateDateByIventId**
> updateDateByIventId(id, updateDateByIventIdDto)

Ivent IDsi ile iventin tarih bilgisini günceller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 
final updateDateByIventIdDto = UpdateDateByIventIdDto(); // UpdateDateByIventIdDto | 

try {
    api_instance.updateDateByIventId(id, updateDateByIventIdDto);
} catch (e) {
    print('Exception when calling IventsApi->updateDateByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateDateByIventIdDto** | [**UpdateDateByIventIdDto**](UpdateDateByIventIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateDetailsByIventId**
> updateDetailsByIventId(id, updateDetailsByIventIdDto)

Ivent IDsi ile iventin detaylarını günceller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 
final updateDetailsByIventIdDto = UpdateDetailsByIventIdDto(); // UpdateDetailsByIventIdDto | 

try {
    api_instance.updateDetailsByIventId(id, updateDetailsByIventIdDto);
} catch (e) {
    print('Exception when calling IventsApi->updateDetailsByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateDetailsByIventIdDto** | [**UpdateDetailsByIventIdDto**](UpdateDetailsByIventIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateLocationByIventId**
> updateLocationByIventId(id, updateLocationByIventIdDto)

Ivent IDsi ile iventin konumunu günceller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventsApi();
final id = id_example; // String | 
final updateLocationByIventIdDto = UpdateLocationByIventIdDto(); // UpdateLocationByIventIdDto | 

try {
    api_instance.updateLocationByIventId(id, updateLocationByIventIdDto);
} catch (e) {
    print('Exception when calling IventsApi->updateLocationByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateLocationByIventIdDto** | [**UpdateLocationByIventIdDto**](UpdateLocationByIventIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


