import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/profile.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class IventsController extends BaseControllerWithSharedState<ProfileStateManager> {
  IventsController(AuthService authService, ProfileStateManager state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarControllerJoined;
  late final BaseSearchBarController baseSearchBarControllerCreated;

  final _createdIventsResult = Rxn<GetIventsByUserIdReturn>();
  final _joinedIventsResult = Rxn<GetIventsByUserIdReturn>();

  GetIventsByUserIdReturn? get createdIventsResult => _createdIventsResult.value;
  GetIventsByUserIdReturn? get joinedIventsResult => _joinedIventsResult.value;

  TextEditingController get textEditingControllerCreated => baseSearchBarControllerCreated.textEditingController;
  String get searchTextCreated => baseSearchBarControllerCreated.text;
  bool get isSearchingCreated => baseSearchBarControllerCreated.isSearching;
  bool get isQueryEmptyCreated => searchTextCreated.isEmpty;
  bool get isResultsEmptyCreated => createdIventsResult?.ivents.isEmpty ?? true;

  TextEditingController get textEditingControllerJoined => baseSearchBarControllerJoined.textEditingController;
  String get searchTextJoined => baseSearchBarControllerJoined.text;
  bool get isSearchingJoined => baseSearchBarControllerJoined.isSearching;
  bool get isQueryEmptyJoined => searchTextJoined.isEmpty;
  bool get isResultsEmptyJoined => joinedIventsResult?.ivents.isEmpty ?? true;

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarControllerCreated =
        Get.put(BaseSearchBarController((q) => _searchCreatedIvents(q: q)), tag: 'IventsControllerCreated');
    baseSearchBarControllerJoined =
        Get.put(BaseSearchBarController((q) => _searchJoinedIvents(q: q)), tag: 'IventsControllerJoined');
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'IventsControllerCreated');
    Get.delete<BaseSearchBarController>(tag: 'IventsControllerJoined');
    super.closeController();
  }

  Future<void> _searchCreatedIvents({String? q}) async {
    _createdIventsResult.value = await authService.usersApi.getIventsByUserId(
      state.userId,
      IventListingTypeEnum.created,
      q: q,
    );
  }

  Future<void> _searchJoinedIvents({String? q}) async {
    _joinedIventsResult.value = await authService.usersApi.getIventsByUserId(
     state.userId,
      IventListingTypeEnum.joined,
      q: q,
    );
  }

  Future<void> goToIventsPage() async {
    if (state.userRole == UserTypeEnum.CREATOR) {
      Get.toNamed(ProfileRoutes.PROFILE_PAGE_CREATOR_IVENTLER, arguments: state.userId);
      if (joinedIventsResult == null) await _searchJoinedIvents();
      if (createdIventsResult == null) await _searchCreatedIvents();
    } else {
      Get.toNamed(ProfileRoutes.PROFILE_PAGE_IVENTLER, arguments: state.userId);
      if (joinedIventsResult == null) await _searchJoinedIvents();
    }
  }
}
