//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Status of the group membership of the user
class GroupMembershipStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const GroupMembershipStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const admin = GroupMembershipStatusEnum._(r'admin');
  static const moderator = GroupMembershipStatusEnum._(r'moderator');
  static const pending = GroupMembershipStatusEnum._(r'pending');
  static const accepted = GroupMembershipStatusEnum._(r'accepted');

  /// List of all possible values in this [enum][GroupMembershipStatusEnum].
  static const values = <GroupMembershipStatusEnum>[
    admin,
    moderator,
    pending,
    accepted,
  ];

  static GroupMembershipStatusEnum? fromJson(dynamic value) => GroupMembershipStatusEnumTypeTransformer().decode(value);

  static List<GroupMembershipStatusEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GroupMembershipStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GroupMembershipStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [GroupMembershipStatusEnum] to String,
/// and [decode] dynamic data back to [GroupMembershipStatusEnum].
class GroupMembershipStatusEnumTypeTransformer {
  factory GroupMembershipStatusEnumTypeTransformer() => _instance ??= const GroupMembershipStatusEnumTypeTransformer._();

  const GroupMembershipStatusEnumTypeTransformer._();

  String encode(GroupMembershipStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a GroupMembershipStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  GroupMembershipStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'admin': return GroupMembershipStatusEnum.admin;
        case r'moderator': return GroupMembershipStatusEnum.moderator;
        case r'pending': return GroupMembershipStatusEnum.pending;
        case r'accepted': return GroupMembershipStatusEnum.accepted;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [GroupMembershipStatusEnumTypeTransformer] instance.
  static GroupMembershipStatusEnumTypeTransformer? _instance;
}

