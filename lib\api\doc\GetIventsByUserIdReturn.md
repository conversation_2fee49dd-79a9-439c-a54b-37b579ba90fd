# openapi.model.GetIventsByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ivents** | [**List<IventListItem>**](IventListItem.md) | List of ivents created by the user | [default to const []]
**iventCount** | **int** | Total number of ivents created by the user | 
**isFirst<PERSON>erson** | **bool** | Whether this is the current user's own profile | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



