# openapi.model.GetCommentsByVibeIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**comments** | [**List<CommentItem>**](CommentItem.md) | List of comments on the vibe | [default to const []]
**commentCount** | **int** | Total number of comments on the vibe | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



