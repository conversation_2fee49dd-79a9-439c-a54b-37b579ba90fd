//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class IventsApi {
  IventsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Ivent oluşturur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreateIventDto] createIventDto (required):
  Future<Response> createIventWithHttpInfo(CreateIventDto createIventDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/create';

    // ignore: prefer_final_locals
    Object? postBody = createIventDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent oluşturur
  ///
  /// Parameters:
  ///
  /// * [CreateIventDto] createIventDto (required):
  Future<CreateIventReturn?> createIvent(CreateIventDto createIventDto,) async {
    final response = await createIventWithHttpInfo(createIventDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateIventReturn',) as CreateIventReturn;
    
    }
    return null;
  }

  /// Ivent IDsi ile iventi siler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deleteIventByIventIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventi siler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deleteIventByIventId(String id,) async {
    final response = await deleteIventByIventIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile iventi favorilere ekler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> favoriteIventByIventIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}/favorite'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventi favorilere ekler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> favoriteIventByIventId(String id,) async {
    final response = await favoriteIventByIventIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile iventin küçük kart bilgilerini listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [GetBannerByIventIdDto] getBannerByIventIdDto (required):
  Future<Response> getBannerByIventIdWithHttpInfo(GetBannerByIventIdDto getBannerByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/banner';

    // ignore: prefer_final_locals
    Object? postBody = getBannerByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventin küçük kart bilgilerini listeler
  ///
  /// Parameters:
  ///
  /// * [GetBannerByIventIdDto] getBannerByIventIdDto (required):
  Future<GetBannerByIventIdReturn?> getBannerByIventId(GetBannerByIventIdDto getBannerByIventIdDto,) async {
    final response = await getBannerByIventIdWithHttpInfo(getBannerByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetBannerByIventIdReturn',) as GetBannerByIventIdReturn;
    
    }
    return null;
  }

  /// Ivent IDsi ile iventin sayfa bilgilerini listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getIventPageByIventIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventin sayfa bilgilerini listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetIventPageByIventIdReturn?> getIventPageByIventId(String id,) async {
    final response = await getIventPageByIventIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetIventPageByIventIdReturn',) as GetIventPageByIventIdReturn;
    
    }
    return null;
  }

  /// Kullanıcının katıldığı son iventleri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getLatestIventsWithHttpInfo({ String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/latest';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Kullanıcının katıldığı son iventleri listeler
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetLatestIventsReturn?> getLatestIvents({ String? q, int? limit, int? page, }) async {
    final response = await getLatestIventsWithHttpInfo( q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetLatestIventsReturn',) as GetLatestIventsReturn;
    
    }
    return null;
  }

  /// Ivent oluşturulurken resim önerilerini listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] criterias (required):
  Future<Response> getSuggestedImagesWithHttpInfo(String criterias,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/suggestedImages';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'criterias', criterias));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent oluşturulurken resim önerilerini listeler
  ///
  /// Parameters:
  ///
  /// * [String] criterias (required):
  Future<GetSuggestedImagesReturn?> getSuggestedImages(String criterias,) async {
    final response = await getSuggestedImagesWithHttpInfo(criterias,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetSuggestedImagesReturn',) as GetSuggestedImagesReturn;
    
    }
    return null;
  }

  /// Kullanıcının katılacağı en yakın iventi listeler
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> getUpcomingIventWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/upcoming';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Kullanıcının katılacağı en yakın iventi listeler
  Future<void> getUpcomingIvent() async {
    final response = await getUpcomingIventWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile iventi favorilerden çıkarır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unfavoriteIventByIventIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}/unfavorite'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventi favorilerden çıkarır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unfavoriteIventByIventId(String id,) async {
    final response = await unfavoriteIventByIventIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile iventin tarih bilgisini günceller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateDateByIventIdDto] updateDateByIventIdDto (required):
  Future<Response> updateDateByIventIdWithHttpInfo(String id, UpdateDateByIventIdDto updateDateByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}/update/date'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateDateByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventin tarih bilgisini günceller
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateDateByIventIdDto] updateDateByIventIdDto (required):
  Future<void> updateDateByIventId(String id, UpdateDateByIventIdDto updateDateByIventIdDto,) async {
    final response = await updateDateByIventIdWithHttpInfo(id, updateDateByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile iventin detaylarını günceller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateDetailsByIventIdDto] updateDetailsByIventIdDto (required):
  Future<Response> updateDetailsByIventIdWithHttpInfo(String id, UpdateDetailsByIventIdDto updateDetailsByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}/update/details'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateDetailsByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventin detaylarını günceller
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateDetailsByIventIdDto] updateDetailsByIventIdDto (required):
  Future<void> updateDetailsByIventId(String id, UpdateDetailsByIventIdDto updateDetailsByIventIdDto,) async {
    final response = await updateDetailsByIventIdWithHttpInfo(id, updateDetailsByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile iventin konumunu günceller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateLocationByIventIdDto] updateLocationByIventIdDto (required):
  Future<Response> updateLocationByIventIdWithHttpInfo(String id, UpdateLocationByIventIdDto updateLocationByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/ivents/{id}/update/location'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateLocationByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile iventin konumunu günceller
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateLocationByIventIdDto] updateLocationByIventIdDto (required):
  Future<void> updateLocationByIventId(String id, UpdateLocationByIventIdDto updateLocationByIventIdDto,) async {
    final response = await updateLocationByIventIdWithHttpInfo(id, updateLocationByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

