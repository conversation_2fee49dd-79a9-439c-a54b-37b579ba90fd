//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class UniversityItem {
  /// Returns a new [UniversityItem] instance.
  UniversityItem({
    required this.universityName,
    this.universityImageUrl,
    this.universityLocationState,
  });

  /// Name of the university
  String universityName;

  /// URL to the university image
  String? universityImageUrl;

  /// State where the university is located
  String? universityLocationState;

  @override
  bool operator ==(Object other) => identical(this, other) || other is UniversityItem &&
    other.universityName == universityName &&
    other.universityImageUrl == universityImageUrl &&
    other.universityLocationState == universityLocationState;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (universityName.hashCode) +
    (universityImageUrl == null ? 0 : universityImageUrl!.hashCode) +
    (universityLocationState == null ? 0 : universityLocationState!.hashCode);

  @override
  String toString() => 'UniversityItem[universityName=$universityName, universityImageUrl=$universityImageUrl, universityLocationState=$universityLocationState]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'universityName'] = this.universityName;
    if (this.universityImageUrl != null) {
      json[r'universityImageUrl'] = this.universityImageUrl;
    } else {
      json[r'universityImageUrl'] = null;
    }
    if (this.universityLocationState != null) {
      json[r'universityLocationState'] = this.universityLocationState;
    } else {
      json[r'universityLocationState'] = null;
    }
    return json;
  }

  /// Returns a new [UniversityItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static UniversityItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "UniversityItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "UniversityItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return UniversityItem(
        universityName: mapValueOfType<String>(json, r'universityName')!,
        universityImageUrl: mapValueOfType<String>(json, r'universityImageUrl'),
        universityLocationState: mapValueOfType<String>(json, r'universityLocationState'),
      );
    }
    return null;
  }

  static List<UniversityItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UniversityItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UniversityItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, UniversityItem> mapFromJson(dynamic json) {
    final map = <String, UniversityItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = UniversityItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of UniversityItem-objects as value to a dart map
  static Map<String, List<UniversityItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<UniversityItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = UniversityItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'universityName',
  };
}

