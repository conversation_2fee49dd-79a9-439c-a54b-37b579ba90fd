import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/utils/list_utils.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/widgets/profile_tabs.dart';

class ProfilePage extends StatelessWidget {
  final String userId;

  const ProfilePage({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    return Obx(() {
      final pageContext = controller.userInfoController.userPageInfo;
      final vibesContext = controller.contentController.vibeFolders;
      if (pageContext == null || vibesContext == null) {
        return IaScaffold.loading();
      }
      final isFirstPerson = pageContext.isFirstPerson;
      final hobbiesList = pageContext.hobbies;
      return IaScaffold.profile(
        title: pageContext.fullname,
        subtitle: '@${pageContext.username}',
        body: DefaultTabController(
          length: 2,
          child: NestedScrollView(
            headerSliverBuilder: (context, value) {
              return [
                SliverToBoxAdapter(child: _buildStatsAvatarRow(controller, pageContext)),
                SliverToBoxAdapter(child: _buildProfileButtonsRow(controller, pageContext)),
                if (hobbiesList.isNotEmpty || isFirstPerson)
                  SliverToBoxAdapter(
                    child: _buildHobbiesRow(hobbiesList, isFirstPerson),
                  ),
                SliverToBoxAdapter(child: _buildProfileTabButtons()),
              ];
            },
            body: ProfileTabs(controller: controller, vibesContext: vibesContext),
          ),
        ),
      );
    });
  }

  Widget _buildStatsAvatarRow(ProfileController controller, GetUserByUserIdReturn pageContext) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ProfileCount.iventCount(controller),
          SizedBox(width: Get.width * 0.13),
          CircleAvatar(
            radius: Get.width * 0.084,
            backgroundImage: pageContext.avatarUrl != null ? NetworkImage(pageContext.avatarUrl!) : null,
          ),
          SizedBox(width: Get.width * 0.13),
          pageContext.userRole == UserTypeEnum.CREATOR
              ? ProfileCount.followerCount(controller)
              : ProfileCount.friendCount(controller),
        ],
      ),
    );
  }

  Widget _buildProfileButtonsRow(ProfileController controller, GetUserByUserIdReturn pageContext) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: insertBetween(
          _buildButtonLayout(controller, pageContext).map((buttonLayout) {
            return Expanded(child: buttonLayout);
          }).toList(),
          const SizedBox(width: AppDimensions.padding12),
        ),
      ),
    );
  }

  List<Widget> _buildButtonLayout(ProfileController controller, GetUserByUserIdReturn pageContext) {
    if (pageContext.isFirstPerson) {
      return [
        ProfileButtons.profileFollowings(onTap: controller.goToFollowingsPage),
        ProfileButtons.profileFavorites(onTap: controller.goToFavoritesPage),
      ];
    } else {
      if (pageContext.userRole == UserTypeEnum.CREATOR) {
        return [
          Obx(() {
            return ProfileButtons.profileFollow(
              onTap: controller.userInfoController.toggleFollowing,
              isFollowing: controller.userInfoController.isFollowing,
            );
          }),
          Obx(() {
            return ProfileButtons.profileAddFriend(
              onTap: controller.userInfoController.toggleFriendship,
              relationshipStatus: controller.userInfoController.relationshipStatus,
            );
          }),
        ];
      } else {
        return [
          Obx(() {
            return ProfileButtons.profileAddFriend(
              onTap: controller.userInfoController.toggleFriendship,
              relationshipStatus: controller.userInfoController.relationshipStatus,
            );
          }),
        ];
      }
    }
  }

  Widget _buildHobbiesRow(List<String> hobbiesList, bool isFirstPerson) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Row(
        children: [
          if (isFirstPerson)
            Padding(
              padding: const EdgeInsets.only(left: AppDimensions.padding20, right: AppDimensions.padding8),
              child: ProfileButtons.profileTagEditButton(),
            ),
          Expanded(
            child: Container(
              height: AppDimensions.buttonHeightProfileTag,
              child: ListView.separated(
                padding: const EdgeInsets.only(right: AppDimensions.padding20),
                scrollDirection: Axis.horizontal,
                itemCount: hobbiesList.length,
                itemBuilder: (context, index) {
                  return ProfileButtons.profileTag(
                    onTap: () {},
                    text: hobbiesList[index],
                  );
                },
                separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileTabButtons() {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        height: 40,
        child: TabBar(
          dividerHeight: 0,
          indicatorColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          labelStyle: AppTextStyles.size14Bold,
          unselectedLabelStyle: AppTextStyles.size14BoldTextSecondary,
          tabs: const [Tab(text: 'Vibes'), Tab(text: 'iVent Memories')],
        ),
      ),
    );
  }
}
