# openapi.model.SearchFriendsByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groups** | [**List<GroupListItem>**](GroupListItem.md) | List of groups | [default to const []]
**groupCount** | **int** | Total number of groups | 
**friends** | [**List<UserListItem>**](UserListItem.md) | List of friends | [default to const []]
**friendCount** | **int** | Total number of friends | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



