//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetIventsByUserIdReturn {
  /// Returns a new [GetIventsByUserIdReturn] instance.
  GetIventsByUserIdReturn({
    this.ivents = const [],
    required this.iventCount,
    required this.isFirstPerson,
  });

  /// List of ivents created by the user
  List<IventListItem> ivents;

  /// Total number of ivents created by the user
  ///
  /// Minimum value: 0
  int iventCount;

  /// Whether this is the current user's own profile
  bool isFirstPerson;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetIventsByUserIdReturn &&
    _deepEquality.equals(other.ivents, ivents) &&
    other.iventCount == iventCount &&
    other.isFirstPerson == isFirstPerson;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (ivents.hashCode) +
    (iventCount.hashCode) +
    (isFirstPerson.hashCode);

  @override
  String toString() => 'GetIventsByUserIdReturn[ivents=$ivents, iventCount=$iventCount, isFirstPerson=$isFirstPerson]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'ivents'] = this.ivents;
      json[r'iventCount'] = this.iventCount;
      json[r'isFirstPerson'] = this.isFirstPerson;
    return json;
  }

  /// Returns a new [GetIventsByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetIventsByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetIventsByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetIventsByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetIventsByUserIdReturn(
        ivents: IventListItem.listFromJson(json[r'ivents']),
        iventCount: mapValueOfType<int>(json, r'iventCount')!,
        isFirstPerson: mapValueOfType<bool>(json, r'isFirstPerson')!,
      );
    }
    return null;
  }

  static List<GetIventsByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetIventsByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetIventsByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetIventsByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetIventsByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetIventsByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetIventsByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetIventsByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetIventsByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetIventsByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'ivents',
    'iventCount',
    'isFirstPerson',
  };
}

