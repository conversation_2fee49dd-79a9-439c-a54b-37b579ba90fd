# openapi.api.PageMembershipsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**addPageMembersByPageId**](PageMembershipsApi.md#pagemembershipscontrolleraddpagemembersbypageid) | **POST** /pageMemberships/{pageId}/add | Sayfa IDsi ile üye eklenir
[**joinPageMembershipByPageId**](PageMembershipsApi.md#pagemembershipscontrollerjoinpagemembershipbypageid) | **POST** /pageMemberships/{pageId}/join | Sayfa IDsi ile üye olunur
[**leavePageMembershipByPageId**](PageMembershipsApi.md#pagemembershipscontrollerleavepagemembershipbypageid) | **POST** /pageMemberships/{pageId}/leave | Sayfa IDsi ile üyelikten çıkılır
[**leavePageModerationByPageId**](PageMembershipsApi.md#pagemembershipscontrollerleavepagemoderationbypageid) | **POST** /pageMemberships/{pageId}/administration/leave | Sayfa IDsi ile adminlikten ayrılınır
[**removePageMemberByPageId**](PageMembershipsApi.md#pagemembershipscontrollerremovepagememberbypageid) | **POST** /pageMemberships/{pageId}/remove | Sayfa IDsi ile üyelikten çıkartılır
[**removePageModeratorByPageId**](PageMembershipsApi.md#pagemembershipscontrollerremovepagemoderatorbypageid) | **POST** /pageMemberships/{pageId}/administration/remove | Sayfa IDsi ile yardımcı admin çıkartılır
[**searchAdministrationByPageId**](PageMembershipsApi.md#pagemembershipscontrollersearchadministrationbypageid) | **GET** /pageMemberships/{pageId}/administration | Sayfa IDsi ile yetkilileri listeler
[**searchModeratorsForPageCreation**](PageMembershipsApi.md#pagemembershipscontrollersearchmoderatorsforpagecreation) | **GET** /pageMemberships/administration/search | Sayfa oluştururken uygun yardımcı adminler listelenir
[**searchModeratorsToAddByPageId**](PageMembershipsApi.md#pagemembershipscontrollersearchmoderatorstoaddbypageid) | **GET** /pageMemberships/{pageId}/administration/search | Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir
[**searchPageMembersByPageId**](PageMembershipsApi.md#pagemembershipscontrollersearchpagemembersbypageid) | **GET** /pageMemberships/{pageId}/members | Sayfa IDsi ile üyeler listelenir
[**searchUsersToAddByPageId**](PageMembershipsApi.md#pagemembershipscontrollersearchuserstoaddbypageid) | **GET** /pageMemberships/{pageId}/search | Sayfa IDsi ile eklenebilecek hesaplar listelenir
[**transferPageAdministrationByPageId**](PageMembershipsApi.md#pagemembershipscontrollertransferpageadministrationbypageid) | **POST** /pageMemberships/{pageId}/administration/transfer | Sayfa IDsi ile adminlik devredilir


# **addPageMembersByPageId**
> addPageMembersByPageId(pageId, addPageMembersByPageIdDto)

Sayfa IDsi ile üye eklenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final addPageMembersByPageIdDto = AddPageMembersByPageIdDto(); // AddPageMembersByPageIdDto | 

try {
    api_instance.addPageMembersByPageId(pageId, addPageMembersByPageIdDto);
} catch (e) {
    print('Exception when calling PageMembershipsApi->addPageMembersByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **addPageMembersByPageIdDto** | [**AddPageMembersByPageIdDto**](AddPageMembersByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **joinPageMembershipByPageId**
> joinPageMembershipByPageId(pageId)

Sayfa IDsi ile üye olunur

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 

try {
    api_instance.joinPageMembershipByPageId(pageId);
} catch (e) {
    print('Exception when calling PageMembershipsApi->joinPageMembershipByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **leavePageMembershipByPageId**
> leavePageMembershipByPageId(pageId)

Sayfa IDsi ile üyelikten çıkılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 

try {
    api_instance.leavePageMembershipByPageId(pageId);
} catch (e) {
    print('Exception when calling PageMembershipsApi->leavePageMembershipByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **leavePageModerationByPageId**
> leavePageModerationByPageId(pageId)

Sayfa IDsi ile adminlikten ayrılınır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 

try {
    api_instance.leavePageModerationByPageId(pageId);
} catch (e) {
    print('Exception when calling PageMembershipsApi->leavePageModerationByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removePageMemberByPageId**
> removePageMemberByPageId(pageId, removePageMemberByPageIdDto)

Sayfa IDsi ile üyelikten çıkartılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final removePageMemberByPageIdDto = RemovePageMemberByPageIdDto(); // RemovePageMemberByPageIdDto | 

try {
    api_instance.removePageMemberByPageId(pageId, removePageMemberByPageIdDto);
} catch (e) {
    print('Exception when calling PageMembershipsApi->removePageMemberByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **removePageMemberByPageIdDto** | [**RemovePageMemberByPageIdDto**](RemovePageMemberByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removePageModeratorByPageId**
> removePageModeratorByPageId(pageId, removePageModeratorByPageIdDto)

Sayfa IDsi ile yardımcı admin çıkartılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final removePageModeratorByPageIdDto = RemovePageModeratorByPageIdDto(); // RemovePageModeratorByPageIdDto | 

try {
    api_instance.removePageModeratorByPageId(pageId, removePageModeratorByPageIdDto);
} catch (e) {
    print('Exception when calling PageMembershipsApi->removePageModeratorByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **removePageModeratorByPageIdDto** | [**RemovePageModeratorByPageIdDto**](RemovePageModeratorByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchAdministrationByPageId**
> SearchAdministrationByPageIdReturn searchAdministrationByPageId(pageId, q, limit, page)

Sayfa IDsi ile yetkilileri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchAdministrationByPageId(pageId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PageMembershipsApi->searchAdministrationByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchAdministrationByPageIdReturn**](SearchAdministrationByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchModeratorsForPageCreation**
> SearchModeratorsForPageCreationReturn searchModeratorsForPageCreation(q, limit, page)

Sayfa oluştururken uygun yardımcı adminler listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchModeratorsForPageCreation(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PageMembershipsApi->searchModeratorsForPageCreation: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchModeratorsForPageCreationReturn**](SearchModeratorsForPageCreationReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchModeratorsToAddByPageId**
> SearchModeratorsForPageCreationReturn searchModeratorsToAddByPageId(pageId, q, limit, page)

Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchModeratorsToAddByPageId(pageId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PageMembershipsApi->searchModeratorsToAddByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchModeratorsForPageCreationReturn**](SearchModeratorsForPageCreationReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchPageMembersByPageId**
> SearchPageMembersByPageIdReturn searchPageMembersByPageId(pageId, q, limit, page)

Sayfa IDsi ile üyeler listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchPageMembersByPageId(pageId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PageMembershipsApi->searchPageMembersByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchPageMembersByPageIdReturn**](SearchPageMembersByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchUsersToAddByPageId**
> SearchUsersToAddByPageIdReturn searchUsersToAddByPageId(pageId, q, limit, page)

Sayfa IDsi ile eklenebilecek hesaplar listelenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchUsersToAddByPageId(pageId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PageMembershipsApi->searchUsersToAddByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchUsersToAddByPageIdReturn**](SearchUsersToAddByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **transferPageAdministrationByPageId**
> transferPageAdministrationByPageId(pageId, transferPageAdministrationByPageIdDto)

Sayfa IDsi ile adminlik devredilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PageMembershipsApi();
final pageId = pageId_example; // String | 
final transferPageAdministrationByPageIdDto = TransferPageAdministrationByPageIdDto(); // TransferPageAdministrationByPageIdDto | 

try {
    api_instance.transferPageAdministrationByPageId(pageId, transferPageAdministrationByPageIdDto);
} catch (e) {
    print('Exception when calling PageMembershipsApi->transferPageAdministrationByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **pageId** | **String**|  | 
 **transferPageAdministrationByPageIdDto** | [**TransferPageAdministrationByPageIdDto**](TransferPageAdministrationByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


