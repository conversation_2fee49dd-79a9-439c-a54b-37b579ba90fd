# openapi.model.SearchCollabsForIventCreationReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**accounts** | [**List<BasicAccountListItem>**](BasicAccountListItem.md) | List of accounts available for collaboration during ivent creation | [default to const []]
**accountCount** | **int** | Total number of accounts available for collaboration | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



