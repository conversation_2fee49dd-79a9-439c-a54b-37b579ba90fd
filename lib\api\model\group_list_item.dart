//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GroupListItem {
  /// Returns a new [GroupListItem] instance.
  GroupListItem({
    required this.groupId,
    required this.groupName,
    this.thumbnailUrl,
    this.memberFirstnames = const [],
    required this.memberCount,
  });

  /// UUID of the group
  String groupId;

  /// Name of the group
  String groupName;

  /// URL to the group thumbnail image
  String? thumbnailUrl;

  /// List of member's first names in the group
  List<String> memberFirstnames;

  /// Number of members in the group
  ///
  /// Minimum value: 0
  int memberCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GroupListItem &&
    other.groupId == groupId &&
    other.groupName == groupName &&
    other.thumbnailUrl == thumbnailUrl &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames) &&
    other.memberCount == memberCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (groupId.hashCode) +
    (groupName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (memberFirstnames.hashCode) +
    (memberCount.hashCode);

  @override
  String toString() => 'GroupListItem[groupId=$groupId, groupName=$groupName, thumbnailUrl=$thumbnailUrl, memberFirstnames=$memberFirstnames, memberCount=$memberCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'groupId'] = this.groupId;
      json[r'groupName'] = this.groupName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'memberFirstnames'] = this.memberFirstnames;
      json[r'memberCount'] = this.memberCount;
    return json;
  }

  /// Returns a new [GroupListItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GroupListItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GroupListItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GroupListItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GroupListItem(
        groupId: mapValueOfType<String>(json, r'groupId')!,
        groupName: mapValueOfType<String>(json, r'groupName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
      );
    }
    return null;
  }

  static List<GroupListItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GroupListItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GroupListItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GroupListItem> mapFromJson(dynamic json) {
    final map = <String, GroupListItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GroupListItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GroupListItem-objects as value to a dart map
  static Map<String, List<GroupListItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GroupListItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GroupListItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'groupId',
    'groupName',
    'memberFirstnames',
    'memberCount',
  };
}

