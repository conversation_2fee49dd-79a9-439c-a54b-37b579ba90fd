import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

/// Controller for managing sliding panels and screen navigation in home feature
///
/// Handles the sliding up panel behavior, screen switching between feed/search/filter,
/// and panel visibility states. Manages the bottom panel UI interactions.
class HomePanelsController extends BaseHomeController {
  // Controllers
  final _panelController = PanelController();

  // Reactive state
  final _isPanelDraggable = true.obs;
  final _isPanelVisible = true.obs;
  final _screenIndex = 0.obs;

  // Constructor
  HomePanelsController(AuthService authService, HomeStateManager state) : super(authService, state);

  // Getters
  bool get isPanelDraggable => _isPanelDraggable.value;
  bool get isPanelVisible => _isPanelVisible.value;
  int get screenIndex => _screenIndex.value;
  PanelController get panelController => _panelController;

  // Setters
  set isPanelDraggable(bool value) => _isPanelDraggable.value = value;
  set isPanelVisible(bool value) => _isPanelVisible.value = value;
  set screenIndex(int value) => _screenIndex.value = value;

  // Navigation methods

  /// Navigate to feed page with draggable panel
  void goToFeedPage() {
    screenIndex = 0;
    panelController.open();
    isPanelDraggable = true;
  }

  /// Navigate to search page with fixed panel
  void goToSearchPage() {
    screenIndex = 1;
    panelController.open();
    isPanelDraggable = false;
  }

  /// Navigate to filter page with fixed panel
  void goToFilterPage() {
    screenIndex = 2;
    panelController.open();
    isPanelDraggable = false;
  }

  // Panel state methods

  /// Set panel as open/visible
  void setPanelOpen() => isPanelVisible = true;

  /// Set panel as closed/hidden
  void setPanelClosed() => isPanelVisible = false;
}
