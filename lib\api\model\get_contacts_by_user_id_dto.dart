//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetContactsByUserIdDto {
  /// Returns a new [GetContactsByUserIdDto] instance.
  GetContactsByUserIdDto({
    this.phoneNumbers = const [],
  });

  /// Array of phone numbers to check for contacts
  List<String> phoneNumbers;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetContactsByUserIdDto &&
    _deepEquality.equals(other.phoneNumbers, phoneNumbers);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (phoneNumbers.hashCode);

  @override
  String toString() => 'GetContactsByUserIdDto[phoneNumbers=$phoneNumbers]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'phoneNumbers'] = this.phoneNumbers;
    return json;
  }

  /// Returns a new [GetContactsByUserIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetContactsByUserIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetContactsByUserIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetContactsByUserIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetContactsByUserIdDto(
        phoneNumbers: json[r'phoneNumbers'] is Iterable
            ? (json[r'phoneNumbers'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<GetContactsByUserIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetContactsByUserIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetContactsByUserIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetContactsByUserIdDto> mapFromJson(dynamic json) {
    final map = <String, GetContactsByUserIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetContactsByUserIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetContactsByUserIdDto-objects as value to a dart map
  static Map<String, List<GetContactsByUserIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetContactsByUserIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetContactsByUserIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'phoneNumbers',
  };
}

