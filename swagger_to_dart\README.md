# Swagger to Dart Code Generation

This folder contains utilities and documentation for generating Dart API client code from Swagger/OpenAPI specifications.

## Usage

1. **Download the API JSON**: Run the following command to download the latest API JSON from the staging server:

   ```powershell
   Invoke-WebRequest -Uri "http://localhost:3000/api-json" -OutFile "swagger_to_dart\api-json.json"
   ```

2. **Generate the Dart API Client**: Run the `swagger_to_dart.ps1` script to generate the Dart API client code:

   ```powershell
   .\swagger_to_dart.ps1
   ```

## Last Updated

July 2025
