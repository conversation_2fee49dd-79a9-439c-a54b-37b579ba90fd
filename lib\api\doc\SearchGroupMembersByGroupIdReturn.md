# openapi.model.SearchGroupMembersByGroupIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**members** | [**List<UserListItemWithGroupRole>**](UserListItemWithGroupRole.md) | List of group members with their roles and friendship status | [default to const []]
**memberCount** | **int** | Total number of group members | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



