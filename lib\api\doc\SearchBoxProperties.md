# openapi.model.SearchBoxProperties

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **String** | Name of the place | 
**namePreferred** | **String** | Preferred name of the place | [optional] 
**mapboxId** | **String** | Mapbox ID of the place | 
**featureType** | **String** | Feature type | 
**address** | **String** | Address of the place | [optional] 
**fullAddress** | **String** | Full address of the place | [optional] 
**placeFormatted** | **String** | Formatted place description | 
**context** | [**SearchBoxContext**](SearchBoxContext.md) | Context information about the place | 
**language** | **String** | Language of the result | 
**maki** | **String** | Maki icon identifier | [optional] 
**poiCategory** | **List<String>** | POI categories | [optional] [default to const []]
**poiCategoryIds** | **List<String>** | POI category IDs | [optional] [default to const []]
**brand** | **List<String>** | Brand names | [optional] [default to const []]
**brandId** | **List<String>** | Brand IDs | [optional] [default to const []]
**externalIds** | [**Object**](.md) | External IDs mapping | [optional] 
**metadata** | [**Object**](.md) | Additional metadata | [optional] 
**coordinates** | [**SearchBoxCoordinates**](SearchBoxCoordinates.md) | Coordinates of the place | 
**bbox** | **List<double>** | Bounding box coordinates [minX, minY, maxX, maxY] | [optional] [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



