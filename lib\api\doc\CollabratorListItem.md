# openapi.model.CollabratorListItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**collabId** | **String** | UUID of the collaborator | 
**collabName** | **String** | Name of the collaborator | 
**collabType** | [**AccountTypeEnum**](AccountTypeEnum.md) |  | 
**collabImageUrl** | **String** | URL to the collaborator image | [optional] 
**pageMembershipStatus** | [**PageMembershipStatusEnum**](PageMembershipStatusEnum.md) |  | 
**relationshipStatus** | [**UserRelationshipStatusEnum**](UserRelationshipStatusEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



