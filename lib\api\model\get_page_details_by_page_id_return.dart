//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetPageDetailsByPageIdReturn {
  /// Returns a new [GetPageDetailsByPageIdReturn] instance.
  GetPageDetailsByPageIdReturn({
    this.description,
    this.websiteUrl,
    required this.locationId,
    this.locationAdress,
  });

  /// Description of the page
  String? description;

  /// Website URL for the page
  String? websiteUrl;

  /// UUID of the location where the page is based
  String locationId;

  /// Address of the page location
  String? locationAdress;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetPageDetailsByPageIdReturn &&
    other.description == description &&
    other.websiteUrl == websiteUrl &&
    other.locationId == locationId &&
    other.locationAdress == locationAdress;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (description == null ? 0 : description!.hashCode) +
    (websiteUrl == null ? 0 : websiteUrl!.hashCode) +
    (locationId.hashCode) +
    (locationAdress == null ? 0 : locationAdress!.hashCode);

  @override
  String toString() => 'GetPageDetailsByPageIdReturn[description=$description, websiteUrl=$websiteUrl, locationId=$locationId, locationAdress=$locationAdress]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
    if (this.websiteUrl != null) {
      json[r'websiteUrl'] = this.websiteUrl;
    } else {
      json[r'websiteUrl'] = null;
    }
      json[r'locationId'] = this.locationId;
    if (this.locationAdress != null) {
      json[r'locationAdress'] = this.locationAdress;
    } else {
      json[r'locationAdress'] = null;
    }
    return json;
  }

  /// Returns a new [GetPageDetailsByPageIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetPageDetailsByPageIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetPageDetailsByPageIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetPageDetailsByPageIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetPageDetailsByPageIdReturn(
        description: mapValueOfType<String>(json, r'description'),
        websiteUrl: mapValueOfType<String>(json, r'websiteUrl'),
        locationId: mapValueOfType<String>(json, r'locationId')!,
        locationAdress: mapValueOfType<String>(json, r'locationAdress'),
      );
    }
    return null;
  }

  static List<GetPageDetailsByPageIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetPageDetailsByPageIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetPageDetailsByPageIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetPageDetailsByPageIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetPageDetailsByPageIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetPageDetailsByPageIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetPageDetailsByPageIdReturn-objects as value to a dart map
  static Map<String, List<GetPageDetailsByPageIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetPageDetailsByPageIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetPageDetailsByPageIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'locationId',
  };
}

