//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Format of the media
class MediaFormatEnum {
  /// Instantiate a new enum with the provided [value].
  const MediaFormatEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const image = MediaFormatEnum._(r'image');
  static const video = MediaFormatEnum._(r'video');

  /// List of all possible values in this [enum][MediaFormatEnum].
  static const values = <MediaFormatEnum>[
    image,
    video,
  ];

  static MediaFormatEnum? fromJson(dynamic value) => MediaFormatEnumTypeTransformer().decode(value);

  static List<MediaFormatEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MediaFormatEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MediaFormatEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [MediaFormatEnum] to String,
/// and [decode] dynamic data back to [MediaFormatEnum].
class MediaFormatEnumTypeTransformer {
  factory MediaFormatEnumTypeTransformer() => _instance ??= const MediaFormatEnumTypeTransformer._();

  const MediaFormatEnumTypeTransformer._();

  String encode(MediaFormatEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a MediaFormatEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  MediaFormatEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'image': return MediaFormatEnum.image;
        case r'video': return MediaFormatEnum.video;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [MediaFormatEnumTypeTransformer] instance.
  static MediaFormatEnumTypeTransformer? _instance;
}

