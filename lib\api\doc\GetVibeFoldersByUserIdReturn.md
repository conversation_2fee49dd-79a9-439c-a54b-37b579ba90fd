# openapi.model.GetVibeFoldersByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**vibeFolders** | [**List<VibeFolderCardItem>**](VibeFolderCardItem.md) | List of user's vibe folders | [default to const []]
**vibeFolderCount** | **int** | Total number of vibe folders | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



