import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/features/home/<USER>/strings.dart';

class HomeButtons {
  HomeButtons._();

  static IaRoundedButton dateFilter({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isSelected,
    required String text,
    String? iconPath,
  }) {
    return IaRoundedButton(
      key: key,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      margin: margin,
      height: AppDimensions.buttonHeightDateFilter,
      roundness: AppDimensions.buttonRadiusL,
      onTap: onTap,
      color: isSelected ? AppColors.primary : AppColors.white,
      text: text,
      textStyle: isSelected ? AppTextStyles.size12RegularWhite : AppTextStyles.size12Regular,
      leading: iconPath != null
          ? IaSvgIcon(
              iconPath: iconPath,
              iconSize: AppDimensions.buttonSizeDateFilterCalender,
              iconColor: isSelected ? AppColors.white : AppColors.black,
            )
          : null,
      boxShadow: AppColors.usualShadow,
    );
  }

  static IaRoundedButton locationFilter({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required String text,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      height: AppDimensions.buttonHeightLocationFilter,
      roundness: AppDimensions.radiusXXL,
      onTap: onTap,
      color: AppColors.white,
      text: '${HomeStrings.seciliKonum} ${text}',
      textStyle: AppTextStyles.size12Regular,
      border: Border.all(color: AppColors.primary, width: 1),
      boxShadow: AppColors.usualShadow,
    );
  }

  static IaRoundedButton filterSelectedHobbyTag({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required String text,
    required bool isSelected,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightFilterSelectedHobbyTag,
      roundness: AppDimensions.buttonRadiusL,
      boxShadow: AppColors.usualShadow,
      onTap: onTap,
      color: isSelected ? AppColors.primary : AppColors.white,
      text: text,
      textStyle: isSelected ? AppTextStyles.size12RegularWhite : AppTextStyles.size12Regular,
      expand: false,
    );
  }

  static IaRoundedButton filterAvailableHobbyTag({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required String text,
    required bool isSelected,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightFilterSelectedHobbyTag,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isSelected ? AppColors.primary : AppColors.lightGrey,
      text: text,
      textStyle: isSelected ? AppTextStyles.size12RegularWhite : AppTextStyles.size12Regular,
      expand: false,
    );
  }

  static IaIconButton searchButton({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.searchMagnifyingGlass,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.grey600,
    );
  }

  static IaIconButton filterButton({
    Key? key,
    VoidCallback? onTap,
    required bool isActive,
    required int filterCount,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.filter,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: isActive ? AppColors.primary : AppColors.grey600,
      overlay: filterCount != 0
          ? Positioned(
              bottom: -2,
              right: 1,
              child: IaRoundedContainer(
                text: filterCount.toString(),
                textStyle: AppTextStyles.size12BoldPrimary,
              ),
            )
          : null,
    );
  }

  static IaRoundedButton popUp({
    Key? key,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    required bool isPrimary,
    required String text,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      height: AppDimensions.buttonHeightPopup,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isPrimary ? AppColors.primary : AppColors.white,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium.copyWith(color: isPrimary ? AppColors.white : AppColors.primary),
    );
  }

  static IaBlurredIconButton favoriteIventThumbnailS({
    Key? key,
    VoidCallback? onTap,
    required bool isFavorited,
  }) {
    return IaBlurredIconButton(
      key: key,
      color: AppColors.black.withValues(alpha: 0.2),
      onTap: onTap,
      roundness: AppDimensions.radiusS,
      width: AppDimensions.actionButtonSizeS,
      height: AppDimensions.actionButtonSizeS,
      iconPath: isFavorited ? AppAssets.star : AppAssets.star,
      iconSize: AppDimensions.actionButtonSizeS * 1 / 2,
      iconColor: isFavorited ? AppColors.starYellow : AppColors.white,
    );
  }

  static IaBlurredIconButton shareIventThumbnailS({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaBlurredIconButton(
      key: key,
      color: AppColors.black.withValues(alpha: 0.2),
      onTap: onTap,
      roundness: AppDimensions.radiusS,
      width: AppDimensions.actionButtonSizeS,
      height: AppDimensions.actionButtonSizeS,
      iconPath: AppAssets.shareAndroid,
      iconSize: AppDimensions.actionButtonSizeS * 1 / 2,
      iconColor: AppColors.white,
    );
  }

  static IaCircularButton createIventButtonL({
    Key? key,
  }) {
    return SharedButtons.createIventButton(
      key: key,
      buttonSize: AppDimensions.buttonSizeCreateIventL,
    );
  }

  static IaIconButton closeButtonLight({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.closeLG,
      iconSize: AppDimensions.defaultIconButtonSize * 2 / 3,
      iconColor: AppColors.grey400,
    );
  }

  static IaRoundedButton mapDateFilter({
    Key? key,
    VoidCallback? onTap,
    required String text,
  }) {
    return IaRoundedButton(
      onTap: onTap,
      roundness: AppDimensions.radiusXL,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightMapDateFilter,
      boxShadow: AppColors.usualShadow,
      color: AppColors.white,
      text: text,
      textStyle: AppTextStyles.size12Bold,
      leading: const IaSvgIcon(iconPath: AppAssets.calendar, iconColor: AppColors.black),
    );
  }

  static IaCircularButton findUserLocation({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaCircularButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.buttonSizeFindUserLocation,
      backgroundColor: AppColors.black.withValues(alpha: 0.7),
      iconPath: AppAssets.navigation,
      iconSize: AppDimensions.buttonSizeFindUserLocation,
      iconColor: AppColors.white,
    );
  }

  static IaIconButton expandCategory({
    Key? key,
    VoidCallback? onTap,
    required bool isExpanded,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: isExpanded ? AppAssets.chevronUp : AppAssets.chevronDown,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.mediumGrey,
    );
  }

  static Row actionButtonS({
    Key? key,
    VoidCallback? onFavorite,
    VoidCallback? onShare,
    required bool isFavorited,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        HomeButtons.favoriteIventThumbnailS(onTap: onFavorite, isFavorited: isFavorited),
        const SizedBox(width: AppDimensions.padding4),
        HomeButtons.shareIventThumbnailS(onTap: onShare),
      ],
    );
  }
}
