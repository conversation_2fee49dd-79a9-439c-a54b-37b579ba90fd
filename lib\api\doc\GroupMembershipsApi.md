# openapi.api.GroupMembershipsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**addModeratorByGroupId**](GroupMembershipsApi.md#groupmembershipscontrolleraddmoderatorbygroupid) | **POST** /groupMemberships/{groupId}/administration/add | Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar
[**inviteMembersByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollerinvitemembersbygroupid) | **POST** /groupMemberships/{groupId}/invite | Grubun IDsi ile bir hesaba davet gönderilir
[**leaveGroupByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollerleavegroupbygroupid) | **POST** /groupMemberships/{groupId}/leave | Grubun IDsi ile arkadaş grubundan ayrılınır
[**removeMemberByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollerremovememberbygroupid) | **POST** /groupMemberships/{groupId}/remove | Grubun IDsi ile bir hesap gruptan çıkartılır
[**removeModeratorByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollerremovemoderatorbygroupid) | **POST** /groupMemberships/{groupId}/administration/remove | Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır
[**searchGroupMembersByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollersearchgroupmembersbygroupid) | **GET** /groupMemberships/{groupId}/search | Arkadaş grubundaki üyeleri listeler
[**searchInvitableUsersByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollersearchinvitableusersbygroupid) | **GET** /groupMemberships/search/{groupId} | Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler
[**searchUsersForGroupCreation**](GroupMembershipsApi.md#groupmembershipscontrollersearchusersforgroupcreation) | **GET** /groupMemberships/search | Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler
[**transferAdministrationByGroupId**](GroupMembershipsApi.md#groupmembershipscontrollertransferadministrationbygroupid) | **POST** /groupMemberships/{groupId}/administration/transfer | Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği devreder


# **addModeratorByGroupId**
> addModeratorByGroupId(groupId, addModeratorByGroupIdDto)

Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final addModeratorByGroupIdDto = AddModeratorByGroupIdDto(); // AddModeratorByGroupIdDto | 

try {
    api_instance.addModeratorByGroupId(groupId, addModeratorByGroupIdDto);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->addModeratorByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **addModeratorByGroupIdDto** | [**AddModeratorByGroupIdDto**](AddModeratorByGroupIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **inviteMembersByGroupId**
> inviteMembersByGroupId(groupId, inviteMembersByGroupIdDto)

Grubun IDsi ile bir hesaba davet gönderilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final inviteMembersByGroupIdDto = InviteMembersByGroupIdDto(); // InviteMembersByGroupIdDto | 

try {
    api_instance.inviteMembersByGroupId(groupId, inviteMembersByGroupIdDto);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->inviteMembersByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **inviteMembersByGroupIdDto** | [**InviteMembersByGroupIdDto**](InviteMembersByGroupIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **leaveGroupByGroupId**
> leaveGroupByGroupId(groupId)

Grubun IDsi ile arkadaş grubundan ayrılınır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 

try {
    api_instance.leaveGroupByGroupId(groupId);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->leaveGroupByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeMemberByGroupId**
> removeMemberByGroupId(groupId, removeMemberByGroupIdDto)

Grubun IDsi ile bir hesap gruptan çıkartılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final removeMemberByGroupIdDto = RemoveMemberByGroupIdDto(); // RemoveMemberByGroupIdDto | 

try {
    api_instance.removeMemberByGroupId(groupId, removeMemberByGroupIdDto);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->removeMemberByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **removeMemberByGroupIdDto** | [**RemoveMemberByGroupIdDto**](RemoveMemberByGroupIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeModeratorByGroupId**
> removeModeratorByGroupId(groupId, removeModeratorByGroupIdDto)

Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final removeModeratorByGroupIdDto = RemoveModeratorByGroupIdDto(); // RemoveModeratorByGroupIdDto | 

try {
    api_instance.removeModeratorByGroupId(groupId, removeModeratorByGroupIdDto);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->removeModeratorByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **removeModeratorByGroupIdDto** | [**RemoveModeratorByGroupIdDto**](RemoveModeratorByGroupIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchGroupMembersByGroupId**
> SearchGroupMembersByGroupIdReturn searchGroupMembersByGroupId(groupId, q, limit, page)

Arkadaş grubundaki üyeleri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchGroupMembersByGroupId(groupId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->searchGroupMembersByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchGroupMembersByGroupIdReturn**](SearchGroupMembersByGroupIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchInvitableUsersByGroupId**
> SearchUsersForGroupCreationReturn searchInvitableUsersByGroupId(groupId, q, limit, page)

Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchInvitableUsersByGroupId(groupId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->searchInvitableUsersByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchUsersForGroupCreationReturn**](SearchUsersForGroupCreationReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchUsersForGroupCreation**
> SearchUsersForGroupCreationReturn searchUsersForGroupCreation(q, limit, page)

Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchUsersForGroupCreation(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->searchUsersForGroupCreation: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchUsersForGroupCreationReturn**](SearchUsersForGroupCreationReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **transferAdministrationByGroupId**
> transferAdministrationByGroupId(groupId, transferAdministrationByGroupIdDto)

Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği devreder

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = GroupMembershipsApi();
final groupId = groupId_example; // String | 
final transferAdministrationByGroupIdDto = TransferAdministrationByGroupIdDto(); // TransferAdministrationByGroupIdDto | 

try {
    api_instance.transferAdministrationByGroupId(groupId, transferAdministrationByGroupIdDto);
} catch (e) {
    print('Exception when calling GroupMembershipsApi->transferAdministrationByGroupId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupId** | **String**|  | 
 **transferAdministrationByGroupIdDto** | [**TransferAdministrationByGroupIdDto**](TransferAdministrationByGroupIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


