//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// View type of the ivent for the current user
class IventViewTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const IventViewTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const joined = IventViewTypeEnum._(r'joined');
  static const created = IventViewTypeEnum._(r'created');
  static const default_ = IventViewTypeEnum._(r'default');

  /// List of all possible values in this [enum][IventViewTypeEnum].
  static const values = <IventViewTypeEnum>[
    joined,
    created,
    default_,
  ];

  static IventViewTypeEnum? fromJson(dynamic value) => IventViewTypeEnumTypeTransformer().decode(value);

  static List<IventViewTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <IventViewTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = IventViewTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [IventViewTypeEnum] to String,
/// and [decode] dynamic data back to [IventViewTypeEnum].
class IventViewTypeEnumTypeTransformer {
  factory IventViewTypeEnumTypeTransformer() => _instance ??= const IventViewTypeEnumTypeTransformer._();

  const IventViewTypeEnumTypeTransformer._();

  String encode(IventViewTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a IventViewTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  IventViewTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'joined': return IventViewTypeEnum.joined;
        case r'created': return IventViewTypeEnum.created;
        case r'default': return IventViewTypeEnum.default_;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [IventViewTypeEnumTypeTransformer] instance.
  static IventViewTypeEnumTypeTransformer? _instance;
}

