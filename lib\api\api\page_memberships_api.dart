//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class PageMembershipsApi {
  PageMembershipsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Sayfa IDsi ile üye eklenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [AddPageMembersByPageIdDto] addPageMembersByPageIdDto (required):
  Future<Response> addPageMembersByPageIdWithHttpInfo(String pageId, AddPageMembersByPageIdDto addPageMembersByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/add'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody = addPageMembersByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile üye eklenir
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [AddPageMembersByPageIdDto] addPageMembersByPageIdDto (required):
  Future<void> addPageMembersByPageId(String pageId, AddPageMembersByPageIdDto addPageMembersByPageIdDto,) async {
    final response = await addPageMembersByPageIdWithHttpInfo(pageId, addPageMembersByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile üye olunur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  Future<Response> joinPageMembershipByPageIdWithHttpInfo(String pageId,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/join'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile üye olunur
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  Future<void> joinPageMembershipByPageId(String pageId,) async {
    final response = await joinPageMembershipByPageIdWithHttpInfo(pageId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile üyelikten çıkılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  Future<Response> leavePageMembershipByPageIdWithHttpInfo(String pageId,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/leave'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile üyelikten çıkılır
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  Future<void> leavePageMembershipByPageId(String pageId,) async {
    final response = await leavePageMembershipByPageIdWithHttpInfo(pageId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile adminlikten ayrılınır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  Future<Response> leavePageModerationByPageIdWithHttpInfo(String pageId,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/administration/leave'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile adminlikten ayrılınır
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  Future<void> leavePageModerationByPageId(String pageId,) async {
    final response = await leavePageModerationByPageIdWithHttpInfo(pageId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile üyelikten çıkartılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [RemovePageMemberByPageIdDto] removePageMemberByPageIdDto (required):
  Future<Response> removePageMemberByPageIdWithHttpInfo(String pageId, RemovePageMemberByPageIdDto removePageMemberByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/remove'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody = removePageMemberByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile üyelikten çıkartılır
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [RemovePageMemberByPageIdDto] removePageMemberByPageIdDto (required):
  Future<void> removePageMemberByPageId(String pageId, RemovePageMemberByPageIdDto removePageMemberByPageIdDto,) async {
    final response = await removePageMemberByPageIdWithHttpInfo(pageId, removePageMemberByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile yardımcı admin çıkartılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [RemovePageModeratorByPageIdDto] removePageModeratorByPageIdDto (required):
  Future<Response> removePageModeratorByPageIdWithHttpInfo(String pageId, RemovePageModeratorByPageIdDto removePageModeratorByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/administration/remove'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody = removePageModeratorByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile yardımcı admin çıkartılır
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [RemovePageModeratorByPageIdDto] removePageModeratorByPageIdDto (required):
  Future<void> removePageModeratorByPageId(String pageId, RemovePageModeratorByPageIdDto removePageModeratorByPageIdDto,) async {
    final response = await removePageModeratorByPageIdWithHttpInfo(pageId, removePageModeratorByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile yetkilileri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchAdministrationByPageIdWithHttpInfo(String pageId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/administration'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile yetkilileri listeler
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchAdministrationByPageIdReturn?> searchAdministrationByPageId(String pageId, { String? q, int? limit, int? page, }) async {
    final response = await searchAdministrationByPageIdWithHttpInfo(pageId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchAdministrationByPageIdReturn',) as SearchAdministrationByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa oluştururken uygun yardımcı adminler listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchModeratorsForPageCreationWithHttpInfo({ String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/administration/search';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa oluştururken uygun yardımcı adminler listelenir
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchModeratorsForPageCreationReturn?> searchModeratorsForPageCreation({ String? q, int? limit, int? page, }) async {
    final response = await searchModeratorsForPageCreationWithHttpInfo( q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchModeratorsForPageCreationReturn',) as SearchModeratorsForPageCreationReturn;
    
    }
    return null;
  }

  /// Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchModeratorsToAddByPageIdWithHttpInfo(String pageId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/administration/search'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Var olan sayfaya eklenebilecek uygun yardımcı adminler listelenir
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchModeratorsForPageCreationReturn?> searchModeratorsToAddByPageId(String pageId, { String? q, int? limit, int? page, }) async {
    final response = await searchModeratorsToAddByPageIdWithHttpInfo(pageId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchModeratorsForPageCreationReturn',) as SearchModeratorsForPageCreationReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile üyeler listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchPageMembersByPageIdWithHttpInfo(String pageId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/members'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile üyeler listelenir
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchPageMembersByPageIdReturn?> searchPageMembersByPageId(String pageId, { String? q, int? limit, int? page, }) async {
    final response = await searchPageMembersByPageIdWithHttpInfo(pageId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchPageMembersByPageIdReturn',) as SearchPageMembersByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile eklenebilecek hesaplar listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchUsersToAddByPageIdWithHttpInfo(String pageId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/search'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile eklenebilecek hesaplar listelenir
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchUsersToAddByPageIdReturn?> searchUsersToAddByPageId(String pageId, { String? q, int? limit, int? page, }) async {
    final response = await searchUsersToAddByPageIdWithHttpInfo(pageId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchUsersToAddByPageIdReturn',) as SearchUsersToAddByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile adminlik devredilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [TransferPageAdministrationByPageIdDto] transferPageAdministrationByPageIdDto (required):
  Future<Response> transferPageAdministrationByPageIdWithHttpInfo(String pageId, TransferPageAdministrationByPageIdDto transferPageAdministrationByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pageMemberships/{pageId}/administration/transfer'
      .replaceAll('{pageId}', pageId);

    // ignore: prefer_final_locals
    Object? postBody = transferPageAdministrationByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile adminlik devredilir
  ///
  /// Parameters:
  ///
  /// * [String] pageId (required):
  ///
  /// * [TransferPageAdministrationByPageIdDto] transferPageAdministrationByPageIdDto (required):
  Future<void> transferPageAdministrationByPageId(String pageId, TransferPageAdministrationByPageIdDto transferPageAdministrationByPageIdDto,) async {
    final response = await transferPageAdministrationByPageIdWithHttpInfo(pageId, transferPageAdministrationByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

