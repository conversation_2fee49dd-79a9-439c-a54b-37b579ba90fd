/// Authentication validation constants
/// 
/// Contains validation rules, limits, and patterns used throughout
/// the authentication feature for consistent validation behavior.
class AuthValidationConstants {
  AuthValidationConstants._(); // Private constructor to prevent instantiation

  // ==========================================================================
  // PHONE NUMBER VALIDATION
  // ==========================================================================
  
  /// Maximum character limit for phone number input
  static const int phoneNumberMaxLength = 10;
  
  /// Minimum character limit for phone number input
  static const int phoneNumberMinLength = 10;
  
  /// Turkey country code
  static const String turkeyCountryCode = '+90';
  
  /// Phone number placeholder text
  static const String phoneNumberPlaceholder = 'XXXXXXXXX';

  // ==========================================================================
  // VALIDATION CODE
  // ==========================================================================
  
  /// Length of the validation code
  static const int validationCodeLength = 6;
  
  /// Validation code input type (numeric only)
  static const bool validationCodeDigitsOnly = true;

  // ==========================================================================
  // NAME VALIDATION
  // ==========================================================================
  
  /// Minimum length for full name
  static const int fullNameMinLength = 2;
  
  /// Maximum length for full name
  static const int fullNameMaxLength = 50;

  // ==========================================================================
  // HOBBY SELECTION
  // ==========================================================================
  
  /// Minimum number of hobbies required for registration
  static const int minRequiredHobbies = 2;
  
  /// Maximum number of hobbies that can be displayed initially
  static const int maxInitialHobbiesDisplayed = 5;

  // ==========================================================================
  // REGEX PATTERNS
  // ==========================================================================
  
  /// Regex pattern for numeric input only
  static const String numericOnlyPattern = r'[0-9]';
  
  /// Regex pattern for name validation (letters, spaces, some special chars)
  static const String namePattern = r'^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$';
}
