import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/vibes/controllers/single_vibe_controller.dart';
import 'package:ivent_app/features/vibes/controllers/video_manager.dart';
import 'package:ivent_app/features/vibes/widgets/ia_vibe_widget.dart';

class SingleVibePage extends StatefulWidget {
  final String vibeId;

  const SingleVibePage({super.key, required this.vibeId});

  @override
  State<SingleVibePage> createState() => _SingleVibePageState();
}

class _SingleVibePageState extends State<SingleVibePage> {
  late final SinglePageVibeController _vibeController;

  VideoManager get videoManager => _vibeController.videoManager;

  @override
  void initState() {
    super.initState();
    _vibeController = Get.find(tag: widget.vibeId);
  }

  @override
  void dispose() {
    Get.delete<SinglePageVibeController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) => _vibeController.stopVideo(),
      child: Obx(() {
        final vibe = _vibeController.singleVibe;
        if (vibe == null) return IaScaffold.loadingBlack();
        return IaScaffold.empty(
          body: IaVibeWidget(
            vibe: vibe,
            onLeftSideTap: _vibeController.getPreviousVibe,
            onRightSideTap: _vibeController.getNextVibe,
          ),
        );
      }),
    );
  }
}
