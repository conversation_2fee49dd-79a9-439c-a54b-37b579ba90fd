//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxRoutablePoints {
  /// Returns a new [SearchBoxRoutablePoints] instance.
  SearchBoxRoutablePoints({
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  /// Name of the routable point
  String name;

  /// Latitude coordinate
  double latitude;

  /// Longitude coordinate
  double longitude;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxRoutablePoints &&
    other.name == name &&
    other.latitude == latitude &&
    other.longitude == longitude;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (name.hashCode) +
    (latitude.hashCode) +
    (longitude.hashCode);

  @override
  String toString() => 'SearchBoxRoutablePoints[name=$name, latitude=$latitude, longitude=$longitude]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'name'] = this.name;
      json[r'latitude'] = this.latitude;
      json[r'longitude'] = this.longitude;
    return json;
  }

  /// Returns a new [SearchBoxRoutablePoints] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxRoutablePoints? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxRoutablePoints[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxRoutablePoints[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxRoutablePoints(
        name: mapValueOfType<String>(json, r'name')!,
        latitude: mapValueOfType<double>(json, r'latitude')!,
        longitude: mapValueOfType<double>(json, r'longitude')!,
      );
    }
    return null;
  }

  static List<SearchBoxRoutablePoints> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxRoutablePoints>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxRoutablePoints.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxRoutablePoints> mapFromJson(dynamic json) {
    final map = <String, SearchBoxRoutablePoints>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxRoutablePoints.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxRoutablePoints-objects as value to a dart map
  static Map<String, List<SearchBoxRoutablePoints>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxRoutablePoints>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxRoutablePoints.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'name',
    'latitude',
    'longitude',
  };
}

