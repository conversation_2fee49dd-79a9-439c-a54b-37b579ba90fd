//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class FriendListingTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const FriendListingTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const user = FriendListingTypeEnum._(r'user');
  static const group = FriendListingTypeEnum._(r'group');

  /// List of all possible values in this [enum][FriendListingTypeEnum].
  static const values = <FriendListingTypeEnum>[
    user,
    group,
  ];

  static FriendListingTypeEnum? fromJson(dynamic value) => FriendListingTypeEnumTypeTransformer().decode(value);

  static List<FriendListingTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FriendListingTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FriendListingTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [FriendListingTypeEnum] to String,
/// and [decode] dynamic data back to [FriendListingTypeEnum].
class FriendListingTypeEnumTypeTransformer {
  factory FriendListingTypeEnumTypeTransformer() => _instance ??= const FriendListingTypeEnumTypeTransformer._();

  const FriendListingTypeEnumTypeTransformer._();

  String encode(FriendListingTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a FriendListingTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  FriendListingTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'user': return FriendListingTypeEnum.user;
        case r'group': return FriendListingTypeEnum.group;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [FriendListingTypeEnumTypeTransformer] instance.
  static FriendListingTypeEnumTypeTransformer? _instance;
}

