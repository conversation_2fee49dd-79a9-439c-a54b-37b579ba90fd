# openapi.model.GetPageDetailsByPageIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**description** | **String** | Description of the page | [optional] 
**websiteUrl** | **String** | Website URL for the page | [optional] 
**locationId** | **String** | UUID of the location where the page is based | 
**locationAdress** | **String** | Address of the page location | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



