# openapi.model.HobbyItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**hobbyId** | **String** | UUID of the hobby | 
**hobbyName** | **String** | Name of the hobby (sub category) | 
**parentHobbyId** | **String** | UUID of the parent hobby | 
**parentHobbyName** | **String** | Name of the parent hobby (main category) | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



