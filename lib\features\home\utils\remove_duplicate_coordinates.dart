import 'package:ivent_app/features/mapbox/models/marker_feature.dart';

List<MarkerFeature> removeDuplicateCoordinates(
  List<MarkerFeature> items, {
  List<MarkerFeature> exclude = const [],
}) {
  final Set<String> seenIds = exclude.map((val) => '${val.id}').toSet();
  final List<MarkerFeature> result = [];

  for (final item in items) {
    if (seenIds.contains(item.id)) continue;
    seenIds.add(item.id);
    result.add(item);
  }

  return result;
}
