//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetMemoryFoldersByUserIdReturn {
  /// Returns a new [GetMemoryFoldersByUserIdReturn] instance.
  GetMemoryFoldersByUserIdReturn({
    this.memoryFolders = const [],
    required this.memoryFolderCount,
  });

  /// List of user's memory folders
  List<MemoryFolderCardItem> memoryFolders;

  /// Total number of memory folders
  ///
  /// Minimum value: 0
  int memoryFolderCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetMemoryFoldersByUserIdReturn &&
    _deepEquality.equals(other.memoryFolders, memoryFolders) &&
    other.memoryFolderCount == memoryFolderCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (memoryFolders.hashCode) +
    (memoryFolderCount.hashCode);

  @override
  String toString() => 'GetMemoryFoldersByUserIdReturn[memoryFolders=$memoryFolders, memoryFolderCount=$memoryFolderCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'memoryFolders'] = this.memoryFolders;
      json[r'memoryFolderCount'] = this.memoryFolderCount;
    return json;
  }

  /// Returns a new [GetMemoryFoldersByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetMemoryFoldersByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetMemoryFoldersByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetMemoryFoldersByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetMemoryFoldersByUserIdReturn(
        memoryFolders: MemoryFolderCardItem.listFromJson(json[r'memoryFolders']),
        memoryFolderCount: mapValueOfType<int>(json, r'memoryFolderCount')!,
      );
    }
    return null;
  }

  static List<GetMemoryFoldersByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetMemoryFoldersByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetMemoryFoldersByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetMemoryFoldersByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetMemoryFoldersByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetMemoryFoldersByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetMemoryFoldersByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetMemoryFoldersByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetMemoryFoldersByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetMemoryFoldersByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'memoryFolders',
    'memoryFolderCount',
  };
}

