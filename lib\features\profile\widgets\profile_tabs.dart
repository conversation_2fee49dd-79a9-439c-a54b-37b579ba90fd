import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/widgets/vibe_thumbnail.dart';

/// Widget for displaying profile tabs (Vibes and Memories)
///
/// Shows user's Vibe folders and memory content in a tabbed interface.
/// Handles the display of upcoming events and existing Vibe thumbnails.
class ProfileTabs extends StatelessWidget {
  final ProfileController controller;
  final GetVibeFoldersByUserIdReturn? vibesContext;

  const ProfileTabs({
    super.key,
    required this.controller,
    required this.vibesContext,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: AppDimensions.padding20,
        bottom: 100,
      ),
      child: Tab<PERSON><PERSON><PERSON>iew(
        physics: const NeverScrollableScrollPhysics(),
        children: [
          _buildVibesTab(),
          _buildMemoriesTab(),
        ],
      ),
    );
  }

  // Widget builders

  /// Builds the Vibes tab with grid of Vibe thumbnails
  Widget _buildVibesTab() {
    if (vibesContext == null || vibesContext!.vibeFolders.isEmpty) {
      return Center(
        child: Text('Henüz Vibe oluşturulmadı', style: AppTextStyles.size16RegularTextSecondary),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppDimensions.padding16,
        mainAxisSpacing: AppDimensions.padding16,
        childAspectRatio: 0.6,
      ),
      itemCount: _getVibesItemCount(),
      itemBuilder: (context, index) => _buildVibeItem(index),
    );
  }

  /// Builds the Memories tab (placeholder)
  Widget _buildMemoriesTab() {
    return Center(
      child: Text('Memories yakın zamanda eklenecek...', style: AppTextStyles.size16RegularTextSecondary),
    );
  }

  // Helper methods

  /// Gets the total item count for Vibes grid
  int _getVibesItemCount() {
    if (vibesContext == null) return 0;
    final baseCount = vibesContext!.vibeFolders.length;
    return controller.authService.hasUpcomingEventToday ? baseCount + 1 : baseCount;
  }

  /// Builds individual Vibe item
  Widget _buildVibeItem(int index) {
    if (controller.authService.hasUpcomingEventToday) {
      if (index == 0) {
        // TODO: Implement upcoming event display
        return const SizedBox.shrink();
      } else {
        final vibe = vibesContext!.vibeFolders[index - 1];
        return _buildVibeThumbnail(vibe);
      }
    } else {
      final vibe = vibesContext!.vibeFolders[index];
      return _buildVibeThumbnail(vibe);
    }
  }

  /// Builds a VibeThumbnail widget
  Widget _buildVibeThumbnail(dynamic vibe) {
    return VibeThumbnail(
      vibeId: vibe.vibeId,
      iventName: vibe.iventName,
      participantCount: vibe.memberCount,
      participantNames: vibe.memberFirstnames,
      imageUrl: vibe.thumbnailUrl,
    );
  }
}
