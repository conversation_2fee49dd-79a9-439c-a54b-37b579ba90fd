//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class RemoveCollabByIventIdDto {
  /// Returns a new [RemoveCollabByIventIdDto] instance.
  RemoveCollabByIventIdDto({
    required this.collabId,
    required this.collabType,
  });

  /// UUID of the collaborator to be removed from the ivent
  String collabId;

  AccountTypeEnum collabType;

  @override
  bool operator ==(Object other) => identical(this, other) || other is RemoveCollabByIventIdDto &&
    other.collabId == collabId &&
    other.collabType == collabType;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (collabId.hashCode) +
    (collabType.hashCode);

  @override
  String toString() => 'RemoveCollabByIventIdDto[collabId=$collabId, collabType=$collabType]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'collabId'] = this.collabId;
      json[r'collabType'] = this.collabType;
    return json;
  }

  /// Returns a new [RemoveCollabByIventIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static RemoveCollabByIventIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "RemoveCollabByIventIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "RemoveCollabByIventIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return RemoveCollabByIventIdDto(
        collabId: mapValueOfType<String>(json, r'collabId')!,
        collabType: AccountTypeEnum.fromJson(json[r'collabType'])!,
      );
    }
    return null;
  }

  static List<RemoveCollabByIventIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <RemoveCollabByIventIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = RemoveCollabByIventIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, RemoveCollabByIventIdDto> mapFromJson(dynamic json) {
    final map = <String, RemoveCollabByIventIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = RemoveCollabByIventIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of RemoveCollabByIventIdDto-objects as value to a dart map
  static Map<String, List<RemoveCollabByIventIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<RemoveCollabByIventIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = RemoveCollabByIventIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'collabId',
    'collabType',
  };
}

