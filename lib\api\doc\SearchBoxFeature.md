# openapi.model.SearchBoxFeature

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **String** | Feature type | 
**geometry** | [**SearchBoxGeometry**](SearchBoxGeometry.md) | Geometry of the feature | 
**properties** | [**SearchBoxProperties**](SearchBoxProperties.md) | Properties of the feature | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



