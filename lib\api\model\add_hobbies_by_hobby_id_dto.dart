//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class AddHobbiesByHobbyIdDto {
  /// Returns a new [AddHobbiesByHobbyIdDto] instance.
  AddHobbiesByHobbyIdDto({
    this.hobbyIds = const [],
  });

  /// Array of hobby UUIDs to add to the user's profile
  List<String> hobbyIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is AddHobbiesByHobbyIdDto &&
    _deepEquality.equals(other.hobbyIds, hobbyIds);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (hobbyIds.hashCode);

  @override
  String toString() => 'AddHobbiesByHobbyIdDto[hobbyIds=$hobbyIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'hobbyIds'] = this.hobbyIds;
    return json;
  }

  /// Returns a new [AddHobbiesByHobbyIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static AddHobbiesByHobbyIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "AddHobbiesByHobbyIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "AddHobbiesByHobbyIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return AddHobbiesByHobbyIdDto(
        hobbyIds: json[r'hobbyIds'] is Iterable
            ? (json[r'hobbyIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<AddHobbiesByHobbyIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <AddHobbiesByHobbyIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = AddHobbiesByHobbyIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, AddHobbiesByHobbyIdDto> mapFromJson(dynamic json) {
    final map = <String, AddHobbiesByHobbyIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = AddHobbiesByHobbyIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of AddHobbiesByHobbyIdDto-objects as value to a dart map
  static Map<String, List<AddHobbiesByHobbyIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<AddHobbiesByHobbyIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = AddHobbiesByHobbyIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'hobbyIds',
  };
}

