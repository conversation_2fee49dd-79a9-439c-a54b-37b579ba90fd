//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetUserBannerByUserIdReturn {
  /// Returns a new [GetUserBannerByUserIdReturn] instance.
  GetUserBannerByUserIdReturn({
    required this.userId,
    required this.username,
    this.avatarUrl,
    required this.fullname,
  });

  /// Unique identifier of the user
  String userId;

  /// Username of the user
  String username;

  /// URL to the user's avatar image
  String? avatarUrl;

  /// Full name of the user
  String fullname;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetUserBannerByUserIdReturn &&
    other.userId == userId &&
    other.username == username &&
    other.avatarUrl == avatarUrl &&
    other.fullname == fullname;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userId.hashCode) +
    (username.hashCode) +
    (avatarUrl == null ? 0 : avatarUrl!.hashCode) +
    (fullname.hashCode);

  @override
  String toString() => 'GetUserBannerByUserIdReturn[userId=$userId, username=$username, avatarUrl=$avatarUrl, fullname=$fullname]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'userId'] = this.userId;
      json[r'username'] = this.username;
    if (this.avatarUrl != null) {
      json[r'avatarUrl'] = this.avatarUrl;
    } else {
      json[r'avatarUrl'] = null;
    }
      json[r'fullname'] = this.fullname;
    return json;
  }

  /// Returns a new [GetUserBannerByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetUserBannerByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetUserBannerByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetUserBannerByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetUserBannerByUserIdReturn(
        userId: mapValueOfType<String>(json, r'userId')!,
        username: mapValueOfType<String>(json, r'username')!,
        avatarUrl: mapValueOfType<String>(json, r'avatarUrl'),
        fullname: mapValueOfType<String>(json, r'fullname')!,
      );
    }
    return null;
  }

  static List<GetUserBannerByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetUserBannerByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetUserBannerByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetUserBannerByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetUserBannerByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetUserBannerByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetUserBannerByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetUserBannerByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetUserBannerByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetUserBannerByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'userId',
    'username',
    'fullname',
  };
}

