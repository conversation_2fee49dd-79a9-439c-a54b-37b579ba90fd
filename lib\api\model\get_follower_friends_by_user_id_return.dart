//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetFollowerFriendsByUserIdReturn {
  /// Returns a new [GetFollowerFriendsByUserIdReturn] instance.
  GetFollowerFriendsByUserIdReturn({
    this.friends = const [],
    required this.friendCount,
  });

  /// List of friends who are also followers
  List<UserListItem> friends;

  /// Total number of follower friends
  ///
  /// Minimum value: 0
  int friendCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetFollowerFriendsByUserIdReturn &&
    _deepEquality.equals(other.friends, friends) &&
    other.friendCount == friendCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (friends.hashCode) +
    (friendCount.hashCode);

  @override
  String toString() => 'GetFollowerFriendsByUserIdReturn[friends=$friends, friendCount=$friendCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'friends'] = this.friends;
      json[r'friendCount'] = this.friendCount;
    return json;
  }

  /// Returns a new [GetFollowerFriendsByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetFollowerFriendsByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetFollowerFriendsByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetFollowerFriendsByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetFollowerFriendsByUserIdReturn(
        friends: UserListItem.listFromJson(json[r'friends']),
        friendCount: mapValueOfType<int>(json, r'friendCount')!,
      );
    }
    return null;
  }

  static List<GetFollowerFriendsByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetFollowerFriendsByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetFollowerFriendsByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetFollowerFriendsByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetFollowerFriendsByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetFollowerFriendsByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetFollowerFriendsByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetFollowerFriendsByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetFollowerFriendsByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetFollowerFriendsByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'friends',
    'friendCount',
  };
}

