//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetSuggestedImagesReturn {
  /// Returns a new [GetSuggestedImagesReturn] instance.
  GetSuggestedImagesReturn({
    this.imageUrls = const [],
    required this.imageCount,
  });

  /// List of suggested image URLs
  List<String> imageUrls;

  /// Total number of suggested images
  ///
  /// Minimum value: 0
  int imageCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetSuggestedImagesReturn &&
    _deepEquality.equals(other.imageUrls, imageUrls) &&
    other.imageCount == imageCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (imageUrls.hashCode) +
    (imageCount.hashCode);

  @override
  String toString() => 'GetSuggestedImagesReturn[imageUrls=$imageUrls, imageCount=$imageCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'imageUrls'] = this.imageUrls;
      json[r'imageCount'] = this.imageCount;
    return json;
  }

  /// Returns a new [GetSuggestedImagesReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetSuggestedImagesReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetSuggestedImagesReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetSuggestedImagesReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetSuggestedImagesReturn(
        imageUrls: json[r'imageUrls'] is Iterable
            ? (json[r'imageUrls'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        imageCount: mapValueOfType<int>(json, r'imageCount')!,
      );
    }
    return null;
  }

  static List<GetSuggestedImagesReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetSuggestedImagesReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetSuggestedImagesReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetSuggestedImagesReturn> mapFromJson(dynamic json) {
    final map = <String, GetSuggestedImagesReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetSuggestedImagesReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetSuggestedImagesReturn-objects as value to a dart map
  static Map<String, List<GetSuggestedImagesReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetSuggestedImagesReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetSuggestedImagesReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'imageUrls',
    'imageCount',
  };
}

