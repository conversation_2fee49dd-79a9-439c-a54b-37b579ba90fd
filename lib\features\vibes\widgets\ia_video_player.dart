import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/features/vibes/models/video_vibe_item.dart';
import 'package:video_player/video_player.dart';

class IaVideoPlayer extends StatelessWidget {
  final VideoVibeItem video;

  const IaVideoPlayer({
    super.key,
    required this.video,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!video.isAvailable) return IaLoadingIndicator.white;
      return Center(
        child: AspectRatio(
          aspectRatio: video.controller.value.aspectRatio,
          child: VideoPlayer(video.controller),
        ),
      );
    });
  }
}
