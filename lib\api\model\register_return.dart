//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class RegisterReturn {
  /// Returns a new [RegisterReturn] instance.
  RegisterReturn({
    required this.userId,
    required this.token,
    required this.role,
    required this.username,
    required this.fullname,
    this.avatarUrl,
  });

  /// Unique identifier of the newly registered user
  String userId;

  /// JWT authentication token for the new user
  String token;

  UserRoleEnum role;

  /// Username of the new user
  String username;

  /// Full name of the new user
  String fullname;

  /// URL to the user's avatar image
  String? avatarUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is RegisterReturn &&
    other.userId == userId &&
    other.token == token &&
    other.role == role &&
    other.username == username &&
    other.fullname == fullname &&
    other.avatarUrl == avatarUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userId.hashCode) +
    (token.hashCode) +
    (role.hashCode) +
    (username.hashCode) +
    (fullname.hashCode) +
    (avatarUrl == null ? 0 : avatarUrl!.hashCode);

  @override
  String toString() => 'RegisterReturn[userId=$userId, token=$token, role=$role, username=$username, fullname=$fullname, avatarUrl=$avatarUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'userId'] = this.userId;
      json[r'token'] = this.token;
      json[r'role'] = this.role;
      json[r'username'] = this.username;
      json[r'fullname'] = this.fullname;
    if (this.avatarUrl != null) {
      json[r'avatarUrl'] = this.avatarUrl;
    } else {
      json[r'avatarUrl'] = null;
    }
    return json;
  }

  /// Returns a new [RegisterReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static RegisterReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "RegisterReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "RegisterReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return RegisterReturn(
        userId: mapValueOfType<String>(json, r'userId')!,
        token: mapValueOfType<String>(json, r'token')!,
        role: UserRoleEnum.fromJson(json[r'role'])!,
        username: mapValueOfType<String>(json, r'username')!,
        fullname: mapValueOfType<String>(json, r'fullname')!,
        avatarUrl: mapValueOfType<String>(json, r'avatarUrl'),
      );
    }
    return null;
  }

  static List<RegisterReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <RegisterReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = RegisterReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, RegisterReturn> mapFromJson(dynamic json) {
    final map = <String, RegisterReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = RegisterReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of RegisterReturn-objects as value to a dart map
  static Map<String, List<RegisterReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<RegisterReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = RegisterReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'userId',
    'token',
    'role',
    'username',
    'fullname',
  };
}

