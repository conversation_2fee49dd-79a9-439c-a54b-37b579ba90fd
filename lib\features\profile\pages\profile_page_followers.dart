import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/app/routes/profile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/followers_controller.dart';

class ProfilePageFollowers extends StatelessWidget {
  final String userId;

  const ProfilePageFollowers({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    final FollowersController followersController = controller.followersController;

    return IaScaffold.search(
      title: 'Takipçiler',
      textEditingController: followersController.textEditingController,
      body: Obx(() {
        final followersResult = followersController.followersResult;
        return IaSearchPlaceholder(
          entityType: 'Takipçi',
          isSearching: followersController.isSearching,
          isQueryEmpty: followersController.isQueryEmpty,
          isResultsEmpty: followersController.isResultsEmpty,
          isDefaultStateEmpty: false,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: followersResult!.followerCount,
              itemBuilder: (context, index) {
                final follower = followersResult.followers[index];
                return IaListTile.withImageUrl(
                  avatarUrl: follower.avatarUrl,
                  title: '@${follower.username}',
                  subtitle: follower.university,
                  onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: follower.userId),
                  trailing: SharedButtons.moreVertical(),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }
}
