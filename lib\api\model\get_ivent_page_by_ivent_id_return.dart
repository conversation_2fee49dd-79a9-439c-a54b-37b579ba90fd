//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetIventPageByIventIdReturn {
  /// Returns a new [GetIventPageByIventIdReturn] instance.
  GetIventPageByIventIdReturn({
    required this.iventId,
    required this.iventName,
    this.thumbnailUrl,
    required this.locationId,
    required this.mapboxId,
    required this.locationName,
    this.dates = const [],
    this.description,
    required this.categoryTag,
    this.tagNames = const [],
    required this.creatorId,
    required this.creatorType,
    required this.creatorUsername,
    this.creatorImageUrl,
    this.collabNames = const [],
    required this.collabCount,
    this.memberFirstnames = const [],
    this.memberAvatarUrls = const [],
    required this.memberCount,
    this.isFavorited,
    this.favoriteCount,
    this.googleFormsUrl,
    this.instagramUsername,
    this.whatsappUrl,
    this.whatsappNumber,
    this.isWhatsappUrlPrivate,
    this.callNumber,
    this.websiteUrl,
    required this.viewType,
  });

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// URL to the ivent thumbnail image
  String? thumbnailUrl;

  /// UUID of the location
  String locationId;

  /// Mapbox place ID for the location
  String mapboxId;

  /// Name of the location
  String locationName;

  /// List of dates for the ivent, in ISO 8601 date-time format
  List<String> dates;

  /// Detailed description of the ivent
  String? description;

  /// Name of the category tag
  String categoryTag;

  /// List of hobby tags associated with the ivent
  List<String> tagNames;

  /// UUID of the ivent creator
  String creatorId;

  IventCreatorTypeEnum creatorType;

  /// Username of the ivent creator
  String creatorUsername;

  /// URL to the ivent creator image
  String? creatorImageUrl;

  /// List of collaborator names, either page names or usernames
  List<String> collabNames;

  /// Number of collaborators
  ///
  /// Minimum value: 0
  int collabCount;

  /// List of member first names
  List<String>? memberFirstnames;

  /// List of member avatar URLs
  List<String> memberAvatarUrls;

  /// Number of members
  ///
  /// Minimum value: 0
  int memberCount;

  /// Whether the ivent is favorited by the current user
  bool? isFavorited;

  /// Number of favorites the ivent has
  ///
  /// Minimum value: 0
  int? favoriteCount;

  /// URL to Google Forms for registration
  String? googleFormsUrl;

  /// Instagram username for the ivent
  String? instagramUsername;

  /// WhatsApp group URL
  String? whatsappUrl;

  /// WhatsApp contact number
  String? whatsappNumber;

  /// Whether the WhatsApp URL should be kept private
  bool? isWhatsappUrlPrivate;

  /// Phone number for calls
  String? callNumber;

  /// Website URL for the ivent
  String? websiteUrl;

  IventViewTypeEnum viewType;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetIventPageByIventIdReturn &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.locationId == locationId &&
    other.mapboxId == mapboxId &&
    other.locationName == locationName &&
    _deepEquality.equals(other.dates, dates) &&
    other.description == description &&
    other.categoryTag == categoryTag &&
    _deepEquality.equals(other.tagNames, tagNames) &&
    other.creatorId == creatorId &&
    other.creatorType == creatorType &&
    other.creatorUsername == creatorUsername &&
    other.creatorImageUrl == creatorImageUrl &&
    _deepEquality.equals(other.collabNames, collabNames) &&
    other.collabCount == collabCount &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames) &&
    _deepEquality.equals(other.memberAvatarUrls, memberAvatarUrls) &&
    other.memberCount == memberCount &&
    other.isFavorited == isFavorited &&
    other.favoriteCount == favoriteCount &&
    other.googleFormsUrl == googleFormsUrl &&
    other.instagramUsername == instagramUsername &&
    other.whatsappUrl == whatsappUrl &&
    other.whatsappNumber == whatsappNumber &&
    other.isWhatsappUrlPrivate == isWhatsappUrlPrivate &&
    other.callNumber == callNumber &&
    other.websiteUrl == websiteUrl &&
    other.viewType == viewType;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (iventId.hashCode) +
    (iventName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (locationId.hashCode) +
    (mapboxId.hashCode) +
    (locationName.hashCode) +
    (dates.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (categoryTag.hashCode) +
    (tagNames.hashCode) +
    (creatorId.hashCode) +
    (creatorType.hashCode) +
    (creatorUsername.hashCode) +
    (creatorImageUrl == null ? 0 : creatorImageUrl!.hashCode) +
    (collabNames.hashCode) +
    (collabCount.hashCode) +
    (memberFirstnames == null ? 0 : memberFirstnames!.hashCode) +
    (memberAvatarUrls.hashCode) +
    (memberCount.hashCode) +
    (isFavorited == null ? 0 : isFavorited!.hashCode) +
    (favoriteCount == null ? 0 : favoriteCount!.hashCode) +
    (googleFormsUrl == null ? 0 : googleFormsUrl!.hashCode) +
    (instagramUsername == null ? 0 : instagramUsername!.hashCode) +
    (whatsappUrl == null ? 0 : whatsappUrl!.hashCode) +
    (whatsappNumber == null ? 0 : whatsappNumber!.hashCode) +
    (isWhatsappUrlPrivate == null ? 0 : isWhatsappUrlPrivate!.hashCode) +
    (callNumber == null ? 0 : callNumber!.hashCode) +
    (websiteUrl == null ? 0 : websiteUrl!.hashCode) +
    (viewType.hashCode);

  @override
  String toString() => 'GetIventPageByIventIdReturn[iventId=$iventId, iventName=$iventName, thumbnailUrl=$thumbnailUrl, locationId=$locationId, mapboxId=$mapboxId, locationName=$locationName, dates=$dates, description=$description, categoryTag=$categoryTag, tagNames=$tagNames, creatorId=$creatorId, creatorType=$creatorType, creatorUsername=$creatorUsername, creatorImageUrl=$creatorImageUrl, collabNames=$collabNames, collabCount=$collabCount, memberFirstnames=$memberFirstnames, memberAvatarUrls=$memberAvatarUrls, memberCount=$memberCount, isFavorited=$isFavorited, favoriteCount=$favoriteCount, googleFormsUrl=$googleFormsUrl, instagramUsername=$instagramUsername, whatsappUrl=$whatsappUrl, whatsappNumber=$whatsappNumber, isWhatsappUrlPrivate=$isWhatsappUrlPrivate, callNumber=$callNumber, websiteUrl=$websiteUrl, viewType=$viewType]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'locationId'] = this.locationId;
      json[r'mapboxId'] = this.mapboxId;
      json[r'locationName'] = this.locationName;
      json[r'dates'] = this.dates;
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'categoryTag'] = this.categoryTag;
      json[r'tagNames'] = this.tagNames;
      json[r'creatorId'] = this.creatorId;
      json[r'creatorType'] = this.creatorType;
      json[r'creatorUsername'] = this.creatorUsername;
    if (this.creatorImageUrl != null) {
      json[r'creatorImageUrl'] = this.creatorImageUrl;
    } else {
      json[r'creatorImageUrl'] = null;
    }
      json[r'collabNames'] = this.collabNames;
      json[r'collabCount'] = this.collabCount;
    if (this.memberFirstnames != null) {
      json[r'memberFirstnames'] = this.memberFirstnames;
    } else {
      json[r'memberFirstnames'] = null;
    }
      json[r'memberAvatarUrls'] = this.memberAvatarUrls;
      json[r'memberCount'] = this.memberCount;
    if (this.isFavorited != null) {
      json[r'isFavorited'] = this.isFavorited;
    } else {
      json[r'isFavorited'] = null;
    }
    if (this.favoriteCount != null) {
      json[r'favoriteCount'] = this.favoriteCount;
    } else {
      json[r'favoriteCount'] = null;
    }
    if (this.googleFormsUrl != null) {
      json[r'googleFormsUrl'] = this.googleFormsUrl;
    } else {
      json[r'googleFormsUrl'] = null;
    }
    if (this.instagramUsername != null) {
      json[r'instagramUsername'] = this.instagramUsername;
    } else {
      json[r'instagramUsername'] = null;
    }
    if (this.whatsappUrl != null) {
      json[r'whatsappUrl'] = this.whatsappUrl;
    } else {
      json[r'whatsappUrl'] = null;
    }
    if (this.whatsappNumber != null) {
      json[r'whatsappNumber'] = this.whatsappNumber;
    } else {
      json[r'whatsappNumber'] = null;
    }
    if (this.isWhatsappUrlPrivate != null) {
      json[r'isWhatsappUrlPrivate'] = this.isWhatsappUrlPrivate;
    } else {
      json[r'isWhatsappUrlPrivate'] = null;
    }
    if (this.callNumber != null) {
      json[r'callNumber'] = this.callNumber;
    } else {
      json[r'callNumber'] = null;
    }
    if (this.websiteUrl != null) {
      json[r'websiteUrl'] = this.websiteUrl;
    } else {
      json[r'websiteUrl'] = null;
    }
      json[r'viewType'] = this.viewType;
    return json;
  }

  /// Returns a new [GetIventPageByIventIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetIventPageByIventIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetIventPageByIventIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetIventPageByIventIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetIventPageByIventIdReturn(
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        locationId: mapValueOfType<String>(json, r'locationId')!,
        mapboxId: mapValueOfType<String>(json, r'mapboxId')!,
        locationName: mapValueOfType<String>(json, r'locationName')!,
        dates: json[r'dates'] is Iterable
            ? (json[r'dates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        description: mapValueOfType<String>(json, r'description'),
        categoryTag: mapValueOfType<String>(json, r'categoryTag')!,
        tagNames: json[r'tagNames'] is Iterable
            ? (json[r'tagNames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        creatorId: mapValueOfType<String>(json, r'creatorId')!,
        creatorType: IventCreatorTypeEnum.fromJson(json[r'creatorType'])!,
        creatorUsername: mapValueOfType<String>(json, r'creatorUsername')!,
        creatorImageUrl: mapValueOfType<String>(json, r'creatorImageUrl'),
        collabNames: json[r'collabNames'] is Iterable
            ? (json[r'collabNames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        collabCount: mapValueOfType<int>(json, r'collabCount')!,
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberAvatarUrls: json[r'memberAvatarUrls'] is Iterable
            ? (json[r'memberAvatarUrls'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
        isFavorited: mapValueOfType<bool>(json, r'isFavorited'),
        favoriteCount: mapValueOfType<int>(json, r'favoriteCount'),
        googleFormsUrl: mapValueOfType<String>(json, r'googleFormsUrl'),
        instagramUsername: mapValueOfType<String>(json, r'instagramUsername'),
        whatsappUrl: mapValueOfType<String>(json, r'whatsappUrl'),
        whatsappNumber: mapValueOfType<String>(json, r'whatsappNumber'),
        isWhatsappUrlPrivate: mapValueOfType<bool>(json, r'isWhatsappUrlPrivate'),
        callNumber: mapValueOfType<String>(json, r'callNumber'),
        websiteUrl: mapValueOfType<String>(json, r'websiteUrl'),
        viewType: IventViewTypeEnum.fromJson(json[r'viewType'])!,
      );
    }
    return null;
  }

  static List<GetIventPageByIventIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetIventPageByIventIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetIventPageByIventIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetIventPageByIventIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetIventPageByIventIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetIventPageByIventIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetIventPageByIventIdReturn-objects as value to a dart map
  static Map<String, List<GetIventPageByIventIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetIventPageByIventIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetIventPageByIventIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'iventId',
    'iventName',
    'locationId',
    'mapboxId',
    'locationName',
    'dates',
    'categoryTag',
    'tagNames',
    'creatorId',
    'creatorType',
    'creatorUsername',
    'collabNames',
    'collabCount',
    'memberAvatarUrls',
    'memberCount',
    'viewType',
  };
}

