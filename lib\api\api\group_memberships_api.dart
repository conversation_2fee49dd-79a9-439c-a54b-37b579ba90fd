//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class GroupMembershipsApi {
  GroupMembershipsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [AddModeratorByGroupIdDto] addModeratorByGroupIdDto (required):
  Future<Response> addModeratorByGroupIdWithHttpInfo(String groupId, AddModeratorByGroupIdDto addModeratorByGroupIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/administration/add'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody = addModeratorByGroupIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [AddModeratorByGroupIdDto] addModeratorByGroupIdDto (required):
  Future<void> addModeratorByGroupId(String groupId, AddModeratorByGroupIdDto addModeratorByGroupIdDto,) async {
    final response = await addModeratorByGroupIdWithHttpInfo(groupId, addModeratorByGroupIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Grubun IDsi ile bir hesaba davet gönderilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [InviteMembersByGroupIdDto] inviteMembersByGroupIdDto (required):
  Future<Response> inviteMembersByGroupIdWithHttpInfo(String groupId, InviteMembersByGroupIdDto inviteMembersByGroupIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/invite'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody = inviteMembersByGroupIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile bir hesaba davet gönderilir
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [InviteMembersByGroupIdDto] inviteMembersByGroupIdDto (required):
  Future<void> inviteMembersByGroupId(String groupId, InviteMembersByGroupIdDto inviteMembersByGroupIdDto,) async {
    final response = await inviteMembersByGroupIdWithHttpInfo(groupId, inviteMembersByGroupIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Grubun IDsi ile arkadaş grubundan ayrılınır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  Future<Response> leaveGroupByGroupIdWithHttpInfo(String groupId,) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/leave'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile arkadaş grubundan ayrılınır
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  Future<void> leaveGroupByGroupId(String groupId,) async {
    final response = await leaveGroupByGroupIdWithHttpInfo(groupId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Grubun IDsi ile bir hesap gruptan çıkartılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [RemoveMemberByGroupIdDto] removeMemberByGroupIdDto (required):
  Future<Response> removeMemberByGroupIdWithHttpInfo(String groupId, RemoveMemberByGroupIdDto removeMemberByGroupIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/remove'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody = removeMemberByGroupIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile bir hesap gruptan çıkartılır
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [RemoveMemberByGroupIdDto] removeMemberByGroupIdDto (required):
  Future<void> removeMemberByGroupId(String groupId, RemoveMemberByGroupIdDto removeMemberByGroupIdDto,) async {
    final response = await removeMemberByGroupIdWithHttpInfo(groupId, removeMemberByGroupIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [RemoveModeratorByGroupIdDto] removeModeratorByGroupIdDto (required):
  Future<Response> removeModeratorByGroupIdWithHttpInfo(String groupId, RemoveModeratorByGroupIdDto removeModeratorByGroupIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/administration/remove'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody = removeModeratorByGroupIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [RemoveModeratorByGroupIdDto] removeModeratorByGroupIdDto (required):
  Future<void> removeModeratorByGroupId(String groupId, RemoveModeratorByGroupIdDto removeModeratorByGroupIdDto,) async {
    final response = await removeModeratorByGroupIdWithHttpInfo(groupId, removeModeratorByGroupIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Arkadaş grubundaki üyeleri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchGroupMembersByGroupIdWithHttpInfo(String groupId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/search'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Arkadaş grubundaki üyeleri listeler
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchGroupMembersByGroupIdReturn?> searchGroupMembersByGroupId(String groupId, { String? q, int? limit, int? page, }) async {
    final response = await searchGroupMembersByGroupIdWithHttpInfo(groupId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchGroupMembersByGroupIdReturn',) as SearchGroupMembersByGroupIdReturn;
    
    }
    return null;
  }

  /// Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchInvitableUsersByGroupIdWithHttpInfo(String groupId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/search/{groupId}'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchUsersForGroupCreationReturn?> searchInvitableUsersByGroupId(String groupId, { String? q, int? limit, int? page, }) async {
    final response = await searchInvitableUsersByGroupIdWithHttpInfo(groupId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchUsersForGroupCreationReturn',) as SearchUsersForGroupCreationReturn;
    
    }
    return null;
  }

  /// Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchUsersForGroupCreationWithHttpInfo({ String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/search';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchUsersForGroupCreationReturn?> searchUsersForGroupCreation({ String? q, int? limit, int? page, }) async {
    final response = await searchUsersForGroupCreationWithHttpInfo( q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchUsersForGroupCreationReturn',) as SearchUsersForGroupCreationReturn;
    
    }
    return null;
  }

  /// Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği devreder
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [TransferAdministrationByGroupIdDto] transferAdministrationByGroupIdDto (required):
  Future<Response> transferAdministrationByGroupIdWithHttpInfo(String groupId, TransferAdministrationByGroupIdDto transferAdministrationByGroupIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/groupMemberships/{groupId}/administration/transfer'
      .replaceAll('{groupId}', groupId);

    // ignore: prefer_final_locals
    Object? postBody = transferAdministrationByGroupIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği devreder
  ///
  /// Parameters:
  ///
  /// * [String] groupId (required):
  ///
  /// * [TransferAdministrationByGroupIdDto] transferAdministrationByGroupIdDto (required):
  Future<void> transferAdministrationByGroupId(String groupId, TransferAdministrationByGroupIdDto transferAdministrationByGroupIdDto,) async {
    final response = await transferAdministrationByGroupIdWithHttpInfo(groupId, transferAdministrationByGroupIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

