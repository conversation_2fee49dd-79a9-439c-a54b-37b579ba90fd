//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxSuggestFeature {
  /// Returns a new [SearchBoxSuggestFeature] instance.
  SearchBoxSuggestFeature({
    required this.name,
    this.namePreferred,
    required this.mapboxId,
    required this.featureType,
    this.address,
    this.fullAddress,
    required this.placeFormatted,
    required this.context,
    required this.language,
    this.maki,
    this.poiCategory = const [],
    this.poiCategoryIds = const [],
    this.brand = const [],
    this.brandId = const [],
    this.externalIds,
    this.metadata,
    this.distance,
    this.eta,
    this.addedDistance,
    this.addedTime,
  });

  /// Name of the place
  String name;

  /// Preferred name of the place
  String? namePreferred;

  /// Mapbox ID of the place
  String mapboxId;

  /// Feature type
  String featureType;

  /// Address of the place
  String? address;

  /// Full address of the place
  String? fullAddress;

  /// Formatted place description
  String placeFormatted;

  /// Context information about the place
  SearchBoxContext context;

  /// Language of the result
  String language;

  /// Maki icon identifier
  String? maki;

  /// POI categories
  List<String>? poiCategory;

  /// POI category IDs
  List<String>? poiCategoryIds;

  /// Brand names
  List<String>? brand;

  /// Brand IDs
  List<String>? brandId;

  /// External IDs mapping
  Object? externalIds;

  /// Additional metadata
  Object? metadata;

  /// Distance in meters
  ///
  /// Minimum value: 0
  int? distance;

  /// Estimated time of arrival in minutes
  double? eta;

  /// Added distance in meters
  double? addedDistance;

  /// Added time in minutes
  double? addedTime;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxSuggestFeature &&
    other.name == name &&
    other.namePreferred == namePreferred &&
    other.mapboxId == mapboxId &&
    other.featureType == featureType &&
    other.address == address &&
    other.fullAddress == fullAddress &&
    other.placeFormatted == placeFormatted &&
    other.context == context &&
    other.language == language &&
    other.maki == maki &&
    _deepEquality.equals(other.poiCategory, poiCategory) &&
    _deepEquality.equals(other.poiCategoryIds, poiCategoryIds) &&
    _deepEquality.equals(other.brand, brand) &&
    _deepEquality.equals(other.brandId, brandId) &&
    other.externalIds == externalIds &&
    other.metadata == metadata &&
    other.distance == distance &&
    other.eta == eta &&
    other.addedDistance == addedDistance &&
    other.addedTime == addedTime;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (name.hashCode) +
    (namePreferred == null ? 0 : namePreferred!.hashCode) +
    (mapboxId.hashCode) +
    (featureType.hashCode) +
    (address == null ? 0 : address!.hashCode) +
    (fullAddress == null ? 0 : fullAddress!.hashCode) +
    (placeFormatted.hashCode) +
    (context.hashCode) +
    (language.hashCode) +
    (maki == null ? 0 : maki!.hashCode) +
    (poiCategory == null ? 0 : poiCategory!.hashCode) +
    (poiCategoryIds == null ? 0 : poiCategoryIds!.hashCode) +
    (brand == null ? 0 : brand!.hashCode) +
    (brandId == null ? 0 : brandId!.hashCode) +
    (externalIds == null ? 0 : externalIds!.hashCode) +
    (metadata == null ? 0 : metadata!.hashCode) +
    (distance == null ? 0 : distance!.hashCode) +
    (eta == null ? 0 : eta!.hashCode) +
    (addedDistance == null ? 0 : addedDistance!.hashCode) +
    (addedTime == null ? 0 : addedTime!.hashCode);

  @override
  String toString() => 'SearchBoxSuggestFeature[name=$name, namePreferred=$namePreferred, mapboxId=$mapboxId, featureType=$featureType, address=$address, fullAddress=$fullAddress, placeFormatted=$placeFormatted, context=$context, language=$language, maki=$maki, poiCategory=$poiCategory, poiCategoryIds=$poiCategoryIds, brand=$brand, brandId=$brandId, externalIds=$externalIds, metadata=$metadata, distance=$distance, eta=$eta, addedDistance=$addedDistance, addedTime=$addedTime]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'name'] = this.name;
    if (this.namePreferred != null) {
      json[r'name_preferred'] = this.namePreferred;
    } else {
      json[r'name_preferred'] = null;
    }
      json[r'mapbox_id'] = this.mapboxId;
      json[r'feature_type'] = this.featureType;
    if (this.address != null) {
      json[r'address'] = this.address;
    } else {
      json[r'address'] = null;
    }
    if (this.fullAddress != null) {
      json[r'full_address'] = this.fullAddress;
    } else {
      json[r'full_address'] = null;
    }
      json[r'place_formatted'] = this.placeFormatted;
      json[r'context'] = this.context;
      json[r'language'] = this.language;
    if (this.maki != null) {
      json[r'maki'] = this.maki;
    } else {
      json[r'maki'] = null;
    }
    if (this.poiCategory != null) {
      json[r'poi_category'] = this.poiCategory;
    } else {
      json[r'poi_category'] = null;
    }
    if (this.poiCategoryIds != null) {
      json[r'poi_category_ids'] = this.poiCategoryIds;
    } else {
      json[r'poi_category_ids'] = null;
    }
    if (this.brand != null) {
      json[r'brand'] = this.brand;
    } else {
      json[r'brand'] = null;
    }
    if (this.brandId != null) {
      json[r'brand_id'] = this.brandId;
    } else {
      json[r'brand_id'] = null;
    }
    if (this.externalIds != null) {
      json[r'external_ids'] = this.externalIds;
    } else {
      json[r'external_ids'] = null;
    }
    if (this.metadata != null) {
      json[r'metadata'] = this.metadata;
    } else {
      json[r'metadata'] = null;
    }
    if (this.distance != null) {
      json[r'distance'] = this.distance;
    } else {
      json[r'distance'] = null;
    }
    if (this.eta != null) {
      json[r'eta'] = this.eta;
    } else {
      json[r'eta'] = null;
    }
    if (this.addedDistance != null) {
      json[r'added_distance'] = this.addedDistance;
    } else {
      json[r'added_distance'] = null;
    }
    if (this.addedTime != null) {
      json[r'added_time'] = this.addedTime;
    } else {
      json[r'added_time'] = null;
    }
    return json;
  }

  /// Returns a new [SearchBoxSuggestFeature] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxSuggestFeature? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxSuggestFeature[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxSuggestFeature[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxSuggestFeature(
        name: mapValueOfType<String>(json, r'name')!,
        namePreferred: mapValueOfType<String>(json, r'name_preferred'),
        mapboxId: mapValueOfType<String>(json, r'mapbox_id')!,
        featureType: mapValueOfType<String>(json, r'feature_type')!,
        address: mapValueOfType<String>(json, r'address'),
        fullAddress: mapValueOfType<String>(json, r'full_address'),
        placeFormatted: mapValueOfType<String>(json, r'place_formatted')!,
        context: SearchBoxContext.fromJson(json[r'context'])!,
        language: mapValueOfType<String>(json, r'language')!,
        maki: mapValueOfType<String>(json, r'maki'),
        poiCategory: json[r'poi_category'] is Iterable
            ? (json[r'poi_category'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        poiCategoryIds: json[r'poi_category_ids'] is Iterable
            ? (json[r'poi_category_ids'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        brand: json[r'brand'] is Iterable
            ? (json[r'brand'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        brandId: json[r'brand_id'] is Iterable
            ? (json[r'brand_id'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        externalIds: mapValueOfType<Object>(json, r'external_ids'),
        metadata: mapValueOfType<Object>(json, r'metadata'),
        distance: mapValueOfType<int>(json, r'distance'),
        eta: mapValueOfType<double>(json, r'eta'),
        addedDistance: mapValueOfType<double>(json, r'added_distance'),
        addedTime: mapValueOfType<double>(json, r'added_time'),
      );
    }
    return null;
  }

  static List<SearchBoxSuggestFeature> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxSuggestFeature>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxSuggestFeature.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxSuggestFeature> mapFromJson(dynamic json) {
    final map = <String, SearchBoxSuggestFeature>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxSuggestFeature.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxSuggestFeature-objects as value to a dart map
  static Map<String, List<SearchBoxSuggestFeature>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxSuggestFeature>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxSuggestFeature.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'name',
    'mapbox_id',
    'feature_type',
    'place_formatted',
    'context',
    'language',
  };
}

