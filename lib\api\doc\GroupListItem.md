# openapi.model.GroupListItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groupId** | **String** | UUID of the group | 
**groupName** | **String** | Name of the group | 
**thumbnailUrl** | **String** | URL to the group thumbnail image | [optional] 
**memberFirstnames** | **List<String>** | List of member's first names in the group | [default to const []]
**memberCount** | **int** | Number of members in the group | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



