//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class AddPageMembersByPageIdDto {
  /// Returns a new [AddPageMembersByPageIdDto] instance.
  AddPageMembersByPageIdDto({
    this.userIds = const [],
  });

  /// Array of user UUIDs to add as page members
  List<String> userIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is AddPageMembersByPageIdDto &&
    _deepEquality.equals(other.userIds, userIds);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userIds.hashCode);

  @override
  String toString() => 'AddPageMembersByPageIdDto[userIds=$userIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'userIds'] = this.userIds;
    return json;
  }

  /// Returns a new [AddPageMembersByPageIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static AddPageMembersByPageIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "AddPageMembersByPageIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "AddPageMembersByPageIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return AddPageMembersByPageIdDto(
        userIds: json[r'userIds'] is Iterable
            ? (json[r'userIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
      );
    }
    return null;
  }

  static List<AddPageMembersByPageIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <AddPageMembersByPageIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = AddPageMembersByPageIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, AddPageMembersByPageIdDto> mapFromJson(dynamic json) {
    final map = <String, AddPageMembersByPageIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = AddPageMembersByPageIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of AddPageMembersByPageIdDto-objects as value to a dart map
  static Map<String, List<AddPageMembersByPageIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<AddPageMembersByPageIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = AddPageMembersByPageIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'userIds',
  };
}

