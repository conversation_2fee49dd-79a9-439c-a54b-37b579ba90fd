/// Authentication feature string constants
///
/// Centralized string constants for the authentication feature following
/// clean architecture principles. Organized by functional areas for better
/// maintainability and localization support.
class AuthStrings {
  AuthStrings._(); // Private constructor to prevent instantiation

  // ==========================================================================
  // BUTTON TEXTS
  // ==========================================================================

  /// Skip button text
  static const String atla = 'Atla';

  /// Continue button text
  static const String devamEt = 'Devam Et';

  /// Complete membership button text
  static const String uyeligiTamamla = 'Üyeliğ<PERSON>';

  /// Start immediately button text
  static const String hemenBaslat = 'Hemen Başlat!';

  /// Invite button text
  static const String davetEt = 'Davet Et';

  /// Resend button text
  static const String tekrarGonder = 'Tekrar Gönder';

  /// Cancel button text
  static const String iptal = 'İptal';

  // ==========================================================================
  // EXPANDABLE CONTENT
  // ==========================================================================

  /// Show more button text
  static const String dahaFazlaGoster = 'Daha Fazla Göster';

  /// Show less button text
  static const String dahaAzGoster = 'Daha Az Göster';

  // ==========================================================================
  // STATUS MESSAGES
  // ==========================================================================

  /// Request sent status message
  static const String istekGonderildi = 'İstek Gönderildi';

  // ==========================================================================
  // FORM LABELS & PLACEHOLDERS
  // ==========================================================================

  /// Turkey area code
  static const String alanCode = '+90';

  /// Full name field label
  static const String adSoyad = 'Adınız Soyadınız';

  /// Interest area requirement label
  static const String ilgiAlani = 'En Az 2 İlgi Alanı';

  /// Contacts section label
  static const String kisileriniz = 'Kişileriniz';

  /// Search placeholder text
  static const String arama = 'Arama';

  // ==========================================================================
  // INFORMATIONAL MESSAGES
  // ==========================================================================

  /// Phone number entry instruction
  static const String iletisimNumGirerek = 'İletişim numaranı girerek\nseçkin etkinliklere katılabilirsin';

  /// Validation code entry instruction
  static const String koduGirebilirsin = 'İletişim numarana gönderdiğimiz\nkodu aşağıya girebilirsin.';

  /// Getting to know user message
  static const String tanisalim = 'Etkinliklere göz atmadan\nönce tanışalım';

  /// Interest areas explanation
  static const String ilgiAlaniText =
      'İlgi alanlarınızı belirleyerek gitmek isteyebileceğiniz etkinlikleri sizin için listeleyeceğiz.';

  /// Contacts permission explanation
  static const String erisimIzin = 'Kişilerinize erişebilmemiz için ayarlar zart zurt...';

  // ==========================================================================
  // ERROR MESSAGES
  // ==========================================================================

  /// Validation code error message
  static const String onayKoduHata =
      'Girdiğiniz kod mail adresinize gönderilen\nkod ile eşleşmedi. Lütfen tekrar kontrol ediniz.';

  // ==========================================================================
  // ONBOARDING CONTENT
  // ==========================================================================

  /// First onboarding screen text elements
  static const List<String> onboarding1 = [
    'Tüm',
    'Etkinlikler',
    'Tek',
    'Haritada!',
  ];

  /// Second onboarding screen text elements
  static const List<String> onboarding2 = ['Etkinlik', 'Keşfetmek', 'Çok', 'Kolay!'];

  /// Third onboarding screen text elements
  static const List<String> onboarding3 = ['Arkadaşlarınla', 'Birlikte', 'Etkinliklere', 'Katıl!'];
}
