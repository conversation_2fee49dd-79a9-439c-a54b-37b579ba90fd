import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class HobbyFilterCategoryBox extends StatefulWidget {
  final void Function(String hobbyId) onHobbyToggle;
  final List<String> selectedHobbyIds;
  final String mainCategory;
  final List<Hobby> hobbyList;
  final HomeController controller;

  const HobbyFilterCategoryBox({
    super.key,
    required this.onHobbyToggle,
    required this.selectedHobbyIds,
    required this.mainCategory,
    required this.hobbyList,
    required this.controller,
  });

  @override
  State<HobbyFilterCategoryBox> createState() => _HobbyFilterCategoryBoxState();
}

class _HobbyFilterCategoryBoxState extends State<HobbyFilterCategoryBox> {
  bool _isExpanded = false;

  void Function(String hobbyId) get _onHobbyToggle => widget.onHobbyToggle;
  List<String> get _selectedHobbyIds => widget.selectedHobbyIds;
  String get _mainCategory => widget.mainCategory;
  List<Hobby> get _hobbyList => widget.hobbyList;
  HomeController get _controller => widget.controller;
  bool get _isEverythingSelected => _hobbyList.every((hobby) => _selectedHobbyIds.contains(hobby.hobbyId));

  void _toggleExpandedCategory() => setState(() => _isExpanded = !_isExpanded);

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCategoryTitle(),
          if (_isExpanded) const SizedBox(height: AppDimensions.padding12),
          if (_isExpanded) _buildHobbyTags(),
        ],
      ),
    );
  }

  Widget _buildCategoryTitle() {
    return IaListTile.withSvgIcon(
      iconPath: AppAssets.book,
      title: _mainCategory,
      trailing: Row(
        children: [
          SharedButtons.checkBox(
            isSelected: _isEverythingSelected,
            onTap: () {
              if (_isEverythingSelected) {
                for (final hobby in _hobbyList) {
                  _controller.filterController.toggleHobbySelection(hobby.hobbyId);
                }
              } else {
                for (final hobby in _hobbyList) {
                  if (!_selectedHobbyIds.contains(hobby.hobbyId)) {
                    _controller.filterController.toggleHobbySelection(hobby.hobbyId);
                  }
                }
              }
            },
          ),
          const SizedBox(width: AppDimensions.padding12),
          HomeButtons.expandCategory(onTap: _toggleExpandedCategory, isExpanded: _isExpanded),
        ],
      ),
    );
  }

  Widget _buildHobbyTags() {
    const int maxInitialDisplay = AuthValidationConstants.maxInitialHobbiesDisplayed;
    final int displayCount = _isExpanded ? _hobbyList.length : maxInitialDisplay;

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      spacing: AppDimensions.padding8,
      runSpacing: AppDimensions.padding8,
      children: _hobbyList
          .take(displayCount)
          .map((hobby) => HomeButtons.filterAvailableHobbyTag(
                onTap: () => _onHobbyToggle(hobby.hobbyId),
                text: hobby.hobbyName,
                isSelected: _selectedHobbyIds.contains(hobby.hobbyId),
              ))
          .toList(),
    );
  }
}
