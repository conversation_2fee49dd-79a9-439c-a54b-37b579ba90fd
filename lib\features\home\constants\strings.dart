class Lists {
  static List<filter> timeFilter = [
    filter('Zaman Aralığı Seç', false),
    filter('<PERSON><PERSON>', false),
    filter('<PERSON><PERSON><PERSON><PERSON>', true),
    filter('<PERSON><PERSON><PERSON><PERSON>', false),
    filter('<PERSON>u Hafta', false),
    filter('<PERSON><PERSON><PERSON>', false),
    filter('<PERSON>u A<PERSON>', false),
    filter('<PERSON><PERSON> <PERSON><PERSON>', false),
    filter('<PERSON><PERSON><PERSON>', false),
  ];

  static List iventFilter = [
    Tag('Spor',
        ['Takım Sporları', 'Su Sporları', 'Savunma Sporları', 'K<PERSON>ş Sporları', 'Bireysel Sporlar', 'Fitnes&Sağlık']),
    Tag('Müzik',
        ['<PERSON>k<PERSON><PERSON> Sporları', '<PERSON> Sporları', 'Savunma Sporları', 'K<PERSON>ş Sporları', 'Bireysel Sporlar', 'Fitnes&Sağlık']),
    Tag('Sanat&Kültür',
        ['<PERSON><PERSON><PERSON><PERSON>ları', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>ı', '<PERSON><PERSON>ysel Sporlar', 'Fitnes&Sağlık'])
  ];
}

class filter {
  final String name;
  final bool isCheck;

  filter(this.name, this.isCheck);
}

class Tag {
  final String name;
  final List tags;

  Tag(this.name, this.tags);
}

class HomeStrings {
  static String lokasyonSec = 'Lokasyon Seç';

  static String arama = 'Arama';
  static String sonSecilenKonum = 'Son Seçien konumlar';
  static String filtreleriTemizle = 'Filtreleri Temizle';
  static String seciliKonum = 'Seçili konum:';
  static String iventAkisSenin = 'iVentAkış’ta konumun senin için önemi:';
  static String ivent = 'Ivent';
  static String kesfet = 'Keşfet';
  static String iventler = "Ivent'ler";
  static String hesaplar = 'Hesaplar';
  static String iptal = 'İptal';
  static String baslangicTarihi = 'Başlangıç Tarihi Seçiniz';
  static String bitisTarihi = 'Bitiş Tarihi Seçiniz';
  static String bitti = 'Bitti';
  static String kullaniciAdi = 'Kullanıci adi';
}
