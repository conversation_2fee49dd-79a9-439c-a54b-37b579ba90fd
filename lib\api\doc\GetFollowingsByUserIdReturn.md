# openapi.model.GetFollowingsByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**followings** | [**List<UserListItemWithRelationshipStatus>**](UserListItemWithRelationshipStatus.md) | List of users being followed | [default to const []]
**followingCount** | **int** | Total number of users being followed | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



