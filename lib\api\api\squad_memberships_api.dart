//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class SquadMembershipsApi {
  SquadMembershipsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Ivent IDsi ile seçilen hesaplar davet edilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [InviteFriendsByIventIdDto] inviteFriendsByIventIdDto (required):
  Future<Response> inviteFriendsByIventIdWithHttpInfo(String iventId, InviteFriendsByIventIdDto inviteFriendsByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/squadMemberships/{iventId}/invite'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody = inviteFriendsByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile seçilen hesaplar davet edilir
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [InviteFriendsByIventIdDto] inviteFriendsByIventIdDto (required):
  Future<void> inviteFriendsByIventId(String iventId, InviteFriendsByIventIdDto inviteFriendsByIventIdDto,) async {
    final response = await inviteFriendsByIventIdWithHttpInfo(iventId, inviteFriendsByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [JoinIventAndCreateSquadByIventIdDto] joinIventAndCreateSquadByIventIdDto (required):
  Future<Response> joinIventAndCreateSquadByIventIdWithHttpInfo(String iventId, JoinIventAndCreateSquadByIventIdDto joinIventAndCreateSquadByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/squadMemberships/{iventId}/join'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody = joinIventAndCreateSquadByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile seçilen hesaplarla birlikte ivente katılır (seçili hesaplara davet gönderilir)
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [JoinIventAndCreateSquadByIventIdDto] joinIventAndCreateSquadByIventIdDto (required):
  Future<void> joinIventAndCreateSquadByIventId(String iventId, JoinIventAndCreateSquadByIventIdDto joinIventAndCreateSquadByIventIdDto,) async {
    final response = await joinIventAndCreateSquadByIventIdWithHttpInfo(iventId, joinIventAndCreateSquadByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile etkinlikten ayrılınır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  Future<Response> leaveSquadByIventIdWithHttpInfo(String iventId,) async {
    // ignore: prefer_const_declarations
    final path = r'/squadMemberships/{iventId}/leave'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile etkinlikten ayrılınır
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  Future<void> leaveSquadByIventId(String iventId,) async {
    final response = await leaveSquadByIventIdWithHttpInfo(iventId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivente katılırken davet edilebilecek hesapları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [FriendListingTypeEnum] type (required):
  ///   Type of friends to list - either groups or users
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchInvitableUsersByIventIdWithHttpInfo(String iventId, FriendListingTypeEnum type, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/squadMemberships/{iventId}/search'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'type', type));
    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivente katılırken davet edilebilecek hesapları listeler
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [FriendListingTypeEnum] type (required):
  ///   Type of friends to list - either groups or users
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchInvitableUsersByIventIdReturn?> searchInvitableUsersByIventId(String iventId, FriendListingTypeEnum type, { String? q, int? limit, int? page, }) async {
    final response = await searchInvitableUsersByIventIdWithHttpInfo(iventId, type,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchInvitableUsersByIventIdReturn',) as SearchInvitableUsersByIventIdReturn;
    
    }
    return null;
  }

  /// Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchParticipantsByIventIdWithHttpInfo(String iventId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/squadMemberships/{iventId}'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile (eğer iventte yetkiliyse): ivente katılan bütün hesapları, (eğer ivente katılmayan bir user ise): ivente katılan ya da favorilemiş arkadaşları, (eğer ivente katılan bir user ise): ivente birlikte katıldığı arkadaşları listeler
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchParticipantsByIventIdReturn?> searchParticipantsByIventId(String iventId, { String? q, int? limit, int? page, }) async {
    final response = await searchParticipantsByIventIdWithHttpInfo(iventId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchParticipantsByIventIdReturn',) as SearchParticipantsByIventIdReturn;
    
    }
    return null;
  }
}

