# openapi.model.UserListItemWithRelationshipStatus

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**userId** | **String** | UUID of the user | 
**username** | **String** | Username of the user | 
**avatarUrl** | **String** | URL to the user's avatar image | [optional] 
**university** | **String** | Name of the user's university | [optional] 
**relationshipStatus** | [**UserRelationshipStatusEnum**](UserRelationshipStatusEnum.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



