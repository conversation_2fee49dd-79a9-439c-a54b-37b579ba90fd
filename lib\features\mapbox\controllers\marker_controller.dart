import 'package:get/get.dart';
import 'package:ivent_app/core/utils/extension_utils.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

/// Controller for managing map markers and their interactions
///
/// Handles marker selection, clustering, and map feature interactions.
/// Manages both individual markers and clustered markers on the map.
class MarkerController {
  // Constants
  static const String sourceId = 'clustered-points';

  // Dependencies
  final Function(List<MarkerFeature> selectedFeatures)? onSelectedFeaturesChanged;

  // State
  late final MapboxMap mapboxMap;
  bool isMapboxMapInitialized = false;

  // Reactive state
  final _allFeatures = <MarkerFeature>[].obs;
  final _selectedFeatures = <MarkerFeature>[].obs;

  // Constructor
  MarkerController({this.onSelectedFeaturesChanged});

  // Getters
  List<MarkerFeature> get allFeatures => _allFeatures;
  List<MarkerFeature> get selectedFeatures => _selectedFeatures;
  List<String> get selectedIventIds => selectedFeatures.map((e) => e.id).toList();

  // Public methods

  /// Initializes the Mapbox map instance
  void setMapboxMap(MapboxMap mapboxMap) {
    if (isMapboxMapInitialized) return;
    this.mapboxMap = mapboxMap;
    isMapboxMapInitialized = true;
  }

  /// Handles feature click events on the map
  Future<void> handleFeatureClick(
    final String? layerId,
    final Map<String?, Object?>? feature,
  ) async {
    if (!isMapboxMapInitialized || feature == null) return;

    switch (layerId) {
      case 'unselected-clusters':
      case 'selected-cluster':
        await _handleClusterClick(feature);
        break;
      case 'unselected-points':
        await _handleUnselectedPointClick(
          MarkerFeature.fromJson(feature.asStringDynamicMap!),
        );
        break;
      case 'selected-point':
        await _handleSelectedPointClick(
          MarkerFeature.fromJson(feature.asStringDynamicMap!),
        );
        break;
    }

    onSelectedFeaturesChanged?.call(selectedFeatures);
  }

  // Private methods

  /// Handles cluster click events
  Future<void> _handleClusterClick(final Map<String?, Object?> cluster) async {
    try {
      final points = await mapboxMap.getGeoJsonClusterLeaves(
        sourceId,
        cluster,
        null,
        null,
      );

      final features =
          points.featureCollection?.map((e) => MarkerFeature.fromJson(e.asStringDynamicMap!)).toList() ?? [];

      final clusterIventIds = features.map((e) => e.id).toList();

      // Check if all cluster features are already selected
      if (selectedIventIds.every((e) => clusterIventIds.contains(e)) &&
          selectedIventIds.length == clusterIventIds.length) {
        // Deselect all features in cluster
        for (final feature in selectedFeatures) {
          feature.properties.isSelected = false;
        }

        await mapboxMap.style.updateGeoJSONSourceFeatures(
          sourceId,
          'unselected-cluster',
          selectedFeatures.map((e) => Feature.fromJson(e.toJson())).toList(),
        );
        selectedFeatures.clear();
      } else {
        // Select features in cluster
        await mapboxMap.style.updateGeoJSONSourceFeatures(
          sourceId,
          'unselected-cluster',
          _getFeaturesToUpdate(features),
        );
      }

      // Fly to cluster location
      final clusterFeature = cluster.asStringDynamicMap!;
      final coordinates = List<double>.from(
        clusterFeature['geometry']['coordinates'],
      );
      _flyTo(coordinates);
    } catch (e) {
      print('Error handling cluster click: $e');
    }
  }

  /// Handles unselected point click events
  Future<void> _handleUnselectedPointClick(final MarkerFeature feature) async {
    try {
      await mapboxMap.style.updateGeoJSONSourceFeatures(
        sourceId,
        'unselected-point',
        _getFeaturesToUpdate([feature]),
      );
      _flyTo(List<double>.from(feature.geometry.coordinates));
    } catch (e) {
      print('Error handling unselected point click: $e');
    }
  }

  /// Handles selected point click events (deselection)
  Future<void> _handleSelectedPointClick(final MarkerFeature feature) async {
    try {
      final iventId = feature.id;
      feature.properties.isSelected = false;

      await mapboxMap.style.updateGeoJSONSourceFeatures(
        sourceId,
        'selected-point',
        [Feature.fromJson(feature.toJson())],
      );
      selectedFeatures.removeWhere((e) => e.id == iventId);
    } catch (e) {
      print('Error handling selected point click: $e');
    }
  }

  /// Gets features to update when selecting new features
  List<Feature> _getFeaturesToUpdate(List<MarkerFeature> newSelectedFeatures) {
    // Deselect currently selected features
    for (final feature in selectedFeatures) {
      feature.properties.isSelected = false;
    }

    // Select new features
    for (final feature in newSelectedFeatures) {
      feature.properties.isSelected = true;
    }

    final featuresToUpdate = newSelectedFeatures.toList();

    // Add previously selected features that are not in new selection
    for (final feature in selectedFeatures) {
      final iventId = feature.id;
      if (!newSelectedFeatures.any((e) => e.id == iventId)) {
        featuresToUpdate.add(feature);
      }
    }

    selectedFeatures.assignAll(newSelectedFeatures);
    return featuresToUpdate.map((e) => Feature.fromJson(e.toJson())).toList();
  }

  /// Flies camera to specified coordinates
  void _flyTo(List<double> coordinates) async {
    final cameraState = await mapboxMap.getCameraState();
    final zoom = cameraState.zoom;

    mapboxMap.flyTo(
      CameraOptions(
        center: Point(
          coordinates: Position(coordinates[0], coordinates[1]),
        ),
        zoom: zoom,
      ),
      MapAnimationOptions(duration: 50),
    );
  }

  /// Adds new markers to the map
  Future<void> addMarkers(List<MarkerFeature> features) async {
    if (!isMapboxMapInitialized || features.isEmpty) return;

    allFeatures.addAll(features);
    await mapboxMap.style.addGeoJSONSourceFeatures(
      sourceId,
      'add-markers',
      features.map((e) => Feature.fromJson(e.toJson())).toList(),
    );
  }

  /// Updates existing markers on the map
  Future<void> updateMarkers(List<MarkerFeature> features) async {
    if (!isMapboxMapInitialized || features.isEmpty) return;

    allFeatures.removeWhere((e) => features.any((f) => f.id == e.id));
    allFeatures.addAll(features);
    selectedFeatures.removeWhere((e) => features.any((f) => f.id == e.id));

    await mapboxMap.style.updateGeoJSONSourceFeatures(
      sourceId,
      'update-markers',
      features.map((e) => Feature.fromJson(e.toJson())).toList(),
    );
  }

  /// Removes markers from the map by their IDs
  Future<void> removeMarkers(List<String> featureIds) async {
    if (!isMapboxMapInitialized || featureIds.isEmpty) return;
    if (!allFeatures.any((e) => featureIds.contains(e.id))) return;

    allFeatures.removeWhere((e) => featureIds.contains(e.id));
    selectedFeatures.removeWhere((e) => featureIds.contains(e.id));

    await mapboxMap.style.removeGeoJSONSourceFeatures(
      sourceId,
      'remove-markers',
      featureIds,
    );
  }

  /// Clears all markers from the controller
  void clearMarkers() {
    allFeatures.clear();
    selectedFeatures.clear();
  }
}
