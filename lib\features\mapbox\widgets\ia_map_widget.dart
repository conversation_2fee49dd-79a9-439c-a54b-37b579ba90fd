import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:rxdart/rxdart.dart';

/// Custom Mapbox map widget for displaying iVent markers
///
/// Provides clustered marker display, user interaction handling,
/// and map bounds change detection. Integrates with the app's
/// marker management system.
class IaMapWidget extends StatefulWidget {
  final Function(MapboxMap mapboxMap)? onMapCreated;
  final Function()? onBoundsChanged;
  final Function(String? layerId, Map<String?, Object?>? feature)? onFeatureSelected;

  const IaMapWidget({
    super.key,
    this.onMapCreated,
    this.onBoundsChanged,
    this.onFeatureSelected,
  });

  @override
  State<IaMapWidget> createState() => _IaMapWidgetState();
}

class _IaMapWidgetState extends State<IaMapWidget> {
  // Constants
  static const String sourceId = 'clustered-points';
  static final Position _defaultPosition = Position(29.0576, 40.9819);
  static const double _defaultZoom = 12.0;
  static final CameraViewportState _defaultCameraViewportState = CameraViewportState(
    center: Point(coordinates: _defaultPosition),
    zoom: _defaultZoom,
  );

  // Controllers and streams
  final _cameraMoveController = BehaviorSubject<MapContentGestureContext>();

  // State
  MapboxMap? mapboxMap;
  bool isMapInitialized = false;

  // Assets
  late final String initialGeojsonString;
  late final Uint8List markerIconDefault;
  late final Uint8List markerIconExpanded;

  @override
  void initState() {
    super.initState();
    MapboxOptions.setAccessToken(dotenv.env['MAPBOX_ACCESS_TOKEN']!);
  }

  @override
  void dispose() {
    _cameraMoveController.close();
    super.dispose();
  }

  // Public methods

  /// Handles map creation and initialization
  Future<void> _onMapCreated(MapboxMap mapboxMap) async {
    if (isMapInitialized) return;

    this.mapboxMap = mapboxMap;
    await _configureMapSettings();
    _setupMapMoveListeners();

    await widget.onMapCreated?.call(mapboxMap);
    await widget.onBoundsChanged?.call();
    isMapInitialized = true;
  }

  // Private methods

  /// Configures initial map settings and camera position
  Future<void> _configureMapSettings() async {
    await mapboxMap?.location.updateSettings(
      LocationComponentSettings(enabled: true),
    );
    await mapboxMap?.gestures.updateSettings(
      GesturesSettings(rotateEnabled: false),
    );
    await mapboxMap?.scaleBar.updateSettings(
      ScaleBarSettings(enabled: false),
    );
    await mapboxMap?.setBounds(
      CameraBoundsOptions(
        maxZoom: 15,
        minZoom: 7,
        maxPitch: 10,
        minPitch: 0,
      ),
    );
    await mapboxMap?.setCamera(
      CameraOptions(
        center: Point(coordinates: _defaultPosition),
        zoom: _defaultZoom,
        padding: MbxEdgeInsets(top: 0, left: 0, bottom: 0, right: 0),
        bearing: 0,
        pitch: 0,
      ),
    );
  }

  /// Sets up map move listeners with debouncing
  void _setupMapMoveListeners() {
    if (widget.onBoundsChanged == null) return;

    mapboxMap?.setOnMapMoveListener(
      (MapContentGestureContext context) => _cameraMoveController.add(context),
    );

    // Debounced map bounds update
    _cameraMoveController.debounceTime(const Duration(milliseconds: 100)).listen((_) async {
      await widget.onBoundsChanged?.call();
    });
  }

  Future<void> _onStyleLoaded(StyleLoadedEventData data) async {
    initialGeojsonString = await rootBundle.loadString('assets/example.json');
    await _setMarkerIcons();
    await _addClusteredGeoJsonSource();
    _setOnMapTapListener();
  }

  Future<void> _setMarkerIcons() async {
    final ByteData dataDefault = await rootBundle.load(AppAssets.iventMapMarkerDefault);
    markerIconDefault = await dataDefault.buffer.asUint8List();

    final ByteData dataExpanded = await rootBundle.load(AppAssets.iventMapMarkerExpanded);
    markerIconExpanded = await dataExpanded.buffer.asUint8List();
  }

  Future<void> _addClusteredGeoJsonSource() async {
    // Create a GeoJSON source with clustering enabled
    final source = GeoJsonSource(
      id: sourceId,
      data: initialGeojsonString,
      cluster: true,
      clusterMaxZoom: 15,
      clusterRadius: 30,
      clusterProperties: {
        'isSelected': ['boolean', false]
      },
    );

    await mapboxMap?.style.addSource(source);

    final imgDefault = MbxImage(width: 48, height: 48, data: markerIconDefault);
    final imgExpanded = MbxImage(width: 60, height: 60, data: markerIconExpanded);
    await mapboxMap?.style.addStyleImage('marker-icon-default', 1, imgDefault, false, [], [], null);
    await mapboxMap?.style.addStyleImage('marker-icon-expanded', 1, imgExpanded, false, [], [], null);

    // Add a circle layer for the unselected clusters
    await mapboxMap?.style.addLayer(
      CircleLayer(
        id: 'unselected-clusters',
        sourceId: sourceId,
        filter: [
          'all',
          ['has', 'point_count'],
          ['==', 'isSelected', false]
        ],
        circleColor: AppColors.primary.toARGB32(),
        circleRadius: 18,
        circleStrokeWidth: 4,
        circleStrokeColor: AppColors.white.toARGB32(),
      ),
    );

    // Add a circle layer for a single selected cluster
    await mapboxMap?.style.addLayer(
      CircleLayer(
        id: 'selected-cluster',
        sourceId: sourceId,
        filter: [
          'all',
          ['has', 'point_count'],
          ['==', 'isSelected', true]
        ],
        circleColor: AppColors.primary.toARGB32(),
        circleRadius: 18,
        circleStrokeWidth: 9,
        circleStrokeColor: AppColors.white.toARGB32(),
      ),
    );

    // Add a symbol layer for cluster counts
    await mapboxMap?.style.addLayer(
      SymbolLayer(
        id: 'cluster-count',
        sourceId: sourceId,
        filter: ['has', 'point_count'],
        textField: '{point_count_abbreviated}',
        textSize: 16,
        textColor: AppColors.white.toARGB32(),
        minZoom: 0,
        maxZoom: 24,
        textAllowOverlap: true,
      ),
    );

    // Add a circle layer for unselected individual points
    await mapboxMap?.style.addLayer(
      SymbolLayer(
        id: 'unselected-points',
        sourceId: sourceId,
        filter: [
          'all',
          ['!has', 'point_count'],
          ['==', 'isSelected', false]
        ],
        iconImage: 'marker-icon-default',
        minZoom: 0,
        maxZoom: 24,
        iconAllowOverlap: true,
      ),
    );

    // Add a circle layer for a single selected individual point
    await mapboxMap?.style.addLayer(
      SymbolLayer(
        id: 'selected-point',
        sourceId: sourceId,
        filter: [
          'all',
          ['!has', 'point_count'],
          ['==', 'isSelected', true]
        ],
        iconImage: 'marker-icon-expanded',
        minZoom: 0,
        maxZoom: 24,
        iconAllowOverlap: true,
      ),
    );
  }

  void _setOnMapTapListener() {
    // Query features at the clicked point
    if (widget.onFeatureSelected == null) return;
    mapboxMap?.setOnMapTapListener(
      (MapContentGestureContext context) async {
        try {
          final features = await mapboxMap?.queryRenderedFeatures(
            RenderedQueryGeometry.fromScreenCoordinate(context.touchPosition),
            RenderedQueryOptions(layerIds: [
              'unselected-clusters',
              'selected-cluster',
              'unselected-points',
              'selected-point',
            ]),
          );
          if (features == null || features.isEmpty) return;
          if (features.isNotEmpty) {
            final layerId = features[0]?.layers[0];
            final feature = features[0]?.queriedFeature.feature;
            widget.onFeatureSelected?.call(layerId, feature);
          }
        } catch (e) {
          print('Error querying features: $e');
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MapWidget(
      styleUri: MapboxStyles.STANDARD,
      viewport: _defaultCameraViewportState,
      onMapCreated: _onMapCreated,
      onStyleLoadedListener: _onStyleLoaded,
      textureView: true, // Use TextureView instead of SurfaceView
    );
  }
}
