//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class UserListItemWithRelationshipStatus {
  /// Returns a new [UserListItemWithRelationshipStatus] instance.
  UserListItemWithRelationshipStatus({
    required this.userId,
    required this.username,
    this.avatarUrl,
    this.university,
    required this.relationshipStatus,
  });

  /// UUID of the user
  String userId;

  /// Username of the user
  String username;

  /// URL to the user's avatar image
  String? avatarUrl;

  /// Name of the user's university
  String? university;

  UserRelationshipStatusEnum relationshipStatus;

  @override
  bool operator ==(Object other) => identical(this, other) || other is UserListItemWithRelationshipStatus &&
    other.userId == userId &&
    other.username == username &&
    other.avatarUrl == avatarUrl &&
    other.university == university &&
    other.relationshipStatus == relationshipStatus;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userId.hashCode) +
    (username.hashCode) +
    (avatarUrl == null ? 0 : avatarUrl!.hashCode) +
    (university == null ? 0 : university!.hashCode) +
    (relationshipStatus.hashCode);

  @override
  String toString() => 'UserListItemWithRelationshipStatus[userId=$userId, username=$username, avatarUrl=$avatarUrl, university=$university, relationshipStatus=$relationshipStatus]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'userId'] = this.userId;
      json[r'username'] = this.username;
    if (this.avatarUrl != null) {
      json[r'avatarUrl'] = this.avatarUrl;
    } else {
      json[r'avatarUrl'] = null;
    }
    if (this.university != null) {
      json[r'university'] = this.university;
    } else {
      json[r'university'] = null;
    }
      json[r'relationshipStatus'] = this.relationshipStatus;
    return json;
  }

  /// Returns a new [UserListItemWithRelationshipStatus] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static UserListItemWithRelationshipStatus? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "UserListItemWithRelationshipStatus[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "UserListItemWithRelationshipStatus[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return UserListItemWithRelationshipStatus(
        userId: mapValueOfType<String>(json, r'userId')!,
        username: mapValueOfType<String>(json, r'username')!,
        avatarUrl: mapValueOfType<String>(json, r'avatarUrl'),
        university: mapValueOfType<String>(json, r'university'),
        relationshipStatus: UserRelationshipStatusEnum.fromJson(json[r'relationshipStatus'])!,
      );
    }
    return null;
  }

  static List<UserListItemWithRelationshipStatus> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UserListItemWithRelationshipStatus>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UserListItemWithRelationshipStatus.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, UserListItemWithRelationshipStatus> mapFromJson(dynamic json) {
    final map = <String, UserListItemWithRelationshipStatus>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = UserListItemWithRelationshipStatus.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of UserListItemWithRelationshipStatus-objects as value to a dart map
  static Map<String, List<UserListItemWithRelationshipStatus>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<UserListItemWithRelationshipStatus>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = UserListItemWithRelationshipStatus.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'userId',
    'username',
    'relationshipStatus',
  };
}

