import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BaseSearchBarController extends GetxController {
  final Function(String? textInput) onSearch;
  final int debounceDuration;

  BaseSearchBarController(this.onSearch, {this.debounceDuration = 500});

  Timer? _debounceTimer;

  final _textEditingController = TextEditingController();
  final _isSearching = false.obs;

  TextEditingController get textEditingController => _textEditingController;
  String get text => _textEditingController.text;
  bool get isSearching => _isSearching.value;

  set text(String value) => _textEditingController.text = value;
  set isSearching(bool value) => _isSearching.value = value;

  @override
  void onInit() async {
    super.onInit();
    _setupSearchListener();
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    textEditingController.dispose();
    super.onClose();
  }

  void _setupSearchListener() {
    textEditingController.addListener(() {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(Duration(milliseconds: debounceDuration), () async {
        final textInput = textEditingController.text.trim();
        if (textInput.isNotEmpty) {
          isSearching = true;
          await onSearch(textInput);
          isSearching = false;
        } else {
          clearSearch();
        }
      });
    });
  }

  void clearSearch() async {
    textEditingController.clear();
    isSearching = true;
    await onSearch(null);
    isSearching = false;
  }
}
