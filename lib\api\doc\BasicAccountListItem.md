# openapi.model.BasicAccountListItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**accountId** | **String** | UUID of the account (user or page) | 
**accountName** | **String** | Name of the account (user or page) | 
**accountType** | [**AccountTypeEnum**](AccountTypeEnum.md) |  | 
**accountImageUrl** | **String** | URL to the account image | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



