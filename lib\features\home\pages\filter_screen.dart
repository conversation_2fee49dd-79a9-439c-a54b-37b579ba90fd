import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/home/<USER>/strings.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/hobby_catalogue.dart';

class FilterScreen extends StatefulWidget {
  const FilterScreen({Key? key}) : super(key: key);

  @override
  State<FilterScreen> createState() => _FilterScreenState();
}

class _FilterScreenState extends State<FilterScreen> {
  final HomeController _controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) => _controller.goToFeedPage(),
      child: Obx(() {
        final appliedFilterCount = _controller.state.appliedFilterCount;
        return Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppDimensions.padding12),
                _buildTopRow(),
                const SizedBox(height: AppDimensions.padding24),
                _buildLocationButton(),
                const SizedBox(height: AppDimensions.padding20),
                _buildLocationSlider(),
                const SizedBox(height: AppDimensions.padding20),
                Expanded(child: HobbyCatalogue(controller: _controller))
              ],
            ),
            if (appliedFilterCount != 0)
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IaFloatingActionButton(
                    text: 'Filtreleri Uygula',
                    onPressed: _controller.feedController.applyFilters,
                    isEnabled: true,
                  ),
                  const SizedBox(height: AppDimensions.bottomNavigationBarHeight),
                ],
              ),
          ],
        );
      }),
    );
  }

  Widget _buildTopRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IaTextButton(
            onPressed: _controller.filterController.clearFilters,
            text: HomeStrings.filtreleriTemizle,
            textStyle: AppTextStyles.size16MediumTextSecondary,
          ),
          HomeButtons.closeButtonLight(onTap: _controller.goToFeedPage),
        ],
      ),
    );
  }

  Widget _buildLocationButton() {
    return HomeButtons.locationFilter(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      onTap: _controller.goToLocationPage,
      text: _controller.state.selectedPlace != null ? _controller.state.selectedPlace!.name : 'Yok',
    );
  }

  Widget _buildLocationSlider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(HomeStrings.iventAkisSenin, style: AppTextStyles.size14Regular),
          const SizedBox(height: AppDimensions.padding32),
          Obx(() {
            return IaSlider(
              value: _controller.state.locationCoeff.toDouble(),
              onChanged: (newValue) => _controller.state.locationCoeff = newValue.toInt(),
              label: _controller.state.locationCoeff.toStringAsFixed(0),
            );
          }),
        ],
      ),
    );
  }
}
