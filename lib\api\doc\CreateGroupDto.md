# openapi.model.CreateGroupDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groupName** | **String** | Group name can only contain letters, numbers, underscores, hyphens, and periods | 
**thumbnailBuffer** | **String** | Base64 encoded thumbnail image buffer for the group | [optional] 
**userIds** | **List<String>** | Array of user UUIDs to add as group members | [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



