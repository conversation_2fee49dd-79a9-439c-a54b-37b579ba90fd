# openapi.api.NotificationsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getNotifications**](NotificationsApi.md#notificationscontrollergetnotifications) | **GET** /notifications | Bildirimleri listeler
[**replyInvitationBySquadId**](NotificationsApi.md#notificationscontrollerreplyinvitationbysquadid) | **POST** /notifications/{id}/reply | Squad IDsi ile seçilen davete \"accept\" ya da \"reject\" yanıtı verilir


# **getNotifications**
> GetNotificationsReturn getNotifications(limit, page)

Bildirimleri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = NotificationsApi();
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getNotifications(limit, page);
    print(result);
} catch (e) {
    print('Exception when calling NotificationsApi->getNotifications: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetNotificationsReturn**](GetNotificationsReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **replyInvitationBySquadId**
> replyInvitationBySquadId(id, reply)

Squad IDsi ile seçilen davete \"accept\" ya da \"reject\" yanıtı verilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = NotificationsApi();
final id = id_example; // String | 
final reply = ; // NotificationReplyTypeEnum | Reply type for the notification

try {
    api_instance.replyInvitationBySquadId(id, reply);
} catch (e) {
    print('Exception when calling NotificationsApi->replyInvitationBySquadId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **reply** | [**NotificationReplyTypeEnum**](.md)| Reply type for the notification | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


