import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/app/routes/profile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/followings_controller.dart';

class ProfilePageFollowings extends StatelessWidget {
  final String userId;

  const ProfilePageFollowings({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    final FollowingsController followingsController = controller.followingsController;

    return IaScaffold.search(
      title: '<PERSON><PERSON><PERSON>klerin',
      textEditingController: followingsController.textEditingController,
      body: Obx(() {
        final followingsResult = followingsController.followingsResult;
        return IaSearchPlaceholder(
          entityType: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
          isSearching: followingsController.isSearching,
          isQueryEmpty: followingsController.isQueryEmpty,
          isResultsEmpty: followingsController.isResultsEmpty,
          isDefaultStateEmpty: false,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: followingsResult!.followingCount,
              itemBuilder: (context, index) {
                final following = followingsResult.followings[index];
                return IaListTile.withImageUrl(
                  avatarUrl: following.avatarUrl,
                  title: '@${following.username}',
                  subtitle: following.university,
                  onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: following.userId),
                  trailing: SharedButtons.moreVertical(),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }
}
