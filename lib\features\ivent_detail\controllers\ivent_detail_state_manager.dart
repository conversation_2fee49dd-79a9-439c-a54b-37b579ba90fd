import 'package:get/get.dart';

/// State manager for iVent detail feature
///
/// Manages shared reactive state across all iVent detail controllers.
/// This includes navigation state, UI state, and shared data that needs
/// to be accessible across multiple controllers within the feature.
class IventDetailStateManager extends GetxController {
  // Constants
  final String iventId;

  // Reactive state
  final _isLoading = false.obs;
  final _hasError = false.obs;
  final _errorMessage = ''.obs;

  // Constructor
  IventDetailStateManager(this.iventId);

  // Getters
  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;

  // Setters
  set isLoading(bool value) => _isLoading.value = value;
  set hasError(bool value) => _hasError.value = value;
  set errorMessage(String value) => _errorMessage.value = value;

  // Methods

  /// Clears any error state
  void clearError() {
    hasError = false;
    errorMessage = '';
  }

  /// Sets error state with message
  void setError(String message) {
    hasError = true;
    errorMessage = message;
    isLoading = false;
  }

  /// Sets loading state
  void setLoading(bool loading) {
    isLoading = loading;
    if (loading) {
      clearError();
    }
  }
}
