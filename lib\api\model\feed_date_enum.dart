//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class FeedDateEnum {
  /// Instantiate a new enum with the provided [value].
  const FeedDateEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const range = FeedDateEnum._(r'range');
  static const toYou = FeedDateEnum._(r'to_you');
  static const today = FeedDateEnum._(r'today');
  static const tomorrow = FeedDateEnum._(r'tomorrow');
  static const thisWeek = FeedDateEnum._(r'this_week');
  static const nextWeek = FeedDateEnum._(r'next_week');
  static const thisMonth = FeedDateEnum._(r'this_month');
  static const thisSummer = FeedDateEnum._(r'this_summer');
  static const holiday = FeedDateEnum._(r'holiday');

  /// List of all possible values in this [enum][FeedDateEnum].
  static const values = <FeedDateEnum>[
    range,
    toYou,
    today,
    tomorrow,
    thisWeek,
    nextWeek,
    thisMonth,
    thisSummer,
    holiday,
  ];

  static FeedDateEnum? fromJson(dynamic value) => FeedDateEnumTypeTransformer().decode(value);

  static List<FeedDateEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FeedDateEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FeedDateEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [FeedDateEnum] to String,
/// and [decode] dynamic data back to [FeedDateEnum].
class FeedDateEnumTypeTransformer {
  factory FeedDateEnumTypeTransformer() => _instance ??= const FeedDateEnumTypeTransformer._();

  const FeedDateEnumTypeTransformer._();

  String encode(FeedDateEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a FeedDateEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  FeedDateEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'range': return FeedDateEnum.range;
        case r'to_you': return FeedDateEnum.toYou;
        case r'today': return FeedDateEnum.today;
        case r'tomorrow': return FeedDateEnum.tomorrow;
        case r'this_week': return FeedDateEnum.thisWeek;
        case r'next_week': return FeedDateEnum.nextWeek;
        case r'this_month': return FeedDateEnum.thisMonth;
        case r'this_summer': return FeedDateEnum.thisSummer;
        case r'holiday': return FeedDateEnum.holiday;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [FeedDateEnumTypeTransformer] instance.
  static FeedDateEnumTypeTransformer? _instance;
}

