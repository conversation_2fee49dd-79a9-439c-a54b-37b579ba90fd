//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetNotificationsReturn {
  /// Returns a new [GetNotificationsReturn] instance.
  GetNotificationsReturn({
    this.notifications = const [],
    required this.notificationCount,
  });

  /// List of user notifications
  List<NotificationItem> notifications;

  /// Total number of notifications
  ///
  /// Minimum value: 0
  int notificationCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetNotificationsReturn &&
    _deepEquality.equals(other.notifications, notifications) &&
    other.notificationCount == notificationCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (notifications.hashCode) +
    (notificationCount.hashCode);

  @override
  String toString() => 'GetNotificationsReturn[notifications=$notifications, notificationCount=$notificationCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'notifications'] = this.notifications;
      json[r'notificationCount'] = this.notificationCount;
    return json;
  }

  /// Returns a new [GetNotificationsReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetNotificationsReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetNotificationsReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetNotificationsReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetNotificationsReturn(
        notifications: NotificationItem.listFromJson(json[r'notifications']),
        notificationCount: mapValueOfType<int>(json, r'notificationCount')!,
      );
    }
    return null;
  }

  static List<GetNotificationsReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetNotificationsReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetNotificationsReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetNotificationsReturn> mapFromJson(dynamic json) {
    final map = <String, GetNotificationsReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetNotificationsReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetNotificationsReturn-objects as value to a dart map
  static Map<String, List<GetNotificationsReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetNotificationsReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetNotificationsReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'notifications',
    'notificationCount',
  };
}

