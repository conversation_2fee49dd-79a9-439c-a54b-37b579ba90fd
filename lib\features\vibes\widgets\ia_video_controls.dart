import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class IaVideoControls extends StatefulWidget {
  final VideoPlayerController videoPlayerController;

  const IaVideoControls({super.key, required this.videoPlayerController});

  @override
  State<IaVideoControls> createState() => _IaVideoControlsState();
}

class _IaVideoControlsState extends State<IaVideoControls> {
  VideoPlayerController get _videoPlayerController => widget.videoPlayerController;
  bool get _isPlaying => _videoPlayerController.value.isPlaying;

  void _togglePlayPause() => _isPlaying ? _videoPlayerController.pause() : _videoPlayerController.play();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _togglePlayPause,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.transparent,
      ),
    );
  }
}
