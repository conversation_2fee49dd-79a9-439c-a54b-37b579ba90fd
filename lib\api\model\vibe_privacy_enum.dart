//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Privacy setting for the vibe
class VibePrivacyEnum {
  /// Instantiate a new enum with the provided [value].
  const VibePrivacyEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const private = VibePrivacyEnum._(r'private');
  static const friends = VibePrivacyEnum._(r'friends');
  static const friendsOfFriends = VibePrivacyEnum._(r'friends_of_friends');
  static const public = VibePrivacyEnum._(r'public');

  /// List of all possible values in this [enum][VibePrivacyEnum].
  static const values = <VibePrivacyEnum>[
    private,
    friends,
    friendsOfFriends,
    public,
  ];

  static VibePrivacyEnum? fromJson(dynamic value) => VibePrivacyEnumTypeTransformer().decode(value);

  static List<VibePrivacyEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <VibePrivacyEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = VibePrivacyEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [VibePrivacyEnum] to String,
/// and [decode] dynamic data back to [VibePrivacyEnum].
class VibePrivacyEnumTypeTransformer {
  factory VibePrivacyEnumTypeTransformer() => _instance ??= const VibePrivacyEnumTypeTransformer._();

  const VibePrivacyEnumTypeTransformer._();

  String encode(VibePrivacyEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a VibePrivacyEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  VibePrivacyEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'private': return VibePrivacyEnum.private;
        case r'friends': return VibePrivacyEnum.friends;
        case r'friends_of_friends': return VibePrivacyEnum.friendsOfFriends;
        case r'public': return VibePrivacyEnum.public;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [VibePrivacyEnumTypeTransformer] instance.
  static VibePrivacyEnumTypeTransformer? _instance;
}

