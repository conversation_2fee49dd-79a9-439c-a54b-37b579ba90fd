import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';

class IventCreateImageSelection extends StatefulWidget {
  @override
  State<IventCreateImageSelection> createState() => _IventCreateImageSelectionState();
}

class _IventCreateImageSelectionState extends State<IventCreateImageSelection> {
  final IventCreateController _controller = Get.find();
  final TextEditingController _searchBarController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchBarController.addListener(() => _controller.formController.iventName = _searchBarController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final pageContext = _controller.imageController.suggestedImages;
      if (pageContext == null) {
        return const IaLoadingIndicator();
      }
      return IaScaffold.noSearch(
        child: SizedBox(
          width: double.maxFinite,
          child: TextField(
            controller: _searchBarController,
            style: AppTextStyles.size32Bold,
            decoration: InputDecoration(
              hintText: 'Etkinlik İsmi',
              hintStyle: AppTextStyles.size32BoldTextSecondary,
              border: InputBorder.none,
            ),
          ),
        ),
        showDivider: false,
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IaImageContainer.withChild(
                  onTap: _controller.goToImageGalleryPage,
                  margin: const EdgeInsets.only(top: AppDimensions.padding12),
                  roundness: AppDimensions.radiusL,
                  child: _controller.state.selectedImageFile != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                          child: Image.memory(_controller.state.selectedImageFile!, fit: BoxFit.cover),
                        )
                      : Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const IaSvgIcon(
                                iconPath: AppAssets.image01, iconSize: 100, iconColor: AppColors.mediumGrey),
                            const SizedBox(height: AppDimensions.padding12),
                            Text(
                              'Görseli galeriden yüklemek için tıklayınız',
                              style: AppTextStyles.size14MediumTextSecondary,
                            ),
                          ],
                        ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
                  child: Text('Senin için Seçtiğimiz Görseller', style: AppTextStyles.size16MediumTextSecondary),
                ),
                GridView.builder(
                  padding: const EdgeInsets.only(bottom: 100),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    crossAxisSpacing: AppDimensions.padding16,
                    mainAxisSpacing: AppDimensions.padding16,
                  ),
                  itemCount: pageContext.imageCount,
                  itemBuilder: (context, index) {
                    final element = pageContext.imageUrls[index];
                    final isSelected = _controller.state.selectedImageUrl == element;
                    return IaImageContainer.withImageUrl(
                      onTap: () => _controller.state.selectedImageUrl = element,
                      roundness: AppDimensions.radiusL,
                      margin: EdgeInsets.all(isSelected ? 0 : 2),
                      imageUrl: element,
                      borderColor: isSelected ? AppColors.secondary : null,
                      borderWidth: isSelected ? 2 : null,
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: IaFloatingActionButton(
          onPressed: _controller.goToDateSelectionPage,
          isEnabled: (_controller.state.selectedImageUrl != null || _controller.state.selectedImageFile != null) &&
              _controller.formController.iventName.length > 3,
          isPrimary: false,
          text: 'Devam Et',
        ),
      );
    });
  }
}
