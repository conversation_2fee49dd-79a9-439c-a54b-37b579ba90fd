//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class MemoryOriginEnum {
  /// Instantiate a new enum with the provided [value].
  const MemoryOriginEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const createNew = MemoryOriginEnum._(r'create_new');
  static const listFolder = MemoryOriginEnum._(r'list_folder');

  /// List of all possible values in this [enum][MemoryOriginEnum].
  static const values = <MemoryOriginEnum>[
    createNew,
    listFolder,
  ];

  static MemoryOriginEnum? fromJson(dynamic value) => MemoryOriginEnumTypeTransformer().decode(value);

  static List<MemoryOriginEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MemoryOriginEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MemoryOriginEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [MemoryOriginEnum] to String,
/// and [decode] dynamic data back to [MemoryOriginEnum].
class MemoryOriginEnumTypeTransformer {
  factory MemoryOriginEnumTypeTransformer() => _instance ??= const MemoryOriginEnumTypeTransformer._();

  const MemoryOriginEnumTypeTransformer._();

  String encode(MemoryOriginEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a MemoryOriginEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  MemoryOriginEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'create_new': return MemoryOriginEnum.createNew;
        case r'list_folder': return MemoryOriginEnum.listFolder;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [MemoryOriginEnumTypeTransformer] instance.
  static MemoryOriginEnumTypeTransformer? _instance;
}

