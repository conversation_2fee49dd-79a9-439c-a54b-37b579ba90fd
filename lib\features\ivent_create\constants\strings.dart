/// String constants for ivent creation feature.
///
/// This class contains all the localized strings used throughout the ivent creation
/// flow, organized by functional areas for better maintainability.
class IventCreateStrings {
  IventCreateStrings._(); // Private constructor to prevent instantiation

  // ============================================================================
  // CATEGORY SELECTION
  // ============================================================================

  /// Category selection page title
  static const String kategoriSec = 'Kategori Seçiniz';

  /// Sub-category selection page title
  static const String altKategoriSec = 'Alt Kategori Seçiniz';

  /// Category names with emojis
  static const String sanatKultur = '🎨 Sanat & Kültür';
  static const String muzik = '🎵  Müzik';
  static const String spor = '👟 Spor ';
  static const String yemeIcme = '🍽️  Yeme İçme';
  static const String toplum = '🤝🏻  Toplum';
  static const String kariyerAkedemik = '📚  Kariyer & Akademik';
  static const String diger = 'Diğer';

  /// Sub-category names
  static const String gorselSanatlar = 'Görsel Sanatlar';
  static const String sahneSanatlari = 'Sahne Sanatları';
  static const String sinemaFilm = 'Sinema & Film';
  static const String muzeTarih = 'Müze & Tarih';
  static const String madoGuzellik = 'Moda & Güzellik';
  static const String yaziEdebiyat = 'Yazı & Edebiyat';

  // ============================================================================
  // IVENT DETAILS
  // ============================================================================

  /// Ivent name input label
  static const String etkinlikIsmi = 'Etkinlik İsmi';

  /// Description section
  static const String aciklama = 'Açıklama';
  static const String aciklamaEkle = 'Açıklama Ekle(Opsiyonel)';
  static const String aciklamaKaydet = 'Açıklamayı Kaydet';
  static const String aciklamayiKayde = 'Açıklamayı Kaydet';

  // ============================================================================
  // IMAGE SELECTION
  // ============================================================================

  /// Image upload instructions
  static const String gorselGaleridenYuklemekicin = 'Görseli galeriden yüklemek için tıklayınız';

  /// Suggested images section
  static const String seninIcinSG = 'Senin için Seçtiğimiz Görseller';

  // ============================================================================
  // DATE AND TIME
  // ============================================================================

  /// Date and time selection
  static const String tarihSaat = 'Tarih & Saat';
  static const String seciniz = 'Seçiniz';
  static const String gunEkle = 'Gün Ekle';
  static const String gun = 'Gün';

  // ============================================================================
  // LOCATION AND MAP
  // ============================================================================

  /// Map and location search
  static const String haritadaMekanAra = 'Haritada Mekan Ara';
  static const String sonbaktiklarin = 'Son Baktıkların';
  static const String konumuSecEtkinligiOnizle = 'Konumu Seç ve Etkinliği Önizle';

  // ============================================================================
  // PRIVACY AND SHARING
  // ============================================================================

  /// Privacy settings
  static const String herkes = 'Herkes';
  static const String arkadaslar = 'Arkadaşlar';
  static const String etkinligiHerkeslePaylas = 'Etkinliği Herkesle Paylaş';
  static const String digerIlePaylas = 'Diğer ile Paylaş';
  static const String sadeceUniOgren = 'Sadece Üniversite öğrencileri';
  static const String seciliUni = 'Seçili Üniversiteler';
  static const String tumUniOgrencilerlePaylas = 'Tüm Üniversite Öğrencileriyle paylaş';
  static const String secilenUniversitelerinOgrencilerilePaylas = 'Seçilen üniversitelerin öğrencileriyle paylaş';
  static const String universitelrt = 'Üniversiteler';
  static const String suKisiler = 'Şu Kişiler Hariç';

  // ============================================================================
  // COLLABORATION
  // ============================================================================

  /// Collaboration and partnerships
  static const String paydasler = 'Paydaşlar';
  static const String paydaslar = 'Paydaşlar';
  static const String paydasEkle = 'Paydaş Ekle';
  static const String paydasDes =
      'Oluşturduğun ivent, onaylandığı takdirde seçtiğin paydaşların sayfasında da paylaşılacaktır.';

  // ============================================================================
  // REGISTRATION TYPES
  // ============================================================================

  /// Registration and contact methods
  static const String kayitTuruSec = 'Kayıt Türü Seç ve Yayınla';
  static const String kayitTurleri = 'Kayıt Türleri';

  /// Google Forms
  static const String googleForms = 'Google Forms';
  static const String formlinkiniBurayaYazin = 'Form Linkini Buraya Yazınız';
  static const String formLinkiniYaziniz = 'Form Linkini Yazınız';

  /// WhatsApp options
  static const String whatsappGrubu = 'Whatsapp Grubu';
  static const String whatsappMesaji = 'Whatsapp Mesajı';
  static const String grupLinkiniBurayaTazin = 'Grup Linkini Buraya Yazınız';
  static const String telNoBurayaYaziniz = 'Telefon Numaranızı Buraya Yazınız';

  /// Instagram
  static const String instagramDM = 'Instagram DM';
  static const String kullaniciAdi = '@kullaniciadi';

  /// Phone and website
  static const String sesliArama = 'Sesli Arama';
  static const String linkiBurayaYaziniz = 'Linki Buraya Yazınız';
  static const String biletKatilimBag = 'Bilet / Katılım Bağlantısı';

  // ============================================================================
  // ACTIONS AND NAVIGATION
  // ============================================================================

  /// Common actions
  static const String devamEt = 'Devam Et';
  static const String kaydet = 'Kaydet';
  static const String onayla = 'Onayla';
  static const String bitti = 'Bitti';
  static const String yayinla = 'Yayınla!';

  /// Display actions
  static const String dahaAzGoster = 'Daha Az Göster';
  static const String dahaFazlaGoster = 'Daha Fazla Göster';
}
