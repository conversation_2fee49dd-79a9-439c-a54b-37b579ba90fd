# openapi.model.CommentItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**commentId** | **String** | UUID of the comment | 
**comment** | **String** | Text content of the comment | 
**commenterUserId** | **String** | UUID of the user who made the comment | 
**commenterUsername** | **String** | Username of the user who made the comment | 
**createdAt** | **String** | Timestamp when the comment was created in ISO 8601 date-time format | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



