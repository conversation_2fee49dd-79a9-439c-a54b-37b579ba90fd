import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_controller.dart';

class ContactsPage extends StatefulWidget {
  final bool accessGranted;

  const ContactsPage({super.key, required this.accessGranted});

  @override
  State<ContactsPage> createState() => _ContactsPageState();
}

class _ContactsPageState extends State<ContactsPage> {
  late final AuthController _controller;
  late final TextEditingController _searchBarController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: AuthStrings.kisileriniz,
      textEditingController: _searchBarController,
      body: Obx(() => _buildBody()),
      floatingActionButton: IaFloatingActionButton(
        isEnabled: true,
        text: AuthStrings.uyeligiTamamla,
        onPressed: _handleCompletePressed,
      ),
    );
  }

  Widget _buildBody() {
    final contactsData = _controller.contactsController.getContactsReturn;

    if (contactsData == null) {
      return const IaLoadingIndicator();
    }

    if (widget.accessGranted) {
      return _buildContactsList(contactsData);
    } else {
      return _buildAccessDeniedMessage();
    }
  }

  Widget _buildContactsList(GetContactsByUserIdReturn contactsData) {
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: contactsData.contactCount,
      itemBuilder: (context, index) => _buildContactItem(contactsData.contacts[index]),
      separatorBuilder: IaListTile.separatorBuilder20,
    );
  }

  Widget _buildContactItem(dynamic contact) {
    return IaListTile.withImageUrl(
      avatarUrl: contact.avatarUrl,
      title: '@${contact.username}',
      subtitle: contact.university,
      trailing: SharedButtons.addFriendListTile(
        onTap: () => _handleFriendRequestToggle(contact.userId),
        relationshipStatus: _controller.contactsController.getRelationshipStatus(contact.userId)!,
      ),
    );
  }

  Widget _buildAccessDeniedMessage() {
    return const Column(
      children: [
        Expanded(
          child: Center(
            child: Text(
              'Kişilerinize erişim izni vermediniz.',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
        ),
        SizedBox(height: 100),
      ],
    );
  }

  Future<void> _handleFriendRequestToggle(String userId) async {
    try {
      await _controller.contactsController.toggleFriendRequest(userId);
    } catch (error) {
      print('Friend request toggle error: $error');
    }
  }

  void _handleCompletePressed() {
    _controller.goToBottomNavBar();
  }

  void _initializeControllers() {
    _controller = Get.find<AuthController>();
    _searchBarController = TextEditingController();
  }

  void _disposeControllers() {
    _searchBarController.dispose();
  }
}
