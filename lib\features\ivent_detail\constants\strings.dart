/// String constants for ivent detail feature.
///
/// This class contains all the localized strings used throughout the ivent detail
/// flow, organized by functional areas for better maintainability and consistency
/// with the project's string management patterns.
class IventDetailStrings {
  IventDetailStrings._(); // Private constructor to prevent instantiation

  // ============================================================================
  // PAGE TITLES AND NAVIGATION
  // ============================================================================

  /// Main ivent detail page title
  static const String iventDetay = 'iVent Detay';

  /// Members/participants page title
  static const String uyeler = 'Üyeler';

  /// Participants section title
  static const String katilimcilar = 'Katılımcılar';

  // ============================================================================
  // ACTIONS AND BUTTONS
  // ============================================================================

  /// Common actions
  static const String kaydet = 'Kaydet';
  static const String bitti = 'Bitti';
  static const String iptalEt = 'İptal Et';

  /// iVent-specific actions
  static const String etkinligeKatil = 'Etkinliğe Katıl';
  static const String etkinligiPaylas = 'Etkinliği Paylaş';
  static const String etkinliktenAyril = 'Etkinlikten Ayrıl';
  static const String etkinligeKatildin = 'Etkinliğe Katıldın';

  /// Display actions
  static const String dahaFazlaGoster = 'Daha Fazla Göster';
  static const String devaminiOku = 'Devamını Oku';
  static const String gizle = 'Gizle';

  // ============================================================================
  // CONTENT SECTIONS
  // ============================================================================

  /// Description section
  static const String aciklama = 'Açıklama';
  static const String aciklamaKaydet = 'Açıklamayı Kaydet';

  /// Collaborators and participants
  static const String paydaslar = 'Paydaşlar';
  static const String etkinlikSahibi = 'Etkinlik Sahibi';
  static const String paydasliktanAyril = 'Paydaşlıktan Ayrıl';

  // ============================================================================
  // INVITATION AND PARTICIPATION
  // ============================================================================

  /// Invitation flow
  static const String katiliyormusun = 'Katılıyor musun?';
  static const String kimlerleKatiliyorsun = 'Kimlerle Katılıyorsun?';
  static const String birdenFazlaSec = 'Birden Fazla Seçebilirsin';
  static const String arkadasinlaKatil = 'Arkadaşınla Katıl';

  /// Friend and group management
  static const String arkadasGruplari = 'Arkadaş Grupları';
  static const String arkadaslar = 'Arkadaşlar';
  static const String dahaFazlaKisicagir = 'Daha Fazla Kişi Çağır';
  static const String arkadasiniDahaCagir = 'Arkadaşını Daha Çağır';

  /// Team and squad
  static const String iventEkibin = 'iVent Ekibin';

  // ============================================================================
  // COMMUNICATION METHODS
  // ============================================================================

  /// Contact options
  static const String mesajGonder = 'Mesaj Gönder';
  static const String sesliArama = 'Sesli Arama';
  static const String dMileUlas = 'DM ile Ulaş';
  static const String numarayiKopyala = 'Numarayı Kopyala';

  /// Platform-specific
  static const String dM = 'DM';
  static const String link = 'Link';
  static const String form = 'Form';
  static const String grup = 'Grup';
  static const String mesaj = 'Mesaj';
  static const String kayitFormu = 'Kayıt Formu';
  static const String linkeGit = "Link'e Git";
  static const String grubaKatil = 'Gruba Katıl';

  // ============================================================================
  // USER INTERACTIONS
  // ============================================================================

  /// Social actions
  static const String takipEt = 'Takip Et';
  static const String takipEdiliyor = 'Takip Ediliyor';
  static const String favorilreEkledi = 'Favorilere Ekledi';
  static const String etkinligeKatiliyor = 'Etkinliğe Katılıyor';

  /// Moderation actions
  static const String etkinlikSahibiniEngelle = 'Etkinliğin Sahibini Engelle';
  static const String etkinligiSikyetEt = 'Etkinliği Şikayet Et';
  static const String suKisilerHaric = 'Şu Kişiler Hariç';
  static const String cikarveEtkinligiGizle = 'Çıkar ve Etkinliği Gizle';
  static const String suKisilerdengizle = 'Şu Kişilerden Gizle';

  // ============================================================================
  // SETTINGS AND MANAGEMENT
  // ============================================================================

  /// Event management
  static const String etkinlikKonumunuGuncelle = 'Etkinlik Konumunu Güncelle';
  static const String etkinlikZamaniGuncelle = 'Etkinlik Zamanını Güncelle';
  static const String kayitTurleri = 'Kayıt Türleri';

  /// Location and map
  static const String haritadaGIcin = 'Haritada görmek için tıkla...';

  // ============================================================================
  // GENERAL UI
  // ============================================================================

  /// Search and navigation
  static const String arama = 'Arama...';
  static const String anaSayfa = 'Ana Sayfa';

  /// Count and quantity
  static const String kisi = 'Kişi';

  // ============================================================================
  // CATEGORIES (for reference)
  // ============================================================================

  /// Category display strings
  static const String sanatKultur = '🎨 Sanat & Kültür';
  static const String muzik = '🎵 Müzik';
  static const String spor = '👟 Spor';
  static const String yemeIcme = '🍽️ Yeme İçme';
  static const String toplum = '🤝🏻 Toplum';
  static const String kariyerAkedemik = '📚 Kariyer & Akademik';
}
