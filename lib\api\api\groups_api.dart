//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class GroupsApi {
  GroupsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Arkadaş grubu oluşturur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreateGroupDto] createGroupDto (required):
  Future<Response> createGroupWithHttpInfo(CreateGroupDto createGroupDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/groups/create';

    // ignore: prefer_final_locals
    Object? postBody = createGroupDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Arkadaş grubu oluşturur
  ///
  /// Parameters:
  ///
  /// * [CreateGroupDto] createGroupDto (required):
  Future<CreateGroupReturn?> createGroup(CreateGroupDto createGroupDto,) async {
    final response = await createGroupWithHttpInfo(createGroupDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateGroupReturn',) as CreateGroupReturn;
    
    }
    return null;
  }

  /// Grubun IDsi ile arkadaş grubunu siler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deleteGroupByGroupIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/groups/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile arkadaş grubunu siler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deleteGroupByGroupId(String id,) async {
    final response = await deleteGroupByGroupIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Grubun IDsi ile arkadaş grubundaki hesapları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getGroupByGroupIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/groups/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Grubun IDsi ile arkadaş grubundaki hesapları listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetGroupByGroupIdReturn?> getGroupByGroupId(String id,) async {
    final response = await getGroupByGroupIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetGroupByGroupIdReturn',) as GetGroupByGroupIdReturn;
    
    }
    return null;
  }
}

