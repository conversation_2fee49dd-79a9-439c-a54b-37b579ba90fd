import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ivent_create_place_search_panel.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ivent_create_selected_place_panel.dart';
import 'package:ivent_app/features/mapbox/widgets/ia_map_widget.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IventCreateMap extends StatefulWidget {
  const IventCreateMap({super.key});

  @override
  State<IventCreateMap> createState() => _IventCreateMapState();
}

class _IventCreateMapState extends State<IventCreateMap> {
  // Controllers
  final IventCreateController _controller = Get.find();

  IventCreateMapController get _mapController => _controller.mapController;
  bool get _isPlaceSelected => _mapController.state.selectedPlace != null;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IaSlidingPanel(
          panelController: _mapController.panelController,
          defaultPanelState: PanelState.CLOSED,
          isDraggable: !_isPlaceSelected,
          maxHeight: Get.height * 0.8,
          minHeight: _isPlaceSelected ? 115 + AppDimensions.buttonHeightLongBar : 90,
          panel: IaBottomPanel(
            showSlideIndicator: true,
            body: _isPlaceSelected
                ? IventCreateSelectedPlacePanel(controller: _controller)
                : IventCreatePlaceSearchPanel(controller: _controller),
          ),
          body: SafeArea(
            child: Stack(
              children: [
                Positioned.fill(child: _buildMapWidget()),
                _buildActionButtons(),
              ],
            ),
          ),
        );
      }),
    );
  }

  IaMapWidget _buildMapWidget() {
    return IaMapWidget(
      onMapCreated: _mapController.mapboxController.setMapboxMap,
    );
  }

  Widget _buildActionButtons() {
    return Positioned(
      top: 20,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IventCreateButtons.mapBackButton(),
          IventCreateButtons.findUserLocation(onTap: _controller.mapController.mapboxController.moveCameraToUserLocation),
        ],
      ),
    );
  }
}
