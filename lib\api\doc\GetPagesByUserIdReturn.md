# openapi.model.GetPagesByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**pages** | [**List<SideMenuPageItem>**](SideMenuPageItem.md) | List of pages the user is associated with | [default to const []]
**pageCount** | **int** | Total number of pages | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



