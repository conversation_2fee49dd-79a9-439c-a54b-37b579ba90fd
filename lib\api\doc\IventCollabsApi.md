# openapi.api.IventCollabsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**leaveCollabrationByIventId**](IventCollabsApi.md#iventcollabscontrollerleavecollabrationbyiventid) | **POST** /iventCollabs/{iventId}/leave | Ivent IDsi ile paydaş hesaplardan ayrılınır
[**removeCollabByIventId**](IventCollabsApi.md#iventcollabscontrollerremovecollabbyiventid) | **POST** /iventCollabs/{iventId}/remove | Ivent IDsi ile bir hesabı paydaş hesaplardan çıkartır
[**searchCollabs**](IventCollabsApi.md#iventcollabscontrollersearchcollabs) | **GET** /iventCollabs/{iventId}/search | Ivent'te bulunan paydaş hesapları listeler
[**searchCollabsForIventCreation**](IventCollabsApi.md#iventcollabscontrollersearchcollabsforiventcreation) | **GET** /iventCollabs/search | Ivent oluştururken paydaş olabilecek hesapları listeler


# **leaveCollabrationByIventId**
> leaveCollabrationByIventId(iventId)

Ivent IDsi ile paydaş hesaplardan ayrılınır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventCollabsApi();
final iventId = iventId_example; // String | 

try {
    api_instance.leaveCollabrationByIventId(iventId);
} catch (e) {
    print('Exception when calling IventCollabsApi->leaveCollabrationByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeCollabByIventId**
> removeCollabByIventId(iventId, removeCollabByIventIdDto)

Ivent IDsi ile bir hesabı paydaş hesaplardan çıkartır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventCollabsApi();
final iventId = iventId_example; // String | 
final removeCollabByIventIdDto = RemoveCollabByIventIdDto(); // RemoveCollabByIventIdDto | 

try {
    api_instance.removeCollabByIventId(iventId, removeCollabByIventIdDto);
} catch (e) {
    print('Exception when calling IventCollabsApi->removeCollabByIventId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 
 **removeCollabByIventIdDto** | [**RemoveCollabByIventIdDto**](RemoveCollabByIventIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchCollabs**
> SearchCollabsReturn searchCollabs(iventId, q, limit, page)

Ivent'te bulunan paydaş hesapları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventCollabsApi();
final iventId = iventId_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchCollabs(iventId, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling IventCollabsApi->searchCollabs: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **iventId** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchCollabsReturn**](SearchCollabsReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchCollabsForIventCreation**
> SearchCollabsForIventCreationReturn searchCollabsForIventCreation(q, limit, page)

Ivent oluştururken paydaş olabilecek hesapları listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = IventCollabsApi();
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchCollabsForIventCreation(q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling IventCollabsApi->searchCollabsForIventCreation: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchCollabsForIventCreationReturn**](SearchCollabsForIventCreationReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


