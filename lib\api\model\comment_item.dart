//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CommentItem {
  /// Returns a new [CommentItem] instance.
  CommentItem({
    required this.commentId,
    required this.comment,
    required this.commenterUserId,
    required this.commenterUsername,
    required this.createdAt,
  });

  /// UUID of the comment
  String commentId;

  /// Text content of the comment
  String comment;

  /// UUID of the user who made the comment
  String commenterUserId;

  /// Username of the user who made the comment
  String commenterUsername;

  /// Timestamp when the comment was created in ISO 8601 date-time format
  String createdAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CommentItem &&
    other.commentId == commentId &&
    other.comment == comment &&
    other.commenterUserId == commenterUserId &&
    other.commenterUsername == commenterUsername &&
    other.createdAt == createdAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (commentId.hashCode) +
    (comment.hashCode) +
    (commenterUserId.hashCode) +
    (commenterUsername.hashCode) +
    (createdAt.hashCode);

  @override
  String toString() => 'CommentItem[commentId=$commentId, comment=$comment, commenterUserId=$commenterUserId, commenterUsername=$commenterUsername, createdAt=$createdAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'commentId'] = this.commentId;
      json[r'comment'] = this.comment;
      json[r'commenterUserId'] = this.commenterUserId;
      json[r'commenterUsername'] = this.commenterUsername;
      json[r'createdAt'] = this.createdAt;
    return json;
  }

  /// Returns a new [CommentItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CommentItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CommentItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CommentItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CommentItem(
        commentId: mapValueOfType<String>(json, r'commentId')!,
        comment: mapValueOfType<String>(json, r'comment')!,
        commenterUserId: mapValueOfType<String>(json, r'commenterUserId')!,
        commenterUsername: mapValueOfType<String>(json, r'commenterUsername')!,
        createdAt: mapValueOfType<String>(json, r'createdAt')!,
      );
    }
    return null;
  }

  static List<CommentItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CommentItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CommentItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CommentItem> mapFromJson(dynamic json) {
    final map = <String, CommentItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CommentItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CommentItem-objects as value to a dart map
  static Map<String, List<CommentItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CommentItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CommentItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'commentId',
    'comment',
    'commenterUserId',
    'commenterUsername',
    'createdAt',
  };
}

