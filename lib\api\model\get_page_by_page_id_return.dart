//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetPageByPageIdReturn {
  /// Returns a new [GetPageByPageIdReturn] instance.
  GetPageByPageIdReturn({
    required this.pageId,
    required this.pageName,
    this.thumbnailUrl,
    required this.createdIventCount,
    required this.followerCount,
    this.tagIds = const [],
    required this.haveMembership,
    required this.isFirstPerson,
  });

  /// UUID of the page
  String pageId;

  /// Name of the page
  String pageName;

  /// URL to the page thumbnail image
  String? thumbnailUrl;

  /// Number of ivents created by this page
  ///
  /// Minimum value: 0
  int createdIventCount;

  /// Number of followers of this page
  ///
  /// Minimum value: 0
  int followerCount;

  /// Hobby tag IDs associated with the page
  List<String> tagIds;

  /// Whether this page has membership functionality
  bool haveMembership;

  /// Whether the current user is the owner/creator of this page
  bool isFirstPerson;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetPageByPageIdReturn &&
    other.pageId == pageId &&
    other.pageName == pageName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.createdIventCount == createdIventCount &&
    other.followerCount == followerCount &&
    _deepEquality.equals(other.tagIds, tagIds) &&
    other.haveMembership == haveMembership &&
    other.isFirstPerson == isFirstPerson;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (pageId.hashCode) +
    (pageName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (createdIventCount.hashCode) +
    (followerCount.hashCode) +
    (tagIds.hashCode) +
    (haveMembership.hashCode) +
    (isFirstPerson.hashCode);

  @override
  String toString() => 'GetPageByPageIdReturn[pageId=$pageId, pageName=$pageName, thumbnailUrl=$thumbnailUrl, createdIventCount=$createdIventCount, followerCount=$followerCount, tagIds=$tagIds, haveMembership=$haveMembership, isFirstPerson=$isFirstPerson]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'pageId'] = this.pageId;
      json[r'pageName'] = this.pageName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'createdIventCount'] = this.createdIventCount;
      json[r'followerCount'] = this.followerCount;
      json[r'tagIds'] = this.tagIds;
      json[r'haveMembership'] = this.haveMembership;
      json[r'isFirstPerson'] = this.isFirstPerson;
    return json;
  }

  /// Returns a new [GetPageByPageIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetPageByPageIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetPageByPageIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetPageByPageIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetPageByPageIdReturn(
        pageId: mapValueOfType<String>(json, r'pageId')!,
        pageName: mapValueOfType<String>(json, r'pageName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        createdIventCount: mapValueOfType<int>(json, r'createdIventCount')!,
        followerCount: mapValueOfType<int>(json, r'followerCount')!,
        tagIds: json[r'tagIds'] is Iterable
            ? (json[r'tagIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        haveMembership: mapValueOfType<bool>(json, r'haveMembership')!,
        isFirstPerson: mapValueOfType<bool>(json, r'isFirstPerson')!,
      );
    }
    return null;
  }

  static List<GetPageByPageIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetPageByPageIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetPageByPageIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetPageByPageIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetPageByPageIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetPageByPageIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetPageByPageIdReturn-objects as value to a dart map
  static Map<String, List<GetPageByPageIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetPageByPageIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetPageByPageIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'pageId',
    'pageName',
    'createdIventCount',
    'followerCount',
    'tagIds',
    'haveMembership',
    'isFirstPerson',
  };
}

