//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetLatestLocationsReturn {
  /// Returns a new [GetLatestLocationsReturn] instance.
  GetLatestLocationsReturn({
    this.locations = const [],
    required this.locationCount,
  });

  /// List of latest locations
  List<LocationItem> locations;

  /// Total number of latest locations
  ///
  /// Minimum value: 0
  int locationCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetLatestLocationsReturn &&
    _deepEquality.equals(other.locations, locations) &&
    other.locationCount == locationCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (locations.hashCode) +
    (locationCount.hashCode);

  @override
  String toString() => 'GetLatestLocationsReturn[locations=$locations, locationCount=$locationCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'locations'] = this.locations;
      json[r'locationCount'] = this.locationCount;
    return json;
  }

  /// Returns a new [GetLatestLocationsReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetLatestLocationsReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetLatestLocationsReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetLatestLocationsReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetLatestLocationsReturn(
        locations: LocationItem.listFromJson(json[r'locations']),
        locationCount: mapValueOfType<int>(json, r'locationCount')!,
      );
    }
    return null;
  }

  static List<GetLatestLocationsReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetLatestLocationsReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetLatestLocationsReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetLatestLocationsReturn> mapFromJson(dynamic json) {
    final map = <String, GetLatestLocationsReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetLatestLocationsReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetLatestLocationsReturn-objects as value to a dart map
  static Map<String, List<GetLatestLocationsReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetLatestLocationsReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetLatestLocationsReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'locations',
    'locationCount',
  };
}

