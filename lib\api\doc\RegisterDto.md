# openapi.model.RegisterDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**phoneNumber** | **String** | Phone number in international format with country code | 
**fullname** | **String** | User's full name can only contain letters and spaces | 
**hobbyIds** | **List<String>** | Array of hobby UUIDs that the user is interested in | [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



