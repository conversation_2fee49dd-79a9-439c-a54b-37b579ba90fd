//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SideMenuPageItem {
  /// Returns a new [SideMenuPageItem] instance.
  SideMenuPageItem({
    required this.pageId,
    required this.pageName,
    required this.pageMembershipStatus,
    this.thumbnailUrl,
  });

  /// UUID of the page
  String pageId;

  /// Name of the page
  String pageName;

  PageMembershipStatusEnum pageMembershipStatus;

  /// URL to the page thumbnail image
  String? thumbnailUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SideMenuPageItem &&
    other.pageId == pageId &&
    other.pageName == pageName &&
    other.pageMembershipStatus == pageMembershipStatus &&
    other.thumbnailUrl == thumbnailUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (pageId.hashCode) +
    (pageName.hashCode) +
    (pageMembershipStatus.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode);

  @override
  String toString() => 'SideMenuPageItem[pageId=$pageId, pageName=$pageName, pageMembershipStatus=$pageMembershipStatus, thumbnailUrl=$thumbnailUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'pageId'] = this.pageId;
      json[r'pageName'] = this.pageName;
      json[r'pageMembershipStatus'] = this.pageMembershipStatus;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
    return json;
  }

  /// Returns a new [SideMenuPageItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SideMenuPageItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SideMenuPageItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SideMenuPageItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SideMenuPageItem(
        pageId: mapValueOfType<String>(json, r'pageId')!,
        pageName: mapValueOfType<String>(json, r'pageName')!,
        pageMembershipStatus: PageMembershipStatusEnum.fromJson(json[r'pageMembershipStatus'])!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
      );
    }
    return null;
  }

  static List<SideMenuPageItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SideMenuPageItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SideMenuPageItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SideMenuPageItem> mapFromJson(dynamic json) {
    final map = <String, SideMenuPageItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SideMenuPageItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SideMenuPageItem-objects as value to a dart map
  static Map<String, List<SideMenuPageItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SideMenuPageItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SideMenuPageItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'pageId',
    'pageName',
    'pageMembershipStatus',
  };
}

