//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchHobbiesReturn {
  /// Returns a new [SearchHobbiesReturn] instance.
  SearchHobbiesReturn({
    this.hobbies = const [],
    required this.hobbyCount,
  });

  /// List of hobby categories
  List<HobbyItem> hobbies;

  /// Total number of hobby categories
  ///
  /// Minimum value: 0
  int hobbyCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchHobbiesReturn &&
    _deepEquality.equals(other.hobbies, hobbies) &&
    other.hobbyCount == hobbyCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (hobbies.hashCode) +
    (hobbyCount.hashCode);

  @override
  String toString() => 'SearchHobbiesReturn[hobbies=$hobbies, hobbyCount=$hobbyCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'hobbies'] = this.hobbies;
      json[r'hobbyCount'] = this.hobbyCount;
    return json;
  }

  /// Returns a new [SearchHobbiesReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchHobbiesReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchHobbiesReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchHobbiesReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchHobbiesReturn(
        hobbies: HobbyItem.listFromJson(json[r'hobbies']),
        hobbyCount: mapValueOfType<int>(json, r'hobbyCount')!,
      );
    }
    return null;
  }

  static List<SearchHobbiesReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchHobbiesReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchHobbiesReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchHobbiesReturn> mapFromJson(dynamic json) {
    final map = <String, SearchHobbiesReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchHobbiesReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchHobbiesReturn-objects as value to a dart map
  static Map<String, List<SearchHobbiesReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchHobbiesReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchHobbiesReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'hobbies',
    'hobbyCount',
  };
}

