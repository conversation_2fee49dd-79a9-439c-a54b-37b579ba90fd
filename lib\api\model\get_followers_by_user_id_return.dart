//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetFollowersByUserIdReturn {
  /// Returns a new [GetFollowersByUserIdReturn] instance.
  GetFollowersByUserIdReturn({
    this.friendUsernames = const [],
    required this.friendCount,
    this.followers = const [],
    required this.followerCount,
    required this.isFirstPerson,
  });

  /// List of friend usernames
  List<String> friendUsernames;

  /// Total number of friends
  ///
  /// Minimum value: 0
  int friendCount;

  /// List of followers with their relationship status
  List<UserListItemWithRelationshipStatus> followers;

  /// Total number of followers
  ///
  /// Minimum value: 0
  int followerCount;

  /// Whether this is the current user's own profile
  bool isFirstPerson;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetFollowersByUserIdReturn &&
    _deepEquality.equals(other.friendUsernames, friendUsernames) &&
    other.friendCount == friendCount &&
    _deepEquality.equals(other.followers, followers) &&
    other.followerCount == followerCount &&
    other.isFirstPerson == isFirstPerson;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (friendUsernames.hashCode) +
    (friendCount.hashCode) +
    (followers.hashCode) +
    (followerCount.hashCode) +
    (isFirstPerson.hashCode);

  @override
  String toString() => 'GetFollowersByUserIdReturn[friendUsernames=$friendUsernames, friendCount=$friendCount, followers=$followers, followerCount=$followerCount, isFirstPerson=$isFirstPerson]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'friendUsernames'] = this.friendUsernames;
      json[r'friendCount'] = this.friendCount;
      json[r'followers'] = this.followers;
      json[r'followerCount'] = this.followerCount;
      json[r'isFirstPerson'] = this.isFirstPerson;
    return json;
  }

  /// Returns a new [GetFollowersByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetFollowersByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetFollowersByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetFollowersByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetFollowersByUserIdReturn(
        friendUsernames: json[r'friendUsernames'] is Iterable
            ? (json[r'friendUsernames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        friendCount: mapValueOfType<int>(json, r'friendCount')!,
        followers: UserListItemWithRelationshipStatus.listFromJson(json[r'followers']),
        followerCount: mapValueOfType<int>(json, r'followerCount')!,
        isFirstPerson: mapValueOfType<bool>(json, r'isFirstPerson')!,
      );
    }
    return null;
  }

  static List<GetFollowersByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetFollowersByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetFollowersByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetFollowersByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetFollowersByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetFollowersByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetFollowersByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetFollowersByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetFollowersByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetFollowersByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'friendUsernames',
    'friendCount',
    'followers',
    'followerCount',
    'isFirstPerson',
  };
}

