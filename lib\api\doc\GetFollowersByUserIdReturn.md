# openapi.model.GetFollowersByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**friendUsernames** | **List<String>** | List of friend usernames | [default to const []]
**friendCount** | **int** | Total number of friends | 
**followers** | [**List<UserListItemWithRelationshipStatus>**](UserListItemWithRelationshipStatus.md) | List of followers with their relationship status | [default to const []]
**followerCount** | **int** | Total number of followers | 
**isFirstPerson** | **bool** | Whether this is the current user's own profile | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



