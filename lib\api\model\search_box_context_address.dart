//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxContextAddress {
  /// Returns a new [SearchBoxContextAddress] instance.
  SearchBoxContextAddress({
    this.id,
    this.name,
    this.addressNumber,
    this.streetName,
  });

  /// Address ID
  String? id;

  /// Address name
  String? name;

  /// Address number
  String? addressNumber;

  /// Street name
  String? streetName;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxContextAddress &&
    other.id == id &&
    other.name == name &&
    other.addressNumber == addressNumber &&
    other.streetName == streetName;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id == null ? 0 : id!.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (addressNumber == null ? 0 : addressNumber!.hashCode) +
    (streetName == null ? 0 : streetName!.hashCode);

  @override
  String toString() => 'SearchBoxContextAddress[id=$id, name=$name, addressNumber=$addressNumber, streetName=$streetName]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.id != null) {
      json[r'id'] = this.id;
    } else {
      json[r'id'] = null;
    }
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    if (this.addressNumber != null) {
      json[r'address_number'] = this.addressNumber;
    } else {
      json[r'address_number'] = null;
    }
    if (this.streetName != null) {
      json[r'street_name'] = this.streetName;
    } else {
      json[r'street_name'] = null;
    }
    return json;
  }

  /// Returns a new [SearchBoxContextAddress] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxContextAddress? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxContextAddress[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxContextAddress[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxContextAddress(
        id: mapValueOfType<String>(json, r'id'),
        name: mapValueOfType<String>(json, r'name'),
        addressNumber: mapValueOfType<String>(json, r'address_number'),
        streetName: mapValueOfType<String>(json, r'street_name'),
      );
    }
    return null;
  }

  static List<SearchBoxContextAddress> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxContextAddress>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxContextAddress.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxContextAddress> mapFromJson(dynamic json) {
    final map = <String, SearchBoxContextAddress>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxContextAddress.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxContextAddress-objects as value to a dart map
  static Map<String, List<SearchBoxContextAddress>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxContextAddress>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxContextAddress.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

