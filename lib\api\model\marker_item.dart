//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MarkerItem {
  /// Returns a new [MarkerItem] instance.
  MarkerItem({
    required this.iventId,
    required this.latitude,
    required this.longitude,
  });

  /// UUID of the ivent
  String iventId;

  /// Latitude coordinate of the ivent
  double latitude;

  /// Longitude coordinate of the ivent
  double longitude;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MarkerItem &&
    other.iventId == iventId &&
    other.latitude == latitude &&
    other.longitude == longitude;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (iventId.hashCode) +
    (latitude.hashCode) +
    (longitude.hashCode);

  @override
  String toString() => 'MarkerItem[iventId=$iventId, latitude=$latitude, longitude=$longitude]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'iventId'] = this.iventId;
      json[r'latitude'] = this.latitude;
      json[r'longitude'] = this.longitude;
    return json;
  }

  /// Returns a new [MarkerItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MarkerItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MarkerItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MarkerItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MarkerItem(
        iventId: mapValueOfType<String>(json, r'iventId')!,
        latitude: mapValueOfType<double>(json, r'latitude')!,
        longitude: mapValueOfType<double>(json, r'longitude')!,
      );
    }
    return null;
  }

  static List<MarkerItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MarkerItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MarkerItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MarkerItem> mapFromJson(dynamic json) {
    final map = <String, MarkerItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MarkerItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MarkerItem-objects as value to a dart map
  static Map<String, List<MarkerItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MarkerItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = MarkerItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'iventId',
    'latitude',
    'longitude',
  };
}

