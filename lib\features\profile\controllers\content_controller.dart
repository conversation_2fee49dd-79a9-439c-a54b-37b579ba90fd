import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Controller for managing user content (Vibes and memories)
///
/// Handles loading and managing user's Vibe folders and memory folders.
/// Manages the content tab functionality in user profiles.
class ContentController extends BaseControllerWithSharedState<ProfileStateManager> {
  // Reactive state
  final _vibeFolders = Rxn<GetVibeFoldersByUserIdReturn>();
  final _memoryFolders = Rxn<GetMemoryFoldersByUserIdReturn>();

  // Constructor
  ContentController(AuthService authService, ProfileStateManager state) : super(authService, state);

  // Getters
  GetVibeFoldersByUserIdReturn? get vibeFolders => _vibeFolders.value;
  GetMemoryFoldersByUserIdReturn? get memoryFolders => _memoryFolders.value;

  // Setters
  set vibeFolders(GetVibeFoldersByUserIdReturn? value) => _vibeFolders.value = value;
  set memoryFolders(GetMemoryFoldersByUserIdReturn? value) => _memoryFolders.value = value;

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();
    await loadFolders();
  }

  // Public methods

  /// Loads user's Vibe and memory folders
  Future<void> loadFolders() async {
    vibeFolders = await authService.usersApi.getVibeFoldersByUserId(state.userId);
    // TODO: Load memory folders when API is available
    // memoryFolders = await authService.usersApi.getMemoryFoldersByUserId(state.userId);
  }
}
