import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class LocationController extends BaseHomeController {
  LocationController(AuthService authService, HomeStateManager state) : super(authService, state) {
    mapboxController = MapboxController(authService: authService);
  }

  late final BaseSearchBarController baseSearchBarController;
  late final MapboxController mapboxController;

  final _placeSuggestionResults = <SearchBoxSuggestFeature>[].obs;

  List<SearchBoxSuggestFeature> get placeSuggestionResults => _placeSuggestionResults;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => placeSuggestionResults.isEmpty;

  @override
  void initController() async {
    super.initController();
    baseSearchBarController = Get.put(BaseSearchBarController(_searchPlaces), tag: 'LocationController');
    await mapboxController.getUserLocationCoordinates();
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'LocationController');
    super.closeController();
  }

  Future<void> _searchPlaces(String? q) async {
    if (q == null) return;
    final result = await authService.mapboxApi.searchBoxSuggest(
      q,
      sessionUser.sessionId,
      limit: 10,
      proximity: mapboxController.userLocation != null
          ? '${mapboxController.userLocationCoordinates!.lng},${mapboxController.userLocationCoordinates!.lat}'
          : null,
      types: 'country,region,postcode,district,place,city,locality,neighborhood,street,address,poi,category',
    );
    if (result != null) {
      placeSuggestionResults.assignAll(result.suggestions);
    }
  }

  Future<void> useCurrentLocation() async {
    if (mapboxController.userLocation == null) return;
    state.selectedPlace = mapboxController.userLocation!;
    baseSearchBarController.text = mapboxController.userLocation!.name;
    Get.back();
  }

  Future<void> selectPlace(SearchBoxSuggestFeature suggestion) async {
    final result = await authService.mapboxApi.searchBoxRetrieve(
      suggestion.mapboxId,
      sessionUser.sessionId,
    );

    if (result != null) {
      state.selectedPlace = IaLocationItem.fromProperties(result.features[0].properties);
      baseSearchBarController.text = suggestion.name;
      placeSuggestionResults.clear();
    }
  }
}
