//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateCommentDto {
  /// Returns a new [CreateCommentDto] instance.
  CreateCommentDto({
    required this.comment,
    required this.vibeId,
    required this.iventName,
    this.thumbnailUrl,
    required this.creatorId,
  });

  /// The comment text content
  String comment;

  /// UUID of the vibe being commented on
  String vibeId;

  /// An ivent name can only contain letters, numbers, underscores, and hyphens.
  String iventName;

  /// Optional thumbnail URL for the comment
  String? thumbnailUrl;

  /// UUID of the user creating the comment
  String creatorId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateCommentDto &&
    other.comment == comment &&
    other.vibeId == vibeId &&
    other.iventName == iventName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.creatorId == creatorId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (comment.hashCode) +
    (vibeId.hashCode) +
    (iventName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (creatorId.hashCode);

  @override
  String toString() => 'CreateCommentDto[comment=$comment, vibeId=$vibeId, iventName=$iventName, thumbnailUrl=$thumbnailUrl, creatorId=$creatorId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'comment'] = this.comment;
      json[r'vibeId'] = this.vibeId;
      json[r'iventName'] = this.iventName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'creatorId'] = this.creatorId;
    return json;
  }

  /// Returns a new [CreateCommentDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateCommentDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateCommentDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateCommentDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateCommentDto(
        comment: mapValueOfType<String>(json, r'comment')!,
        vibeId: mapValueOfType<String>(json, r'vibeId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        creatorId: mapValueOfType<String>(json, r'creatorId')!,
      );
    }
    return null;
  }

  static List<CreateCommentDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateCommentDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateCommentDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateCommentDto> mapFromJson(dynamic json) {
    final map = <String, CreateCommentDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateCommentDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateCommentDto-objects as value to a dart map
  static Map<String, List<CreateCommentDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateCommentDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateCommentDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'comment',
    'vibeId',
    'iventName',
    'creatorId',
  };
}

