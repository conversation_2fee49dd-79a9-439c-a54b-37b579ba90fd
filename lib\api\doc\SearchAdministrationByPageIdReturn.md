# openapi.model.SearchAdministrationByPageIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**users** | [**List<UserListItemWithPageRole>**](UserListItemWithPageRole.md) | List of page administrators and moderators | [default to const []]
**userCount** | **int** | Total number of page administrators and moderators | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



