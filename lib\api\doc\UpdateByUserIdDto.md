# openapi.model.UpdateByUserIdDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**newUsername** | **String** | Username can only contain letters, numbers, underscores, and hyphens | 
**newBirthday** | **String** | Birthday, in ISO 8601 date-time format | 
**newGender** | [**UserGenderEnum**](UserGenderEnum.md) |  | 
**newAvatarUrl** | **String** | URL to the user's avatar image | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



