//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxContext {
  /// Returns a new [SearchBoxContext] instance.
  SearchBoxContext({
    this.region,
    this.postcode,
    this.district,
    this.place,
    this.locality,
    this.neighborhood,
    this.address,
    this.street,
  });

  /// Region context information
  SearchBoxContextRegion? region;

  /// Postcode context information
  SearchBoxContextPostcode? postcode;

  /// District context information
  SearchBoxContextDistrict? district;

  /// Place context information
  SearchBoxContextPlace? place;

  /// Locality context information
  SearchBoxContextLocality? locality;

  /// Neighborhood context information
  SearchBoxContextNeighborhood? neighborhood;

  /// Address context information
  SearchBoxContextAddress? address;

  /// Street context information
  SearchBoxContextStreet? street;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxContext &&
    other.region == region &&
    other.postcode == postcode &&
    other.district == district &&
    other.place == place &&
    other.locality == locality &&
    other.neighborhood == neighborhood &&
    other.address == address &&
    other.street == street;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (region == null ? 0 : region!.hashCode) +
    (postcode == null ? 0 : postcode!.hashCode) +
    (district == null ? 0 : district!.hashCode) +
    (place == null ? 0 : place!.hashCode) +
    (locality == null ? 0 : locality!.hashCode) +
    (neighborhood == null ? 0 : neighborhood!.hashCode) +
    (address == null ? 0 : address!.hashCode) +
    (street == null ? 0 : street!.hashCode);

  @override
  String toString() => 'SearchBoxContext[region=$region, postcode=$postcode, district=$district, place=$place, locality=$locality, neighborhood=$neighborhood, address=$address, street=$street]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.region != null) {
      json[r'region'] = this.region;
    } else {
      json[r'region'] = null;
    }
    if (this.postcode != null) {
      json[r'postcode'] = this.postcode;
    } else {
      json[r'postcode'] = null;
    }
    if (this.district != null) {
      json[r'district'] = this.district;
    } else {
      json[r'district'] = null;
    }
    if (this.place != null) {
      json[r'place'] = this.place;
    } else {
      json[r'place'] = null;
    }
    if (this.locality != null) {
      json[r'locality'] = this.locality;
    } else {
      json[r'locality'] = null;
    }
    if (this.neighborhood != null) {
      json[r'neighborhood'] = this.neighborhood;
    } else {
      json[r'neighborhood'] = null;
    }
    if (this.address != null) {
      json[r'address'] = this.address;
    } else {
      json[r'address'] = null;
    }
    if (this.street != null) {
      json[r'street'] = this.street;
    } else {
      json[r'street'] = null;
    }
    return json;
  }

  /// Returns a new [SearchBoxContext] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxContext? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxContext[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxContext[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxContext(
        region: SearchBoxContextRegion.fromJson(json[r'region']),
        postcode: SearchBoxContextPostcode.fromJson(json[r'postcode']),
        district: SearchBoxContextDistrict.fromJson(json[r'district']),
        place: SearchBoxContextPlace.fromJson(json[r'place']),
        locality: SearchBoxContextLocality.fromJson(json[r'locality']),
        neighborhood: SearchBoxContextNeighborhood.fromJson(json[r'neighborhood']),
        address: SearchBoxContextAddress.fromJson(json[r'address']),
        street: SearchBoxContextStreet.fromJson(json[r'street']),
      );
    }
    return null;
  }

  static List<SearchBoxContext> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxContext>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxContext.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxContext> mapFromJson(dynamic json) {
    final map = <String, SearchBoxContext>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxContext.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxContext-objects as value to a dart map
  static Map<String, List<SearchBoxContext>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxContext>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxContext.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

