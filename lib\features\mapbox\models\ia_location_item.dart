import 'package:ivent_app/api/api.dart';

/// Represents a location item used throughout the iVent app
///
/// Contains location information including coordinates, name, and address.
/// Can be created from Mapbox search results or other location sources.
class IaLocationItem {
  final String? mapboxId;
  final String? locationId;
  final String name;
  final String address;
  final double latitude;
  final double longitude;

  IaLocationItem({
    required this.mapboxId,
    required this.locationId,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  }) : assert(mapboxId != null || locationId != null, 'Either mapboxId or locationId must be provided');

  /// Creates an IaLocationItem from Mapbox SearchBoxProperties
  factory IaLocationItem.fromProperties(SearchBoxProperties properties) {
    return IaLocationItem(
      mapboxId: properties.mapboxId,
      locationId: properties.mapboxId,
      name: properties.name,
      address: properties.fullAddress ?? properties.placeFormatted,
      latitude: properties.coordinates.latitude,
      longitude: properties.coordinates.longitude,
    );
  }

  @override
  String toString() {
    return 'IaLocationItem(name: $name, address: $address, lat: $latitude, lng: $longitude)';
  }
}
