//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class NotificationsApi {
  NotificationsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Bildirimleri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getNotificationsWithHttpInfo({ int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/notifications';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Bildirimleri listeler
  ///
  /// Parameters:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetNotificationsReturn?> getNotifications({ int? limit, int? page, }) async {
    final response = await getNotificationsWithHttpInfo( limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetNotificationsReturn',) as GetNotificationsReturn;
    
    }
    return null;
  }

  /// Squad IDsi ile seçilen davete \"accept\" ya da \"reject\" yanıtı verilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [NotificationReplyTypeEnum] reply (required):
  ///   Reply type for the notification
  Future<Response> replyInvitationBySquadIdWithHttpInfo(String id, NotificationReplyTypeEnum reply,) async {
    // ignore: prefer_const_declarations
    final path = r'/notifications/{id}/reply'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'reply', reply));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Squad IDsi ile seçilen davete \"accept\" ya da \"reject\" yanıtı verilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [NotificationReplyTypeEnum] reply (required):
  ///   Reply type for the notification
  Future<void> replyInvitationBySquadId(String id, NotificationReplyTypeEnum reply,) async {
    final response = await replyInvitationBySquadIdWithHttpInfo(id, reply,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

