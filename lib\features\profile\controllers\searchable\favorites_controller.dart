import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/app/routes/profile.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class FavoritesController extends BaseControllerWithSharedState<ProfileStateManager> {
  FavoritesController(AuthService authService, ProfileStateManager state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarController;

  final _favoritesResult = Rxn<GetFavoritesByUserIdReturn>();
  final _favoritedIventIds = RxList<String>([]);

  GetFavoritesByUserIdReturn? get favoritesResult => _favoritesResult.value;
  List<String> get favoritedIventIds => _favoritedIventIds;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => favoritesResult?.ivents.isEmpty ?? true;

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController = Get.put(
      BaseSearchBarController((q) => _searchFavorites(q: q)),
      tag: 'FavoritesController',
    );
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'FavoritesController');
    super.closeController();
  }

  Future<void> _searchFavorites({String? q}) async {
    _favoritesResult.value = await authService.usersApi.getFavoritesByUserId(
      state.userId,
      q: q,
    );
    if (favoritesResult != null) {
      _favoritedIventIds.value = favoritesResult!.ivents.map((e) => e.iventId).toList();
    }
  }

  void toggleFavorite(String iventId) {
    if (favoritedIventIds.contains(iventId)) {
      favoritedIventIds.remove(iventId);
      authService.iventsApi.unfavoriteIventByIventId(iventId);
    } else {
      favoritedIventIds.add(iventId);
      authService.iventsApi.favoriteIventByIventId(iventId);
    }
  }

  Future<void> goToFavoritesPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_FAVORILER, arguments: state.userId);
    if (favoritesResult == null) await _searchFavorites();
  }
}
