//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class PagesApi {
  PagesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Sayfa IDsi ile sayfa engellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> blockPageByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/block'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfa engellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> blockPageByPageId(String id,) async {
    final response = await blockPageByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa oluşturur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreatePageDto] createPageDto (required):
  Future<Response> createPageWithHttpInfo(CreatePageDto createPageDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/create';

    // ignore: prefer_final_locals
    Object? postBody = createPageDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa oluşturur
  ///
  /// Parameters:
  ///
  /// * [CreatePageDto] createPageDto (required):
  Future<CreatePageReturn?> createPage(CreatePageDto createPageDto,) async {
    final response = await createPageWithHttpInfo(createPageDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreatePageReturn',) as CreatePageReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile sayfayı siler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deletePageByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfayı siler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deletePageByPageId(String id,) async {
    final response = await deletePageByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile takip edilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> followByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/follow'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile takip edilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> followByPageId(String id,) async {
    final response = await followByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile sayfanın detaylarını listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getDetailsByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/details'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın detaylarını listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetPageDetailsByPageIdReturn?> getDetailsByPageId(String id,) async {
    final response = await getDetailsByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetPageDetailsByPageIdReturn',) as GetPageDetailsByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile sayfanın iventlerini listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getIventsCreatedByPageIdWithHttpInfo(String id, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/ivents'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın iventlerini listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetIventsCreatedByPageIdReturn?> getIventsCreatedByPageId(String id, { int? limit, int? page, }) async {
    final response = await getIventsCreatedByPageIdWithHttpInfo(id,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetIventsCreatedByPageIdReturn',) as GetIventsCreatedByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile sayfanın bilgilerini listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getPageByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın bilgilerini listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetPageByPageIdReturn?> getPageByPageId(String id,) async {
    final response = await getPageByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetPageByPageIdReturn',) as GetPageByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile sayfanın vibelarını listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getVibesByPageIdWithHttpInfo(String id, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/vibes'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın vibelarını listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetVibeFoldersByPageIdReturn?> getVibesByPageId(String id, { int? limit, int? page, }) async {
    final response = await getVibesByPageIdWithHttpInfo(id,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetVibeFoldersByPageIdReturn',) as GetVibeFoldersByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile takipçi çıkartılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [RemoveFollowerByPageIdDto] removeFollowerByPageIdDto (required):
  Future<Response> removeFollowerByPageIdWithHttpInfo(String id, RemoveFollowerByPageIdDto removeFollowerByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/followers/remove'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = removeFollowerByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile takipçi çıkartılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [RemoveFollowerByPageIdDto] removeFollowerByPageIdDto (required):
  Future<void> removeFollowerByPageId(String id, RemoveFollowerByPageIdDto removeFollowerByPageIdDto,) async {
    final response = await removeFollowerByPageIdWithHttpInfo(id, removeFollowerByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile takipçileri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchFollowersByPageIdWithHttpInfo(String id, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/followers'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile takipçileri listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchFollowersByPageIdReturn?> searchFollowersByPageId(String id, { String? q, int? limit, int? page, }) async {
    final response = await searchFollowersByPageIdWithHttpInfo(id,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchFollowersByPageIdReturn',) as SearchFollowersByPageIdReturn;
    
    }
    return null;
  }

  /// Sayfa IDsi ile etkinlik bildirimlerini açar
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> subscribeByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/subscribe'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile etkinlik bildirimlerini açar
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> subscribeByPageId(String id,) async {
    final response = await subscribeByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile sayfa engeli kaldırılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unblockPageByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/unblock'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfa engeli kaldırılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unblockPageByPageId(String id,) async {
    final response = await unblockPageByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile takipten çıkılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unfollowByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/unfollow'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile takipten çıkılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unfollowByPageId(String id,) async {
    final response = await unfollowByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile etkinlik bildirimlerini kapatır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unsubscribeByPageIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/unsubscribe'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile etkinlik bildirimlerini kapatır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unsubscribeByPageId(String id,) async {
    final response = await unsubscribeByPageIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile sayfanın açıklamasını günceller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateDescriptionByPageIdDto] updateDescriptionByPageIdDto (required):
  Future<Response> updateDescriptionByPageIdWithHttpInfo(String id, UpdateDescriptionByPageIdDto updateDescriptionByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/update/description'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateDescriptionByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın açıklamasını günceller
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateDescriptionByPageIdDto] updateDescriptionByPageIdDto (required):
  Future<void> updateDescriptionByPageId(String id, UpdateDescriptionByPageIdDto updateDescriptionByPageIdDto,) async {
    final response = await updateDescriptionByPageIdWithHttpInfo(id, updateDescriptionByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile sayfanın linklerini günceller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateLinksByPageIdDto] updateLinksByPageIdDto (required):
  Future<Response> updateLinksByPageIdWithHttpInfo(String id, UpdateLinksByPageIdDto updateLinksByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/update/links'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateLinksByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın linklerini günceller
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateLinksByPageIdDto] updateLinksByPageIdDto (required):
  Future<void> updateLinksByPageId(String id, UpdateLinksByPageIdDto updateLinksByPageIdDto,) async {
    final response = await updateLinksByPageIdWithHttpInfo(id, updateLinksByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sayfa IDsi ile sayfanın konumunu günceller
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateLocationByPageIdDto] updateLocationByPageIdDto (required):
  Future<Response> updateLocationByPageIdWithHttpInfo(String id, UpdateLocationByPageIdDto updateLocationByPageIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/pages/{id}/update/location'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateLocationByPageIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sayfa IDsi ile sayfanın konumunu günceller
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateLocationByPageIdDto] updateLocationByPageIdDto (required):
  Future<void> updateLocationByPageId(String id, UpdateLocationByPageIdDto updateLocationByPageIdDto,) async {
    final response = await updateLocationByPageIdWithHttpInfo(id, updateLocationByPageIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

