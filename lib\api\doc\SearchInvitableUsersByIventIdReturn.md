# openapi.model.SearchInvitableUsersByIventIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**groups** | [**List<GroupListItem>**](GroupListItem.md) | List of groups that can be invited to the ivent | [default to const []]
**groupCount** | **int** | Total number of invitable groups | 
**friends** | [**List<UserListItem>**](UserListItem.md) | List of users that can be invited to the ivent | [default to const []]
**friendCount** | **int** | Total number of invitable users | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



