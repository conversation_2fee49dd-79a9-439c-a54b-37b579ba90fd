# openapi.model.GetLatestIventsReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ivents** | [**List<IventListItem>**](IventListItem.md) | List of latest ivents | [default to const []]
**iventCount** | **int** | Total number of latest ivents | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



