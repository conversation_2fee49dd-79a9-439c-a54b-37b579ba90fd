//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class MemoriesApi {
  MemoriesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Memory oluşturur
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [CreateMemoryDto] createMemoryDto (required):
  Future<Response> createMemoryWithHttpInfo(CreateMemoryDto createMemoryDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/memories/create';

    // ignore: prefer_final_locals
    Object? postBody = createMemoryDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Memory oluşturur
  ///
  /// Parameters:
  ///
  /// * [CreateMemoryDto] createMemoryDto (required):
  Future<CreateMemoryReturn?> createMemory(CreateMemoryDto createMemoryDto,) async {
    final response = await createMemoryWithHttpInfo(createMemoryDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'CreateMemoryReturn',) as CreateMemoryReturn;
    
    }
    return null;
  }

  /// Memory IDsi ile memory siler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deleteMemoryByMemoryIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/memories/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Memory IDsi ile memory siler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deleteMemoryByMemoryId(String id,) async {
    final response = await deleteMemoryByMemoryIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// ID ile memory getirir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [MemoryOriginEnum] origin (required):
  ///   Origin of the memory
  Future<Response> getMemoryByMemoryIdWithHttpInfo(String id, MemoryOriginEnum origin,) async {
    // ignore: prefer_const_declarations
    final path = r'/memories/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'origin', origin));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// ID ile memory getirir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [MemoryOriginEnum] origin (required):
  ///   Origin of the memory
  Future<GetMemoryByMemoryIdReturn?> getMemoryByMemoryId(String id, MemoryOriginEnum origin,) async {
    final response = await getMemoryByMemoryIdWithHttpInfo(id, origin,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetMemoryByMemoryIdReturn',) as GetMemoryByMemoryIdReturn;
    
    }
    return null;
  }
}

