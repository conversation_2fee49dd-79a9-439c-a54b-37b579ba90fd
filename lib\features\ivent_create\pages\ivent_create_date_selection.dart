import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/inputs/ivent_create_date_picker.dart';
import 'package:ivent_app/features/ivent_create/widgets/navigation/ivent_name_top_bar_child.dart';

class IventCreateDateSelection extends StatelessWidget {
  final IventCreateController _controller = Get.find();

  IventCreateFormController get _formController => _controller.formController;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaScaffold.noSearch(
        child: _buildTopBarChild(),
        showDivider: false,
        body: _buildDateBody(),
        floatingActionButton: IaFloatingActionButton(
          onPressed: _controller.goToMapPage,
          text: 'Devam Et',
          isPrimary: false,
          isEnabled: _formController.dates.isNotEmpty,
        ),
      );
    });
  }

  Widget _buildTopBarChild() => IventNameTopBarChild(controller: _controller);

  Widget _buildDateBody() {
    return Obx(() {
      return ListView.separated(
        padding: const EdgeInsets.only(top: AppDimensions.padding20, bottom: 100),
        itemCount: _formController.dates.length + 1,
        itemBuilder: (context, index) {
          if (index == 0 && _formController.dates.isEmpty) {
            return IaRoundedContainer(
              onTap: _openDatePicker,
              margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
              padding: const EdgeInsets.all(20),
              roundness: AppDimensions.radiusS,
              color: AppColors.lightGrey,
              child: Row(
                children: [
                  IaRoundedButton(
                    color: AppColors.transparent,
                    text: 'Tarih & Saat',
                    textStyle: AppTextStyles.size16RegularTextSecondary,
                    leading: const IaSvgIcon(
                      iconPath: AppAssets.calendar,
                      iconColor: AppColors.darkGrey,
                      iconSize: 20,
                    ),
                  ),
                  const Spacer(),
                  Text('Seçiniz', style: AppTextStyles.size16RegularTextSecondary),
                ],
              ),
            );
          } else {
            if (index == _formController.dates.length) {
              return Center(
                child: IaRoundedButton(
                  onTap: _openDatePicker,
                  color: AppColors.transparent,
                  text: 'Gün Ekle',
                  textStyle: AppTextStyles.size16RegularTextSecondary,
                  leading: const IaSvgIcon(iconPath: AppAssets.addPlus, iconColor: AppColors.darkGrey),
                ),
              );
            } else {
              final element = _formController.dates[index];
              return IaRoundedContainer(
                onTap: _openDatePicker,
                margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
                padding: const EdgeInsets.all(AppDimensions.padding20),
                roundness: AppDimensions.radiusS,
                color: AppColors.lightGrey,
                child: Row(
                  children: [
                    IaRoundedButton(
                      color: AppColors.transparent,
                      text: _formController.dates.length == 1 ? 'Tarih & Saat' : '${index + 1}. Gün',
                      textStyle: AppTextStyles.size16RegularTextSecondary,
                      leading: IaIconButton(
                        iconPath: index == 0 ? AppAssets.calendar : AppAssets.closeSM,
                        iconColor: AppColors.darkGrey,
                        onPressed: () {
                          final dates = List<DateTime>.from(_formController.dates);
                          dates.removeAt(index);
                          _formController.dates = dates;
                        },
                      ),
                    ),
                    const Spacer(),
                    Text(
                      DateFormat('d MMMM yyyy  HH:mm').format(element),
                      style: AppTextStyles.size16Regular,
                    ),
                  ],
                ),
              );
            }
          }
        },
        separatorBuilder: IaListTile.separatorBuilder20,
      );
    });
  }

  void _openDatePicker({int? index}) {
    Get.bottomSheet(
      IventCreateDatePicker(
        onDone: (dateTime) {
          final dates = List<DateTime>.from(_formController.dates);
          if (index != null) {
            dates[index] = dateTime;
          } else {
            dates.add(dateTime);
          }
          _formController.dates = dates;
        },
        initialDateTime: index == null ? null : _formController.dates[index],
      ),
      isScrollControlled: true,
    );
  }
}
