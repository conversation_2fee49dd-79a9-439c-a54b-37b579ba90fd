import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';

class VibeFolder {
  final int maxCachedVibelets = 10;
  final String vibeFolderId;
  final String? thumbnailUrl;
  RxMap<int, Vibe> _vibelets;
  RxInt _currentVibeletIndex;

  Map<int, Vibe> get vibelets => _vibelets;
  List<String> get vibeletIds => _vibelets.values.map((v) => v.content.vibeId).toList();
  int get currentVibeletIndex => _currentVibeletIndex.value;
  Vibe get currentVibelet => vibelets[currentVibeletIndex]!;

  set vibelets(Map<int, Vibe> value) => _vibelets.value = value;
  set currentVibeletIndex(int value) => _currentVibeletIndex.value = value;

  VibeFolder({
    required this.vibeFolderId,
    required this.thumbnailUrl,
    required int initialVibeIndex,
    required Map<int, Vibe> vibelets,
  })  : _vibelets = vibelets.obs,
        _currentVibeletIndex = initialVibeIndex.obs;

  void addNewVibelet(Vibe vibelet) {
    final vibeletIndex = vibelet.content.vibeIndex;

    if (vibelets.containsKey(vibeletIndex)) {
      currentVibeletIndex = vibeletIndex;
      return;
    }

    if (vibelets.length >= maxCachedVibelets) {
      final oldest = vibelets.remove(vibelets.keys.first)!;
      oldest.video?.disposeVideoPlayerController();
    }

    vibelets[vibeletIndex] = vibelet;
    currentVibeletIndex = vibeletIndex;
  }

  void setCurrentVibeletById(String vibeId) {
    try {
      currentVibeletIndex = vibelets.entries.firstWhere((entry) => entry.value.content.vibeId == vibeId).key;
    } catch (e) {
      print('Vibe $vibeId not found in folder: $vibeFolderId');
    }
  }

  void dispose() {
    for (final vibelet in vibelets.values) {
      vibelet.video?.disposeVideoPlayerController();
    }
  }

  void clear() {
    dispose();
    vibelets.clear();
  }

  @override
  String toString() {
    return 'VibeFolder{vibeFolderId: $vibeFolderId, thumbnailUrl: $thumbnailUrl, currentVibeletIndex: $currentVibeletIndex}';
  }
}
