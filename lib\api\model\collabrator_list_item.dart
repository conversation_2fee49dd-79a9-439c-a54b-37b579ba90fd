//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CollabratorListItem {
  /// Returns a new [CollabratorListItem] instance.
  CollabratorListItem({
    required this.collabId,
    required this.collabName,
    required this.collabType,
    this.collabImageUrl,
    required this.pageMembershipStatus,
    required this.relationshipStatus,
  });

  /// UUID of the collaborator
  String collabId;

  /// Name of the collaborator
  String collabName;

  AccountTypeEnum collabType;

  /// URL to the collaborator image
  String? collabImageUrl;

  PageMembershipStatusEnum pageMembershipStatus;

  UserRelationshipStatusEnum relationshipStatus;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CollabratorListItem &&
    other.collabId == collabId &&
    other.collabName == collabName &&
    other.collabType == collabType &&
    other.collabImageUrl == collabImageUrl &&
    other.pageMembershipStatus == pageMembershipStatus &&
    other.relationshipStatus == relationshipStatus;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (collabId.hashCode) +
    (collabName.hashCode) +
    (collabType.hashCode) +
    (collabImageUrl == null ? 0 : collabImageUrl!.hashCode) +
    (pageMembershipStatus.hashCode) +
    (relationshipStatus.hashCode);

  @override
  String toString() => 'CollabratorListItem[collabId=$collabId, collabName=$collabName, collabType=$collabType, collabImageUrl=$collabImageUrl, pageMembershipStatus=$pageMembershipStatus, relationshipStatus=$relationshipStatus]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'collabId'] = this.collabId;
      json[r'collabName'] = this.collabName;
      json[r'collabType'] = this.collabType;
    if (this.collabImageUrl != null) {
      json[r'collabImageUrl'] = this.collabImageUrl;
    } else {
      json[r'collabImageUrl'] = null;
    }
      json[r'pageMembershipStatus'] = this.pageMembershipStatus;
      json[r'relationshipStatus'] = this.relationshipStatus;
    return json;
  }

  /// Returns a new [CollabratorListItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CollabratorListItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CollabratorListItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CollabratorListItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CollabratorListItem(
        collabId: mapValueOfType<String>(json, r'collabId')!,
        collabName: mapValueOfType<String>(json, r'collabName')!,
        collabType: AccountTypeEnum.fromJson(json[r'collabType'])!,
        collabImageUrl: mapValueOfType<String>(json, r'collabImageUrl'),
        pageMembershipStatus: PageMembershipStatusEnum.fromJson(json[r'pageMembershipStatus'])!,
        relationshipStatus: UserRelationshipStatusEnum.fromJson(json[r'relationshipStatus'])!,
      );
    }
    return null;
  }

  static List<CollabratorListItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CollabratorListItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CollabratorListItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CollabratorListItem> mapFromJson(dynamic json) {
    final map = <String, CollabratorListItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CollabratorListItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CollabratorListItem-objects as value to a dart map
  static Map<String, List<CollabratorListItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CollabratorListItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CollabratorListItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'collabId',
    'collabName',
    'collabType',
    'pageMembershipStatus',
    'relationshipStatus',
  };
}

