//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxFeature {
  /// Returns a new [SearchBoxFeature] instance.
  SearchBoxFeature({
    required this.type,
    required this.geometry,
    required this.properties,
  });

  /// Feature type
  String type;

  /// Geometry of the feature
  SearchBoxGeometry geometry;

  /// Properties of the feature
  SearchBoxProperties properties;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxFeature &&
    other.type == type &&
    other.geometry == geometry &&
    other.properties == properties;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (type.hashCode) +
    (geometry.hashCode) +
    (properties.hashCode);

  @override
  String toString() => 'SearchBoxFeature[type=$type, geometry=$geometry, properties=$properties]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'type'] = this.type;
      json[r'geometry'] = this.geometry;
      json[r'properties'] = this.properties;
    return json;
  }

  /// Returns a new [SearchBoxFeature] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxFeature? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxFeature[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxFeature[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxFeature(
        type: mapValueOfType<String>(json, r'type')!,
        geometry: SearchBoxGeometry.fromJson(json[r'geometry'])!,
        properties: SearchBoxProperties.fromJson(json[r'properties'])!,
      );
    }
    return null;
  }

  static List<SearchBoxFeature> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxFeature>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxFeature.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxFeature> mapFromJson(dynamic json) {
    final map = <String, SearchBoxFeature>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxFeature.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxFeature-objects as value to a dart map
  static Map<String, List<SearchBoxFeature>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxFeature>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxFeature.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'type',
    'geometry',
    'properties',
  };
}

