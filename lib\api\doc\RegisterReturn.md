# openapi.model.RegisterReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**userId** | **String** | Unique identifier of the newly registered user | 
**token** | **String** | JWT authentication token for the new user | 
**role** | [**UserRoleEnum**](UserRoleEnum.md) |  | 
**username** | **String** | Username of the new user | 
**fullname** | **String** | Full name of the new user | 
**avatarUrl** | **String** | URL to the user's avatar image | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



