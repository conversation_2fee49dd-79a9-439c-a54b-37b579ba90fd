import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

/// Informational text widget for authentication screens
/// 
/// A reusable widget for displaying informational messages
/// throughout the authentication flow. Provides consistent
/// styling and layout for instructional text.
class AuthInfoTextWidget extends StatelessWidget {
  /// The text content to display
  final String text;
  
  /// Optional text style override
  /// If not provided, uses the default auth info text style
  final TextStyle? textStyle;
  
  /// Text alignment (defaults to center)
  final TextAlign textAlign;
  
  /// Maximum number of lines (null for unlimited)
  final int? maxLines;
  
  /// Optional padding around the text
  final EdgeInsetsGeometry? padding;

  /// Creates an auth info text widget
  /// 
  /// [text] is the content to display
  /// [textStyle] optionally overrides the default styling
  /// [textAlign] controls text alignment (defaults to center)
  /// [maxLines] limits the number of lines (null for unlimited)
  /// [padding] adds optional spacing around the text
  const AuthInfoTextWidget({
    super.key,
    required this.text,
    this.textStyle,
    this.textAlign = TextAlign.center,
    this.maxLines,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget textWidget = Text(
      text,
      style: textStyle ?? AppTextStyles.size24BoldPrimary,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : null,
    );

    // Apply padding if provided
    if (padding != null) {
      textWidget = Padding(
        padding: padding!,
        child: textWidget,
      );
    }

    return textWidget;
  }
}
