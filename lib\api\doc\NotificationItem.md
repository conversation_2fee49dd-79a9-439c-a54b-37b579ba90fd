# openapi.model.NotificationItem

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**notificationType** | [**NotificationEnum**](NotificationEnum.md) |  | 
**notificationId** | **String** | Unique identifier of the notification | 
**createdAt** | **String** | Timestamp when the notification was created, in ISO 8601 date-time format | 
**accountType** | [**AccountTypeEnum**](AccountTypeEnum.md) |  | 
**accountId** | **String** | UUID of the account that triggered the notification | 
**accountUsername** | **String** | Username of the account that triggered the notification | 
**accountAvatarUrl** | **String** | Avatar URL of the account that triggered the notification | [optional] 
**contentType** | **String** | Type of content related to the notification | 
**contentId** | **String** | UUID of the content related to the notification | [optional] 
**contentThumbnailUrl** | **String** | Thumbnail URL of the content | [optional] 
**contentName** | **String** | Name or title of the content | 
**contentItem** | **String** | Description of the content item | 
**actionType** | **String** | Type of action that triggered the notification | 
**actionId** | **String** | UUID of the item to navigate to when notification is tapped | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



