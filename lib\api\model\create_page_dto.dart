//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreatePageDto {
  /// Returns a new [CreatePageDto] instance.
  CreatePageDto({
    required this.pageName,
    this.thumbnailUrl,
    this.websiteUrl,
    this.description,
    required this.isEdu,
    required this.haveMembership,
    this.tagIds = const [],
    this.creatorIds = const [],
    required this.locationId,
  });

  /// Name of the page
  String pageName;

  /// URL to the page thumbnail image
  String? thumbnailUrl;

  /// Website URL for the page
  String? websiteUrl;

  /// Description of the page
  String? description;

  /// Whether this page is educational
  bool isEdu;

  /// Whether this page has membership functionality
  bool haveMembership;

  /// Array of hobby tag UUIDs associated with the page
  List<String> tagIds;

  /// Array of creator user UUIDs for the page
  List<String> creatorIds;

  /// UUID of the location where the page is based
  String locationId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreatePageDto &&
    other.pageName == pageName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.websiteUrl == websiteUrl &&
    other.description == description &&
    other.isEdu == isEdu &&
    other.haveMembership == haveMembership &&
    _deepEquality.equals(other.tagIds, tagIds) &&
    _deepEquality.equals(other.creatorIds, creatorIds) &&
    other.locationId == locationId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (pageName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (websiteUrl == null ? 0 : websiteUrl!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (isEdu.hashCode) +
    (haveMembership.hashCode) +
    (tagIds.hashCode) +
    (creatorIds.hashCode) +
    (locationId.hashCode);

  @override
  String toString() => 'CreatePageDto[pageName=$pageName, thumbnailUrl=$thumbnailUrl, websiteUrl=$websiteUrl, description=$description, isEdu=$isEdu, haveMembership=$haveMembership, tagIds=$tagIds, creatorIds=$creatorIds, locationId=$locationId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'pageName'] = this.pageName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
    if (this.websiteUrl != null) {
      json[r'websiteUrl'] = this.websiteUrl;
    } else {
      json[r'websiteUrl'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'isEdu'] = this.isEdu;
      json[r'haveMembership'] = this.haveMembership;
      json[r'tagIds'] = this.tagIds;
      json[r'creatorIds'] = this.creatorIds;
      json[r'locationId'] = this.locationId;
    return json;
  }

  /// Returns a new [CreatePageDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreatePageDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreatePageDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreatePageDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreatePageDto(
        pageName: mapValueOfType<String>(json, r'pageName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        websiteUrl: mapValueOfType<String>(json, r'websiteUrl'),
        description: mapValueOfType<String>(json, r'description'),
        isEdu: mapValueOfType<bool>(json, r'isEdu')!,
        haveMembership: mapValueOfType<bool>(json, r'haveMembership')!,
        tagIds: json[r'tagIds'] is Iterable
            ? (json[r'tagIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        creatorIds: json[r'creatorIds'] is Iterable
            ? (json[r'creatorIds'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        locationId: mapValueOfType<String>(json, r'locationId')!,
      );
    }
    return null;
  }

  static List<CreatePageDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreatePageDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreatePageDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreatePageDto> mapFromJson(dynamic json) {
    final map = <String, CreatePageDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreatePageDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreatePageDto-objects as value to a dart map
  static Map<String, List<CreatePageDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreatePageDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreatePageDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'pageName',
    'isEdu',
    'haveMembership',
    'tagIds',
    'creatorIds',
    'locationId',
  };
}

