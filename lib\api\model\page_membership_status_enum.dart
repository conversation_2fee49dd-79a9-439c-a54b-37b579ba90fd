//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

/// Status of the user in the page
class PageMembershipStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const PageMembershipStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const admin = PageMembershipStatusEnum._(r'admin');
  static const moderator = PageMembershipStatusEnum._(r'moderator');
  static const creator = PageMembershipStatusEnum._(r'creator');
  static const pending = PageMembershipStatusEnum._(r'pending');
  static const accepted = PageMembershipStatusEnum._(r'accepted');
  static const blocked = PageMembershipStatusEnum._(r'blocked');

  /// List of all possible values in this [enum][PageMembershipStatusEnum].
  static const values = <PageMembershipStatusEnum>[
    admin,
    moderator,
    creator,
    pending,
    accepted,
    blocked,
  ];

  static PageMembershipStatusEnum? fromJson(dynamic value) => PageMembershipStatusEnumTypeTransformer().decode(value);

  static List<PageMembershipStatusEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <PageMembershipStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = PageMembershipStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [PageMembershipStatusEnum] to String,
/// and [decode] dynamic data back to [PageMembershipStatusEnum].
class PageMembershipStatusEnumTypeTransformer {
  factory PageMembershipStatusEnumTypeTransformer() => _instance ??= const PageMembershipStatusEnumTypeTransformer._();

  const PageMembershipStatusEnumTypeTransformer._();

  String encode(PageMembershipStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a PageMembershipStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  PageMembershipStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'admin': return PageMembershipStatusEnum.admin;
        case r'moderator': return PageMembershipStatusEnum.moderator;
        case r'creator': return PageMembershipStatusEnum.creator;
        case r'pending': return PageMembershipStatusEnum.pending;
        case r'accepted': return PageMembershipStatusEnum.accepted;
        case r'blocked': return PageMembershipStatusEnum.blocked;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [PageMembershipStatusEnumTypeTransformer] instance.
  static PageMembershipStatusEnumTypeTransformer? _instance;
}

