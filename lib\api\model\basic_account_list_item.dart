//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class BasicAccountListItem {
  /// Returns a new [BasicAccountListItem] instance.
  BasicAccountListItem({
    required this.accountId,
    required this.accountName,
    required this.accountType,
    this.accountImageUrl,
  });

  /// UUID of the account (user or page)
  String accountId;

  /// Name of the account (user or page)
  String accountName;

  AccountTypeEnum accountType;

  /// URL to the account image
  String? accountImageUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is BasicAccountListItem &&
    other.accountId == accountId &&
    other.accountName == accountName &&
    other.accountType == accountType &&
    other.accountImageUrl == accountImageUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (accountId.hashCode) +
    (accountName.hashCode) +
    (accountType.hashCode) +
    (accountImageUrl == null ? 0 : accountImageUrl!.hashCode);

  @override
  String toString() => 'BasicAccountListItem[accountId=$accountId, accountName=$accountName, accountType=$accountType, accountImageUrl=$accountImageUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'accountId'] = this.accountId;
      json[r'accountName'] = this.accountName;
      json[r'accountType'] = this.accountType;
    if (this.accountImageUrl != null) {
      json[r'accountImageUrl'] = this.accountImageUrl;
    } else {
      json[r'accountImageUrl'] = null;
    }
    return json;
  }

  /// Returns a new [BasicAccountListItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static BasicAccountListItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "BasicAccountListItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "BasicAccountListItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return BasicAccountListItem(
        accountId: mapValueOfType<String>(json, r'accountId')!,
        accountName: mapValueOfType<String>(json, r'accountName')!,
        accountType: AccountTypeEnum.fromJson(json[r'accountType'])!,
        accountImageUrl: mapValueOfType<String>(json, r'accountImageUrl'),
      );
    }
    return null;
  }

  static List<BasicAccountListItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <BasicAccountListItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = BasicAccountListItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, BasicAccountListItem> mapFromJson(dynamic json) {
    final map = <String, BasicAccountListItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = BasicAccountListItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of BasicAccountListItem-objects as value to a dart map
  static Map<String, List<BasicAccountListItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<BasicAccountListItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = BasicAccountListItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'accountId',
    'accountName',
    'accountType',
  };
}

