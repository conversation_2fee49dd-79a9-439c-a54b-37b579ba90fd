//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class IventListItemWithIsFavorited {
  /// Returns a new [IventListItemWithIsFavorited] instance.
  IventListItemWithIsFavorited({
    required this.iventId,
    required this.iventName,
    this.thumbnailUrl,
    required this.locationName,
    this.dates = const [],
    required this.creatorId,
    required this.creatorType,
    required this.creatorUsername,
    this.creatorImageUrl,
    required this.memberCount,
    this.memberFirstnames = const [],
    this.memberAvatarUrls = const [],
    required this.viewType,
    required this.isFavorited,
  });

  /// UUID of the ivent
  String iventId;

  /// Name of the ivent
  String iventName;

  /// URL to the ivent thumbnail image
  String? thumbnailUrl;

  /// Name of the ivent location
  String locationName;

  /// List of dates for the ivent in ISO 8601 date-time format
  List<String> dates;

  /// UUID of the ivent creator
  String creatorId;

  IventCreatorTypeEnum creatorType;

  /// Username of the ivent creator
  String creatorUsername;

  /// URL to the ivent creator image
  String? creatorImageUrl;

  /// Number of members in the ivent
  ///
  /// Minimum value: 0
  int memberCount;

  /// List of member's first names in the ivent
  List<String> memberFirstnames;

  /// List of member avatar URLs in the ivent
  List<String> memberAvatarUrls;

  IventViewTypeEnum viewType;

  /// Whether the ivent is favorited by the current user
  bool isFavorited;

  @override
  bool operator ==(Object other) => identical(this, other) || other is IventListItemWithIsFavorited &&
    other.iventId == iventId &&
    other.iventName == iventName &&
    other.thumbnailUrl == thumbnailUrl &&
    other.locationName == locationName &&
    _deepEquality.equals(other.dates, dates) &&
    other.creatorId == creatorId &&
    other.creatorType == creatorType &&
    other.creatorUsername == creatorUsername &&
    other.creatorImageUrl == creatorImageUrl &&
    other.memberCount == memberCount &&
    _deepEquality.equals(other.memberFirstnames, memberFirstnames) &&
    _deepEquality.equals(other.memberAvatarUrls, memberAvatarUrls) &&
    other.viewType == viewType &&
    other.isFavorited == isFavorited;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (iventId.hashCode) +
    (iventName.hashCode) +
    (thumbnailUrl == null ? 0 : thumbnailUrl!.hashCode) +
    (locationName.hashCode) +
    (dates.hashCode) +
    (creatorId.hashCode) +
    (creatorType.hashCode) +
    (creatorUsername.hashCode) +
    (creatorImageUrl == null ? 0 : creatorImageUrl!.hashCode) +
    (memberCount.hashCode) +
    (memberFirstnames.hashCode) +
    (memberAvatarUrls.hashCode) +
    (viewType.hashCode) +
    (isFavorited.hashCode);

  @override
  String toString() => 'IventListItemWithIsFavorited[iventId=$iventId, iventName=$iventName, thumbnailUrl=$thumbnailUrl, locationName=$locationName, dates=$dates, creatorId=$creatorId, creatorType=$creatorType, creatorUsername=$creatorUsername, creatorImageUrl=$creatorImageUrl, memberCount=$memberCount, memberFirstnames=$memberFirstnames, memberAvatarUrls=$memberAvatarUrls, viewType=$viewType, isFavorited=$isFavorited]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'iventId'] = this.iventId;
      json[r'iventName'] = this.iventName;
    if (this.thumbnailUrl != null) {
      json[r'thumbnailUrl'] = this.thumbnailUrl;
    } else {
      json[r'thumbnailUrl'] = null;
    }
      json[r'locationName'] = this.locationName;
      json[r'dates'] = this.dates;
      json[r'creatorId'] = this.creatorId;
      json[r'creatorType'] = this.creatorType;
      json[r'creatorUsername'] = this.creatorUsername;
    if (this.creatorImageUrl != null) {
      json[r'creatorImageUrl'] = this.creatorImageUrl;
    } else {
      json[r'creatorImageUrl'] = null;
    }
      json[r'memberCount'] = this.memberCount;
      json[r'memberFirstnames'] = this.memberFirstnames;
      json[r'memberAvatarUrls'] = this.memberAvatarUrls;
      json[r'viewType'] = this.viewType;
      json[r'isFavorited'] = this.isFavorited;
    return json;
  }

  /// Returns a new [IventListItemWithIsFavorited] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static IventListItemWithIsFavorited? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "IventListItemWithIsFavorited[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "IventListItemWithIsFavorited[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return IventListItemWithIsFavorited(
        iventId: mapValueOfType<String>(json, r'iventId')!,
        iventName: mapValueOfType<String>(json, r'iventName')!,
        thumbnailUrl: mapValueOfType<String>(json, r'thumbnailUrl'),
        locationName: mapValueOfType<String>(json, r'locationName')!,
        dates: json[r'dates'] is Iterable
            ? (json[r'dates'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        creatorId: mapValueOfType<String>(json, r'creatorId')!,
        creatorType: IventCreatorTypeEnum.fromJson(json[r'creatorType'])!,
        creatorUsername: mapValueOfType<String>(json, r'creatorUsername')!,
        creatorImageUrl: mapValueOfType<String>(json, r'creatorImageUrl'),
        memberCount: mapValueOfType<int>(json, r'memberCount')!,
        memberFirstnames: json[r'memberFirstnames'] is Iterable
            ? (json[r'memberFirstnames'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        memberAvatarUrls: json[r'memberAvatarUrls'] is Iterable
            ? (json[r'memberAvatarUrls'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        viewType: IventViewTypeEnum.fromJson(json[r'viewType'])!,
        isFavorited: mapValueOfType<bool>(json, r'isFavorited')!,
      );
    }
    return null;
  }

  static List<IventListItemWithIsFavorited> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <IventListItemWithIsFavorited>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = IventListItemWithIsFavorited.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, IventListItemWithIsFavorited> mapFromJson(dynamic json) {
    final map = <String, IventListItemWithIsFavorited>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = IventListItemWithIsFavorited.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of IventListItemWithIsFavorited-objects as value to a dart map
  static Map<String, List<IventListItemWithIsFavorited>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<IventListItemWithIsFavorited>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = IventListItemWithIsFavorited.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'iventId',
    'iventName',
    'locationName',
    'dates',
    'creatorId',
    'creatorType',
    'creatorUsername',
    'memberCount',
    'memberFirstnames',
    'memberAvatarUrls',
    'viewType',
    'isFavorited',
  };
}

