# openapi.model.GetVibesReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**vibes** | [**List<VibeItem>**](VibeItem.md) | List of vibes | [default to const []]
**vibeCount** | **int** | Total number of vibes | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



