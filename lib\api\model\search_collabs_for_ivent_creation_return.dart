//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchCollabsForIventCreationReturn {
  /// Returns a new [SearchCollabsForIventCreationReturn] instance.
  SearchCollabsForIventCreationReturn({
    this.accounts = const [],
    required this.accountCount,
  });

  /// List of accounts available for collaboration during ivent creation
  List<BasicAccountListItem> accounts;

  /// Total number of accounts available for collaboration
  ///
  /// Minimum value: 0
  int accountCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchCollabsForIventCreationReturn &&
    _deepEquality.equals(other.accounts, accounts) &&
    other.accountCount == accountCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (accounts.hashCode) +
    (accountCount.hashCode);

  @override
  String toString() => 'SearchCollabsForIventCreationReturn[accounts=$accounts, accountCount=$accountCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'accounts'] = this.accounts;
      json[r'accountCount'] = this.accountCount;
    return json;
  }

  /// Returns a new [SearchCollabsForIventCreationReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchCollabsForIventCreationReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchCollabsForIventCreationReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchCollabsForIventCreationReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchCollabsForIventCreationReturn(
        accounts: BasicAccountListItem.listFromJson(json[r'accounts']),
        accountCount: mapValueOfType<int>(json, r'accountCount')!,
      );
    }
    return null;
  }

  static List<SearchCollabsForIventCreationReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchCollabsForIventCreationReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchCollabsForIventCreationReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchCollabsForIventCreationReturn> mapFromJson(dynamic json) {
    final map = <String, SearchCollabsForIventCreationReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchCollabsForIventCreationReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchCollabsForIventCreationReturn-objects as value to a dart map
  static Map<String, List<SearchCollabsForIventCreationReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchCollabsForIventCreationReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchCollabsForIventCreationReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'accounts',
    'accountCount',
  };
}

