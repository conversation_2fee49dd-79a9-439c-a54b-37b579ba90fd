# openapi.api.PagesApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**blockPageByPageId**](PagesApi.md#pagescontrollerblockpagebypageid) | **POST** /pages/{id}/block | Sayfa IDsi ile sayfa engellenir
[**createPage**](PagesApi.md#pagescontrollercreatepage) | **POST** /pages/create | Sayfa oluşturur
[**deletePageByPageId**](PagesApi.md#pagescontrollerdeletepagebypageid) | **DELETE** /pages/{id}/delete | Sayfa IDsi ile sayfayı siler
[**followByPageId**](PagesApi.md#pagescontrollerfollowbypageid) | **POST** /pages/{id}/follow | Sayfa IDsi ile takip edilir
[**getDetailsByPageId**](PagesApi.md#pagescontrollergetdetailsbypageid) | **GET** /pages/{id}/details | Sayfa IDsi ile sayfanın detaylarını listeler
[**getIventsCreatedByPageId**](PagesApi.md#pagescontrollergetiventscreatedbypageid) | **GET** /pages/{id}/ivents | Sayfa IDsi ile sayfanın iventlerini listeler
[**getPageByPageId**](PagesApi.md#pagescontrollergetpagebypageid) | **GET** /pages/{id} | Sayfa IDsi ile sayfanın bilgilerini listeler
[**getVibesByPageId**](PagesApi.md#pagescontrollergetvibesbypageid) | **GET** /pages/{id}/vibes | Sayfa IDsi ile sayfanın vibelarını listeler
[**removeFollowerByPageId**](PagesApi.md#pagescontrollerremovefollowerbypageid) | **POST** /pages/{id}/followers/remove | Sayfa IDsi ile takipçi çıkartılır
[**searchFollowersByPageId**](PagesApi.md#pagescontrollersearchfollowersbypageid) | **GET** /pages/{id}/followers | Sayfa IDsi ile takipçileri listeler
[**subscribeByPageId**](PagesApi.md#pagescontrollersubscribebypageid) | **POST** /pages/{id}/subscribe | Sayfa IDsi ile etkinlik bildirimlerini açar
[**unblockPageByPageId**](PagesApi.md#pagescontrollerunblockpagebypageid) | **POST** /pages/{id}/unblock | Sayfa IDsi ile sayfa engeli kaldırılır
[**unfollowByPageId**](PagesApi.md#pagescontrollerunfollowbypageid) | **POST** /pages/{id}/unfollow | Sayfa IDsi ile takipten çıkılır
[**unsubscribeByPageId**](PagesApi.md#pagescontrollerunsubscribebypageid) | **POST** /pages/{id}/unsubscribe | Sayfa IDsi ile etkinlik bildirimlerini kapatır
[**updateDescriptionByPageId**](PagesApi.md#pagescontrollerupdatedescriptionbypageid) | **PUT** /pages/{id}/update/description | Sayfa IDsi ile sayfanın açıklamasını günceller
[**updateLinksByPageId**](PagesApi.md#pagescontrollerupdatelinksbypageid) | **PUT** /pages/{id}/update/links | Sayfa IDsi ile sayfanın linklerini günceller
[**updateLocationByPageId**](PagesApi.md#pagescontrollerupdatelocationbypageid) | **PUT** /pages/{id}/update/location | Sayfa IDsi ile sayfanın konumunu günceller


# **blockPageByPageId**
> blockPageByPageId(id)

Sayfa IDsi ile sayfa engellenir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.blockPageByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->blockPageByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createPage**
> CreatePageReturn createPage(createPageDto)

Sayfa oluşturur

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final createPageDto = CreatePageDto(); // CreatePageDto | 

try {
    final result = api_instance.createPage(createPageDto);
    print(result);
} catch (e) {
    print('Exception when calling PagesApi->createPage: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **createPageDto** | [**CreatePageDto**](CreatePageDto.md)|  | 

### Return type

[**CreatePageReturn**](CreatePageReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deletePageByPageId**
> deletePageByPageId(id)

Sayfa IDsi ile sayfayı siler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.deletePageByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->deletePageByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **followByPageId**
> followByPageId(id)

Sayfa IDsi ile takip edilir

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.followByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->followByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getDetailsByPageId**
> GetPageDetailsByPageIdReturn getDetailsByPageId(id)

Sayfa IDsi ile sayfanın detaylarını listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    final result = api_instance.getDetailsByPageId(id);
    print(result);
} catch (e) {
    print('Exception when calling PagesApi->getDetailsByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetPageDetailsByPageIdReturn**](GetPageDetailsByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getIventsCreatedByPageId**
> GetIventsCreatedByPageIdReturn getIventsCreatedByPageId(id, limit, page)

Sayfa IDsi ile sayfanın iventlerini listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getIventsCreatedByPageId(id, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PagesApi->getIventsCreatedByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetIventsCreatedByPageIdReturn**](GetIventsCreatedByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getPageByPageId**
> GetPageByPageIdReturn getPageByPageId(id)

Sayfa IDsi ile sayfanın bilgilerini listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    final result = api_instance.getPageByPageId(id);
    print(result);
} catch (e) {
    print('Exception when calling PagesApi->getPageByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**GetPageByPageIdReturn**](GetPageByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getVibesByPageId**
> GetVibeFoldersByPageIdReturn getVibesByPageId(id, limit, page)

Sayfa IDsi ile sayfanın vibelarını listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.getVibesByPageId(id, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PagesApi->getVibesByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**GetVibeFoldersByPageIdReturn**](GetVibeFoldersByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **removeFollowerByPageId**
> removeFollowerByPageId(id, removeFollowerByPageIdDto)

Sayfa IDsi ile takipçi çıkartılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final removeFollowerByPageIdDto = RemoveFollowerByPageIdDto(); // RemoveFollowerByPageIdDto | 

try {
    api_instance.removeFollowerByPageId(id, removeFollowerByPageIdDto);
} catch (e) {
    print('Exception when calling PagesApi->removeFollowerByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **removeFollowerByPageIdDto** | [**RemoveFollowerByPageIdDto**](RemoveFollowerByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **searchFollowersByPageId**
> SearchFollowersByPageIdReturn searchFollowersByPageId(id, q, limit, page)

Sayfa IDsi ile takipçileri listeler

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final q = q_example; // String | 
final limit = 56; // int | 
final page = 56; // int | 

try {
    final result = api_instance.searchFollowersByPageId(id, q, limit, page);
    print(result);
} catch (e) {
    print('Exception when calling PagesApi->searchFollowersByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **q** | **String**|  | [optional] 
 **limit** | **int**|  | [optional] 
 **page** | **int**|  | [optional] 

### Return type

[**SearchFollowersByPageIdReturn**](SearchFollowersByPageIdReturn.md)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **subscribeByPageId**
> subscribeByPageId(id)

Sayfa IDsi ile etkinlik bildirimlerini açar

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.subscribeByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->subscribeByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unblockPageByPageId**
> unblockPageByPageId(id)

Sayfa IDsi ile sayfa engeli kaldırılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.unblockPageByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->unblockPageByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unfollowByPageId**
> unfollowByPageId(id)

Sayfa IDsi ile takipten çıkılır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.unfollowByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->unfollowByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unsubscribeByPageId**
> unsubscribeByPageId(id)

Sayfa IDsi ile etkinlik bildirimlerini kapatır

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 

try {
    api_instance.unsubscribeByPageId(id);
} catch (e) {
    print('Exception when calling PagesApi->unsubscribeByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateDescriptionByPageId**
> updateDescriptionByPageId(id, updateDescriptionByPageIdDto)

Sayfa IDsi ile sayfanın açıklamasını günceller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final updateDescriptionByPageIdDto = UpdateDescriptionByPageIdDto(); // UpdateDescriptionByPageIdDto | 

try {
    api_instance.updateDescriptionByPageId(id, updateDescriptionByPageIdDto);
} catch (e) {
    print('Exception when calling PagesApi->updateDescriptionByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateDescriptionByPageIdDto** | [**UpdateDescriptionByPageIdDto**](UpdateDescriptionByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateLinksByPageId**
> updateLinksByPageId(id, updateLinksByPageIdDto)

Sayfa IDsi ile sayfanın linklerini günceller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final updateLinksByPageIdDto = UpdateLinksByPageIdDto(); // UpdateLinksByPageIdDto | 

try {
    api_instance.updateLinksByPageId(id, updateLinksByPageIdDto);
} catch (e) {
    print('Exception when calling PagesApi->updateLinksByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateLinksByPageIdDto** | [**UpdateLinksByPageIdDto**](UpdateLinksByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateLocationByPageId**
> updateLocationByPageId(id, updateLocationByPageIdDto)

Sayfa IDsi ile sayfanın konumunu günceller

### Example
```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: JWT-auth
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('JWT-auth').setAccessToken(yourTokenGeneratorFunction);

final api_instance = PagesApi();
final id = id_example; // String | 
final updateLocationByPageIdDto = UpdateLocationByPageIdDto(); // UpdateLocationByPageIdDto | 

try {
    api_instance.updateLocationByPageId(id, updateLocationByPageIdDto);
} catch (e) {
    print('Exception when calling PagesApi->updateLocationByPageId: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **updateLocationByPageIdDto** | [**UpdateLocationByPageIdDto**](UpdateLocationByPageIdDto.md)|  | 

### Return type

void (empty response body)

### Authorization

[JWT-auth](../README.md#JWT-auth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)


