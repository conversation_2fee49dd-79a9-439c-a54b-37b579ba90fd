//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetVibeFoldersByPageIdReturn {
  /// Returns a new [GetVibeFoldersByPageIdReturn] instance.
  GetVibeFoldersByPageIdReturn({
    this.vibeFolders = const [],
    required this.vibeFolderCount,
  });

  /// List of vibe folders associated with this page
  List<VibeFolderCardItem> vibeFolders;

  /// Total number of vibe folders
  ///
  /// Minimum value: 0
  int vibeFolderCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetVibeFoldersByPageIdReturn &&
    _deepEquality.equals(other.vibeFolders, vibeFolders) &&
    other.vibeFolderCount == vibeFolderCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (vibeFolders.hashCode) +
    (vibeFolderCount.hashCode);

  @override
  String toString() => 'GetVibeFoldersByPageIdReturn[vibeFolders=$vibeFolders, vibeFolderCount=$vibeFolderCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'vibeFolders'] = this.vibeFolders;
      json[r'vibeFolderCount'] = this.vibeFolderCount;
    return json;
  }

  /// Returns a new [GetVibeFoldersByPageIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetVibeFoldersByPageIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetVibeFoldersByPageIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetVibeFoldersByPageIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetVibeFoldersByPageIdReturn(
        vibeFolders: VibeFolderCardItem.listFromJson(json[r'vibeFolders']),
        vibeFolderCount: mapValueOfType<int>(json, r'vibeFolderCount')!,
      );
    }
    return null;
  }

  static List<GetVibeFoldersByPageIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetVibeFoldersByPageIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetVibeFoldersByPageIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetVibeFoldersByPageIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetVibeFoldersByPageIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetVibeFoldersByPageIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetVibeFoldersByPageIdReturn-objects as value to a dart map
  static Map<String, List<GetVibeFoldersByPageIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetVibeFoldersByPageIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetVibeFoldersByPageIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'vibeFolders',
    'vibeFolderCount',
  };
}

