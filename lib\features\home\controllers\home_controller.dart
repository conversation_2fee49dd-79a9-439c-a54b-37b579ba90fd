import 'package:get/get.dart';
import 'package:ivent_app/app/routes/feed.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_controller.dart';
import 'package:ivent_app/features/home/<USER>/filter_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/searchable/home_search_controller.dart';
import 'package:ivent_app/features/home/<USER>/searchable/location_controller.dart';

/// Main controller that coordinates between the specialized controllers
///
/// Acts as the central coordinator for all home feature functionality,
/// managing child controllers for panels, map, feed, search, filters, and location.
/// Follows the coordinator pattern for complex feature management.
class HomeController extends BaseHomeController {
  // Child controllers
  late final HomePanelsController homePanelsController;
  late final HomeMapController homeMapController;
  late final FeedController feedController;
  late final HomeSearchController homeSearchController;
  late final FilterController filterController;
  late final LocationController locationController;

  // Constructor
  HomeController(AuthService authService, HomeStateManager state) : super(authService, state);

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();

    // Initialize child controllers in dependency order
    homePanelsController = Get.put(HomePanelsController(authService, state));
    homeMapController = Get.put(HomeMapController(authService, state));
    feedController = Get.put(FeedController(authService, state, homePanelsController));
    homeSearchController = Get.put(HomeSearchController(authService, state));
    filterController = Get.put(FilterController(authService, state));
    locationController = Get.put(LocationController(authService, state));

    print('HomeController initialized with user: ${sessionUser.sessionId}');
  }

  @override
  void closeController() {
    // Clean up child controllers
    Get.delete<HomePanelsController>();
    Get.delete<HomeMapController>();
    Get.delete<FeedController>();
    Get.delete<HomeSearchController>();
    Get.delete<FilterController>();
    Get.delete<LocationController>();
    super.closeController();
  }

  // Navigation methods

  /// Navigate to feed page
  void goToFeedPage() => homePanelsController.goToFeedPage();

  /// Navigate to search page
  void goToSearchPage() => homePanelsController.goToSearchPage();

  /// Navigate to filter page
  void goToFilterPage() => homePanelsController.goToFilterPage();

  /// Navigate to location selection page
  void goToLocationPage() => Get.toNamed(FeedRoutes.LOCATION_PAGE);
}
