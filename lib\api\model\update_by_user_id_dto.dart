//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class UpdateByUserIdDto {
  /// Returns a new [UpdateByUserIdDto] instance.
  UpdateByUserIdDto({
    required this.newUsername,
    required this.newBirthday,
    required this.newGender,
    required this.newAvatarUrl,
  });

  /// Username can only contain letters, numbers, underscores, and hyphens
  String newUsername;

  /// Birthday, in ISO 8601 date-time format
  String newBirthday;

  UserGenderEnum newGender;

  /// URL to the user's avatar image
  String newAvatarUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is UpdateByUserIdDto &&
    other.newUsername == newUsername &&
    other.newBirthday == newBirthday &&
    other.newGender == newGender &&
    other.newAvatarUrl == newAvatarUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (newUsername.hashCode) +
    (newBirthday.hashCode) +
    (newGender.hashCode) +
    (newAvatarUrl.hashCode);

  @override
  String toString() => 'UpdateByUserIdDto[newUsername=$newUsername, newBirthday=$newBirthday, newGender=$newGender, newAvatarUrl=$newAvatarUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'newUsername'] = this.newUsername;
      json[r'newBirthday'] = this.newBirthday;
      json[r'newGender'] = this.newGender;
      json[r'newAvatarUrl'] = this.newAvatarUrl;
    return json;
  }

  /// Returns a new [UpdateByUserIdDto] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static UpdateByUserIdDto? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "UpdateByUserIdDto[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "UpdateByUserIdDto[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return UpdateByUserIdDto(
        newUsername: mapValueOfType<String>(json, r'newUsername')!,
        newBirthday: mapValueOfType<String>(json, r'newBirthday')!,
        newGender: UserGenderEnum.fromJson(json[r'newGender'])!,
        newAvatarUrl: mapValueOfType<String>(json, r'newAvatarUrl')!,
      );
    }
    return null;
  }

  static List<UpdateByUserIdDto> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <UpdateByUserIdDto>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = UpdateByUserIdDto.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, UpdateByUserIdDto> mapFromJson(dynamic json) {
    final map = <String, UpdateByUserIdDto>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = UpdateByUserIdDto.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of UpdateByUserIdDto-objects as value to a dart map
  static Map<String, List<UpdateByUserIdDto>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<UpdateByUserIdDto>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = UpdateByUserIdDto.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'newUsername',
    'newBirthday',
    'newGender',
    'newAvatarUrl',
  };
}

