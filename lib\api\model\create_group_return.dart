//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreateGroupReturn {
  /// Returns a new [CreateGroupReturn] instance.
  CreateGroupReturn({
    required this.groupId,
  });

  /// UUID of the newly created group
  String groupId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreateGroupReturn &&
    other.groupId == groupId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (groupId.hashCode);

  @override
  String toString() => 'CreateGroupReturn[groupId=$groupId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'groupId'] = this.groupId;
    return json;
  }

  /// Returns a new [CreateGroupReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreateGroupReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreateGroupReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreateGroupReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreateGroupReturn(
        groupId: mapValueOfType<String>(json, r'groupId')!,
      );
    }
    return null;
  }

  static List<CreateGroupReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreateGroupReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreateGroupReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreateGroupReturn> mapFromJson(dynamic json) {
    final map = <String, CreateGroupReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreateGroupReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreateGroupReturn-objects as value to a dart map
  static Map<String, List<CreateGroupReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreateGroupReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreateGroupReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'groupId',
  };
}

