//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class LocationItem {
  /// Returns a new [LocationItem] instance.
  LocationItem({
    required this.locationId,
    required this.mapboxId,
    required this.locationName,
    required this.openAddress,
    required this.latitude,
    required this.longitude,
    required this.state,
  });

  /// UUID of the location
  String locationId;

  /// Mapbox place identifier
  String mapboxId;

  /// Name of the location
  String locationName;

  /// Detailed address of the location
  String openAddress;

  /// Latitude coordinate of the location
  double latitude;

  /// Longitude coordinate of the location
  double longitude;

  /// State where the location is situated
  String state;

  @override
  bool operator ==(Object other) => identical(this, other) || other is LocationItem &&
    other.locationId == locationId &&
    other.mapboxId == mapboxId &&
    other.locationName == locationName &&
    other.openAddress == openAddress &&
    other.latitude == latitude &&
    other.longitude == longitude &&
    other.state == state;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (locationId.hashCode) +
    (mapboxId.hashCode) +
    (locationName.hashCode) +
    (openAddress.hashCode) +
    (latitude.hashCode) +
    (longitude.hashCode) +
    (state.hashCode);

  @override
  String toString() => 'LocationItem[locationId=$locationId, mapboxId=$mapboxId, locationName=$locationName, openAddress=$openAddress, latitude=$latitude, longitude=$longitude, state=$state]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'locationId'] = this.locationId;
      json[r'mapboxId'] = this.mapboxId;
      json[r'locationName'] = this.locationName;
      json[r'openAddress'] = this.openAddress;
      json[r'latitude'] = this.latitude;
      json[r'longitude'] = this.longitude;
      json[r'state'] = this.state;
    return json;
  }

  /// Returns a new [LocationItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static LocationItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "LocationItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "LocationItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return LocationItem(
        locationId: mapValueOfType<String>(json, r'locationId')!,
        mapboxId: mapValueOfType<String>(json, r'mapboxId')!,
        locationName: mapValueOfType<String>(json, r'locationName')!,
        openAddress: mapValueOfType<String>(json, r'openAddress')!,
        latitude: mapValueOfType<double>(json, r'latitude')!,
        longitude: mapValueOfType<double>(json, r'longitude')!,
        state: mapValueOfType<String>(json, r'state')!,
      );
    }
    return null;
  }

  static List<LocationItem> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <LocationItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = LocationItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, LocationItem> mapFromJson(dynamic json) {
    final map = <String, LocationItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = LocationItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of LocationItem-objects as value to a dart map
  static Map<String, List<LocationItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<LocationItem>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = LocationItem.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'locationId',
    'mapboxId',
    'locationName',
    'openAddress',
    'latitude',
    'longitude',
    'state',
  };
}

