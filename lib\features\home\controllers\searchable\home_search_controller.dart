import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class HomeSearchController extends BaseHomeController {
  HomeSearchController(AuthService authService, HomeStateManager state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarControllerIvent;
  late final BaseSearchBarController baseSearchBarControllerAccount;

  final _searchTabIndex = 0.obs;
  final _searchedIventResults = <IventCardItem>[].obs;
  final _searchedAccountResults = <BasicAccountListItem>[].obs;

  int get searchTabIndex => _searchTabIndex.value;
  List<IventCardItem> get searchedIventResults => _searchedIventResults;
  List<BasicAccountListItem> get searchedAccountResults => _searchedAccountResults;

  TextEditingController get textEditingControllerIvent => baseSearchBarControllerIvent.textEditingController;
  String get searchTextIvent => baseSearchBarControllerIvent.text;
  bool get isSearchingIvent => baseSearchBarControllerIvent.isSearching;
  bool get isQueryEmptyIvent => searchTextIvent.isEmpty;
  bool get isResultsEmptyIvent => _searchedIventResults.isEmpty;

  TextEditingController get textEditingControllerAccount => baseSearchBarControllerAccount.textEditingController;
  String get searchTextAccount => baseSearchBarControllerAccount.text;
  bool get isSearchingAccount => baseSearchBarControllerAccount.isSearching;
  bool get isQueryEmptyAccount => searchTextAccount.isEmpty;
  bool get isResultsEmptyAccount => _searchedAccountResults.isEmpty;

  set searchTabIndex(int value) => _searchTabIndex.value = value;

  @override
  void initController() async {
    super.initController();
    baseSearchBarControllerIvent = Get.put(
      BaseSearchBarController(_searchIvents),
      tag: 'HomeSearchControllerIvents',
    );
    baseSearchBarControllerAccount = Get.put(
      BaseSearchBarController(_searchAccounts),
      tag: 'HomeSearchControllerAccounts',
    );
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'HomeSearchController');
    super.closeController();
  }

  Future<void> _searchIvents(String? q) async {
    if (q == null) return;
    final result = await authService.homeApi.searchIvent(q);
    if (result != null) {
      _searchedIventResults.value = result.ivents;
    }
  }

  Future<void> _searchAccounts(String? q) async {
    if (q == null) return;
    final result = await authService.homeApi.searchAccount(q);
    if (result != null) {
      _searchedAccountResults.value = result.accounts;
    }
  }
}
