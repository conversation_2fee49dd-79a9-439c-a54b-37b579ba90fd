//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class IventCollabsApi {
  IventCollabsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Ivent IDsi ile paydaş hesaplardan ayrılınır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  Future<Response> leaveCollabrationByIventIdWithHttpInfo(String iventId,) async {
    // ignore: prefer_const_declarations
    final path = r'/iventCollabs/{iventId}/leave'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile paydaş hesaplardan ayrılınır
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  Future<void> leaveCollabrationByIventId(String iventId,) async {
    final response = await leaveCollabrationByIventIdWithHttpInfo(iventId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent IDsi ile bir hesabı paydaş hesaplardan çıkartır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [RemoveCollabByIventIdDto] removeCollabByIventIdDto (required):
  Future<Response> removeCollabByIventIdWithHttpInfo(String iventId, RemoveCollabByIventIdDto removeCollabByIventIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/iventCollabs/{iventId}/remove'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody = removeCollabByIventIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent IDsi ile bir hesabı paydaş hesaplardan çıkartır
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [RemoveCollabByIventIdDto] removeCollabByIventIdDto (required):
  Future<void> removeCollabByIventId(String iventId, RemoveCollabByIventIdDto removeCollabByIventIdDto,) async {
    final response = await removeCollabByIventIdWithHttpInfo(iventId, removeCollabByIventIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Ivent'te bulunan paydaş hesapları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchCollabsWithHttpInfo(String iventId, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/iventCollabs/{iventId}/search'
      .replaceAll('{iventId}', iventId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent'te bulunan paydaş hesapları listeler
  ///
  /// Parameters:
  ///
  /// * [String] iventId (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchCollabsReturn?> searchCollabs(String iventId, { String? q, int? limit, int? page, }) async {
    final response = await searchCollabsWithHttpInfo(iventId,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchCollabsReturn',) as SearchCollabsReturn;
    
    }
    return null;
  }

  /// Ivent oluştururken paydaş olabilecek hesapları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> searchCollabsForIventCreationWithHttpInfo({ String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/iventCollabs/search';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Ivent oluştururken paydaş olabilecek hesapları listeler
  ///
  /// Parameters:
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<SearchCollabsForIventCreationReturn?> searchCollabsForIventCreation({ String? q, int? limit, int? page, }) async {
    final response = await searchCollabsForIventCreationWithHttpInfo( q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'SearchCollabsForIventCreationReturn',) as SearchCollabsForIventCreationReturn;
    
    }
    return null;
  }
}

