//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchFriendsByUserIdReturn {
  /// Returns a new [SearchFriendsByUserIdReturn] instance.
  SearchFriendsByUserIdReturn({
    this.groups = const [],
    required this.groupCount,
    this.friends = const [],
    required this.friendCount,
  });

  /// List of groups
  List<GroupListItem> groups;

  /// Total number of groups
  ///
  /// Minimum value: 0
  int groupCount;

  /// List of friends
  List<UserListItem> friends;

  /// Total number of friends
  ///
  /// Minimum value: 0
  int friendCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchFriendsByUserIdReturn &&
    _deepEquality.equals(other.groups, groups) &&
    other.groupCount == groupCount &&
    _deepEquality.equals(other.friends, friends) &&
    other.friendCount == friendCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (groups.hashCode) +
    (groupCount.hashCode) +
    (friends.hashCode) +
    (friendCount.hashCode);

  @override
  String toString() => 'SearchFriendsByUserIdReturn[groups=$groups, groupCount=$groupCount, friends=$friends, friendCount=$friendCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'groups'] = this.groups;
      json[r'groupCount'] = this.groupCount;
      json[r'friends'] = this.friends;
      json[r'friendCount'] = this.friendCount;
    return json;
  }

  /// Returns a new [SearchFriendsByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchFriendsByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchFriendsByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchFriendsByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchFriendsByUserIdReturn(
        groups: GroupListItem.listFromJson(json[r'groups']),
        groupCount: mapValueOfType<int>(json, r'groupCount')!,
        friends: UserListItem.listFromJson(json[r'friends']),
        friendCount: mapValueOfType<int>(json, r'friendCount')!,
      );
    }
    return null;
  }

  static List<SearchFriendsByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchFriendsByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchFriendsByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchFriendsByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, SearchFriendsByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchFriendsByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchFriendsByUserIdReturn-objects as value to a dart map
  static Map<String, List<SearchFriendsByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchFriendsByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchFriendsByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'groups',
    'groupCount',
    'friends',
    'friendCount',
  };
}

