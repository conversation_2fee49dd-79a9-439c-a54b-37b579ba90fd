//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class AuthApi {
  AuthApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Hesaptan çıkış yapar
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> logoutWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/logout'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesaptan çıkış yapar
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> logout(String id,) async {
    final response = await logoutWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Telefon numarasına onay kodu gönderir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [SendVerificationCodeDto] sendVerificationCodeDto (required):
  Future<Response> sendVerificationCodeWithHttpInfo(SendVerificationCodeDto sendVerificationCodeDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/send-verification-code';

    // ignore: prefer_final_locals
    Object? postBody = sendVerificationCodeDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Telefon numarasına onay kodu gönderir
  ///
  /// Parameters:
  ///
  /// * [SendVerificationCodeDto] sendVerificationCodeDto (required):
  Future<void> sendVerificationCode(SendVerificationCodeDto sendVerificationCodeDto,) async {
    final response = await sendVerificationCodeWithHttpInfo(sendVerificationCodeDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma sayfasına, değilse giriş sayfasına yönlendirir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [ValidateDto] validateDto (required):
  Future<Response> validateWithHttpInfo(ValidateDto validateDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/validate';

    // ignore: prefer_final_locals
    Object? postBody = validateDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma sayfasına, değilse giriş sayfasına yönlendirir
  ///
  /// Parameters:
  ///
  /// * [ValidateDto] validateDto (required):
  Future<ValidateReturn?> validate(ValidateDto validateDto,) async {
    final response = await validateWithHttpInfo(validateDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ValidateReturn',) as ValidateReturn;
    
    }
    return null;
  }
}

