# openapi.model.GetUserByUserIdReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**userId** | **String** | Unique identifier of the user | 
**userRole** | [**UserRoleEnum**](UserRoleEnum.md) |  | 
**username** | **String** | Username of the user | 
**fullname** | **String** | Full name of the user | 
**avatarUrl** | **String** | URL to the user's avatar image | [optional] 
**iventCount** | **int** | Number of ivents created by the user | 
**friendCount** | **int** | Number of friends the user has | 
**followerCount** | **int** | Number of followers the user has | 
**hobbies** | **List<String>** | List of user's hobbies | [default to const []]
**isFollowing** | **bool** | Whether the current user is following this user | 
**isFirst<PERSON>erson** | **bool** | Whether this is the current user's own profile | 
**relationshipStatus** | [**UserRelationshipStatusEnum**](UserRelationshipStatusEnum.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



