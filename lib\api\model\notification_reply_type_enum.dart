//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class NotificationReplyTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const NotificationReplyTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const accept = NotificationReplyTypeEnum._(r'accept');
  static const reject = NotificationReplyTypeEnum._(r'reject');

  /// List of all possible values in this [enum][NotificationReplyTypeEnum].
  static const values = <NotificationReplyTypeEnum>[
    accept,
    reject,
  ];

  static NotificationReplyTypeEnum? fromJson(dynamic value) => NotificationReplyTypeEnumTypeTransformer().decode(value);

  static List<NotificationReplyTypeEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <NotificationReplyTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = NotificationReplyTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [NotificationReplyTypeEnum] to String,
/// and [decode] dynamic data back to [NotificationReplyTypeEnum].
class NotificationReplyTypeEnumTypeTransformer {
  factory NotificationReplyTypeEnumTypeTransformer() => _instance ??= const NotificationReplyTypeEnumTypeTransformer._();

  const NotificationReplyTypeEnumTypeTransformer._();

  String encode(NotificationReplyTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a NotificationReplyTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  NotificationReplyTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'accept': return NotificationReplyTypeEnum.accept;
        case r'reject': return NotificationReplyTypeEnum.reject;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [NotificationReplyTypeEnumTypeTransformer] instance.
  static NotificationReplyTypeEnumTypeTransformer? _instance;
}

