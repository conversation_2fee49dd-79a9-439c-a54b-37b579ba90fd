//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ValidateReturn {
  /// Returns a new [ValidateReturn] instance.
  ValidateReturn({
    this.token,
    this.userId,
    this.role,
    this.username,
    this.fullname,
    this.avatarUrl,
    required this.type,
  });

  /// JWT authentication token for authenticated users
  String? token;

  /// Unique identifier of the authenticated user
  String? userId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  UserRoleEnum? role;

  /// Username of the authenticated user
  String? username;

  /// Full name of the authenticated user
  String? fullname;

  /// URL to the user's avatar image
  String? avatarUrl;

  AuthEnum type;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ValidateReturn &&
    other.token == token &&
    other.userId == userId &&
    other.role == role &&
    other.username == username &&
    other.fullname == fullname &&
    other.avatarUrl == avatarUrl &&
    other.type == type;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (token == null ? 0 : token!.hashCode) +
    (userId == null ? 0 : userId!.hashCode) +
    (role == null ? 0 : role!.hashCode) +
    (username == null ? 0 : username!.hashCode) +
    (fullname == null ? 0 : fullname!.hashCode) +
    (avatarUrl == null ? 0 : avatarUrl!.hashCode) +
    (type.hashCode);

  @override
  String toString() => 'ValidateReturn[token=$token, userId=$userId, role=$role, username=$username, fullname=$fullname, avatarUrl=$avatarUrl, type=$type]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.token != null) {
      json[r'token'] = this.token;
    } else {
      json[r'token'] = null;
    }
    if (this.userId != null) {
      json[r'userId'] = this.userId;
    } else {
      json[r'userId'] = null;
    }
    if (this.role != null) {
      json[r'role'] = this.role;
    } else {
      json[r'role'] = null;
    }
    if (this.username != null) {
      json[r'username'] = this.username;
    } else {
      json[r'username'] = null;
    }
    if (this.fullname != null) {
      json[r'fullname'] = this.fullname;
    } else {
      json[r'fullname'] = null;
    }
    if (this.avatarUrl != null) {
      json[r'avatarUrl'] = this.avatarUrl;
    } else {
      json[r'avatarUrl'] = null;
    }
      json[r'type'] = this.type;
    return json;
  }

  /// Returns a new [ValidateReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ValidateReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ValidateReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ValidateReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ValidateReturn(
        token: mapValueOfType<String>(json, r'token'),
        userId: mapValueOfType<String>(json, r'userId'),
        role: UserRoleEnum.fromJson(json[r'role']),
        username: mapValueOfType<String>(json, r'username'),
        fullname: mapValueOfType<String>(json, r'fullname'),
        avatarUrl: mapValueOfType<String>(json, r'avatarUrl'),
        type: AuthEnum.fromJson(json[r'type'])!,
      );
    }
    return null;
  }

  static List<ValidateReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ValidateReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ValidateReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ValidateReturn> mapFromJson(dynamic json) {
    final map = <String, ValidateReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ValidateReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ValidateReturn-objects as value to a dart map
  static Map<String, List<ValidateReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ValidateReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = ValidateReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'type',
  };
}

