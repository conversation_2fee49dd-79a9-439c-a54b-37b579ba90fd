//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class CreatePageReturn {
  /// Returns a new [CreatePageReturn] instance.
  CreatePageReturn({
    required this.pageId,
  });

  /// UUID of the newly created page
  String pageId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is CreatePageReturn &&
    other.pageId == pageId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (pageId.hashCode);

  @override
  String toString() => 'CreatePageReturn[pageId=$pageId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'pageId'] = this.pageId;
    return json;
  }

  /// Returns a new [CreatePageReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static CreatePageReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "CreatePageReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "CreatePageReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return CreatePageReturn(
        pageId: mapValueOfType<String>(json, r'pageId')!,
      );
    }
    return null;
  }

  static List<CreatePageReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <CreatePageReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = CreatePageReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, CreatePageReturn> mapFromJson(dynamic json) {
    final map = <String, CreatePageReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = CreatePageReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of CreatePageReturn-objects as value to a dart map
  static Map<String, List<CreatePageReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<CreatePageReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = CreatePageReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'pageId',
  };
}

