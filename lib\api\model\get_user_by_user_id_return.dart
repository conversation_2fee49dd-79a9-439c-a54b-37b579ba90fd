//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class GetUserByUserIdReturn {
  /// Returns a new [GetUserByUserIdReturn] instance.
  GetUserByUserIdReturn({
    required this.userId,
    required this.userRole,
    required this.username,
    required this.fullname,
    this.avatarUrl,
    required this.iventCount,
    required this.friendCount,
    required this.followerCount,
    this.hobbies = const [],
    required this.isFollowing,
    required this.isFirstPerson,
    this.relationshipStatus,
  });

  /// Unique identifier of the user
  String userId;

  UserRoleEnum userRole;

  /// Username of the user
  String username;

  /// Full name of the user
  String fullname;

  /// URL to the user's avatar image
  String? avatarUrl;

  /// Number of ivents created by the user
  ///
  /// Minimum value: 0
  int iventCount;

  /// Number of friends the user has
  ///
  /// Minimum value: 0
  int friendCount;

  /// Number of followers the user has
  ///
  /// Minimum value: 0
  int followerCount;

  /// List of user's hobbies
  List<String> hobbies;

  /// Whether the current user is following this user
  bool isFollowing;

  /// Whether this is the current user's own profile
  bool isFirstPerson;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  UserRelationshipStatusEnum? relationshipStatus;

  @override
  bool operator ==(Object other) => identical(this, other) || other is GetUserByUserIdReturn &&
    other.userId == userId &&
    other.userRole == userRole &&
    other.username == username &&
    other.fullname == fullname &&
    other.avatarUrl == avatarUrl &&
    other.iventCount == iventCount &&
    other.friendCount == friendCount &&
    other.followerCount == followerCount &&
    _deepEquality.equals(other.hobbies, hobbies) &&
    other.isFollowing == isFollowing &&
    other.isFirstPerson == isFirstPerson &&
    other.relationshipStatus == relationshipStatus;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (userId.hashCode) +
    (userRole.hashCode) +
    (username.hashCode) +
    (fullname.hashCode) +
    (avatarUrl == null ? 0 : avatarUrl!.hashCode) +
    (iventCount.hashCode) +
    (friendCount.hashCode) +
    (followerCount.hashCode) +
    (hobbies.hashCode) +
    (isFollowing.hashCode) +
    (isFirstPerson.hashCode) +
    (relationshipStatus == null ? 0 : relationshipStatus!.hashCode);

  @override
  String toString() => 'GetUserByUserIdReturn[userId=$userId, userRole=$userRole, username=$username, fullname=$fullname, avatarUrl=$avatarUrl, iventCount=$iventCount, friendCount=$friendCount, followerCount=$followerCount, hobbies=$hobbies, isFollowing=$isFollowing, isFirstPerson=$isFirstPerson, relationshipStatus=$relationshipStatus]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'userId'] = this.userId;
      json[r'userRole'] = this.userRole;
      json[r'username'] = this.username;
      json[r'fullname'] = this.fullname;
    if (this.avatarUrl != null) {
      json[r'avatarUrl'] = this.avatarUrl;
    } else {
      json[r'avatarUrl'] = null;
    }
      json[r'iventCount'] = this.iventCount;
      json[r'friendCount'] = this.friendCount;
      json[r'followerCount'] = this.followerCount;
      json[r'hobbies'] = this.hobbies;
      json[r'isFollowing'] = this.isFollowing;
      json[r'isFirstPerson'] = this.isFirstPerson;
    if (this.relationshipStatus != null) {
      json[r'relationshipStatus'] = this.relationshipStatus;
    } else {
      json[r'relationshipStatus'] = null;
    }
    return json;
  }

  /// Returns a new [GetUserByUserIdReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static GetUserByUserIdReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "GetUserByUserIdReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "GetUserByUserIdReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return GetUserByUserIdReturn(
        userId: mapValueOfType<String>(json, r'userId')!,
        userRole: UserRoleEnum.fromJson(json[r'userRole'])!,
        username: mapValueOfType<String>(json, r'username')!,
        fullname: mapValueOfType<String>(json, r'fullname')!,
        avatarUrl: mapValueOfType<String>(json, r'avatarUrl'),
        iventCount: mapValueOfType<int>(json, r'iventCount')!,
        friendCount: mapValueOfType<int>(json, r'friendCount')!,
        followerCount: mapValueOfType<int>(json, r'followerCount')!,
        hobbies: json[r'hobbies'] is Iterable
            ? (json[r'hobbies'] as Iterable).cast<String>().toList(growable: false)
            : const [],
        isFollowing: mapValueOfType<bool>(json, r'isFollowing')!,
        isFirstPerson: mapValueOfType<bool>(json, r'isFirstPerson')!,
        relationshipStatus: UserRelationshipStatusEnum.fromJson(json[r'relationshipStatus']),
      );
    }
    return null;
  }

  static List<GetUserByUserIdReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <GetUserByUserIdReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = GetUserByUserIdReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, GetUserByUserIdReturn> mapFromJson(dynamic json) {
    final map = <String, GetUserByUserIdReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = GetUserByUserIdReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of GetUserByUserIdReturn-objects as value to a dart map
  static Map<String, List<GetUserByUserIdReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<GetUserByUserIdReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = GetUserByUserIdReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'userId',
    'userRole',
    'username',
    'fullname',
    'iventCount',
    'friendCount',
    'followerCount',
    'hobbies',
    'isFollowing',
    'isFirstPerson',
  };
}

