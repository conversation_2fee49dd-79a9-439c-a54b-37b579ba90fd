# openapi.model.SearchCollabsReturn

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**collabs** | [**List<CollabratorListItem>**](CollabratorListItem.md) | List of collaborators with their membership and friendship status | [default to const []]
**collabCount** | **int** | Total number of collaborators found | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



