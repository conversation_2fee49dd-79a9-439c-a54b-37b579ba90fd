//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class SearchBoxCategoryListReturn {
  /// Returns a new [SearchBoxCategoryListReturn] instance.
  SearchBoxCategoryListReturn({
    this.listItems = const [],
    required this.attribution,
    required this.version,
  });

  /// List of available categories
  List<SearchBoxCategoryItem> listItems;

  /// Attribution text
  String attribution;

  /// Version of the API
  String version;

  @override
  bool operator ==(Object other) => identical(this, other) || other is SearchBoxCategoryListReturn &&
    _deepEquality.equals(other.listItems, listItems) &&
    other.attribution == attribution &&
    other.version == version;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (listItems.hashCode) +
    (attribution.hashCode) +
    (version.hashCode);

  @override
  String toString() => 'SearchBoxCategoryListReturn[listItems=$listItems, attribution=$attribution, version=$version]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'list_items'] = this.listItems;
      json[r'attribution'] = this.attribution;
      json[r'version'] = this.version;
    return json;
  }

  /// Returns a new [SearchBoxCategoryListReturn] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static SearchBoxCategoryListReturn? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "SearchBoxCategoryListReturn[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "SearchBoxCategoryListReturn[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return SearchBoxCategoryListReturn(
        listItems: SearchBoxCategoryItem.listFromJson(json[r'list_items']),
        attribution: mapValueOfType<String>(json, r'attribution')!,
        version: mapValueOfType<String>(json, r'version')!,
      );
    }
    return null;
  }

  static List<SearchBoxCategoryListReturn> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <SearchBoxCategoryListReturn>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = SearchBoxCategoryListReturn.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, SearchBoxCategoryListReturn> mapFromJson(dynamic json) {
    final map = <String, SearchBoxCategoryListReturn>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = SearchBoxCategoryListReturn.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of SearchBoxCategoryListReturn-objects as value to a dart map
  static Map<String, List<SearchBoxCategoryListReturn>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<SearchBoxCategoryListReturn>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = SearchBoxCategoryListReturn.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'list_items',
    'attribution',
    'version',
  };
}

