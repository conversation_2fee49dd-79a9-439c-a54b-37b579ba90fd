# openapi.model.CreatePageDto

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**pageName** | **String** | Name of the page | 
**thumbnailUrl** | **String** | URL to the page thumbnail image | [optional] 
**websiteUrl** | **String** | Website URL for the page | [optional] 
**description** | **String** | Description of the page | [optional] 
**isEdu** | **bool** | Whether this page is educational | 
**haveMembership** | **bool** | Whether this page has membership functionality | 
**tagIds** | **List<String>** | Array of hobby tag UUIDs associated with the page | [default to const []]
**creatorIds** | **List<String>** | Array of creator user UUIDs for the page | [default to const []]
**locationId** | **String** | UUID of the location where the page is based | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)



