import 'package:get/get.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

class AuthStateManager extends GetxController {
  final _phoneNumber = ''.obs;
  final _isLoading = false.obs;
  final _errorMessage = ''.obs;
  
  String get phoneNumber => _phoneNumber.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  
  String get formattedPhoneNumber {
    if (phoneNumber.length != AuthValidationConstants.phoneNumberMaxLength) {
      return '';
    }
    return _formatPhoneNumber(phoneNumber);
  }
  bool get isPhoneNumberValid => phoneNumber.length == AuthValidationConstants.phoneNumberMaxLength;

  set phoneNumber(String value) => _phoneNumber.value = value;
  set isLoading(bool value) => _isLoading.value = value;
  set errorMessage(String value) => _errorMessage.value = value;

  void clearError() => _errorMessage.value = '';

  void resetState() {
    _phoneNumber.value = '';
    _isLoading.value = false;
    _errorMessage.value = '';
  }

  String _formatPhoneNumber(String value) {
    if (value.length != AuthValidationConstants.phoneNumberMaxLength) {
      return value;
    }

    final String areaCode = value.substring(0, 3);
    final String remainingNumber = value.substring(3);
    return '${AuthValidationConstants.turkeyCountryCode}($areaCode)$remainingNumber';
    // return '+31$areaCode$remainingNumber';
  }
}
